# Python 3.9 Otomatik Kurulum Scripti
Write-Host "Python 3.9.18 indiriliyor ve kuruluyor..."

# Python 3.9.18 indirme linki
$pythonUrl = "https://www.python.org/ftp/python/3.9.18/python-3.9.18-amd64.exe"
$installerPath = "$env:TEMP\python-3.9.18-amd64.exe"

try {
    # Python installer'ı indir
    Write-Host "Python installer indiriliyor..."
    Invoke-WebRequest -Uri $pythonUrl -OutFile $installerPath -UseBasicParsing
    
    if (Test-Path $installerPath) {
        Write-Host "Indirme tamamlandi. Kurulum baslatiliyor..."
        
        # Python'u sessiz modda kur
        # /quiet = sessiz kurulum
        # InstallAllUsers=1 = tüm kullanıcılar için kur
        # PrependPath=1 = PATH'e ekle
        # Include_launcher=1 = py launcher'ı dahil et
        $installArgs = "/quiet InstallAllUsers=1 PrependPath=1 Include_launcher=1"
        
        Start-Process -FilePath $installerPath -ArgumentList $installArgs -Wait
        
        Write-Host "Python 3.9 kurulumu tamamlandi!"
        Write-Host "Lutfen PowerShell'i yeniden baslatip tekrar deneyin."
        
        # Installer dosyasını sil
        Remove-Item $installerPath -Force
        
    } else {
        Write-Host "HATA: Installer dosyasi indirilemedi!"
        exit 1
    }
}
catch {
    Write-Host "HATA: Python kurulumu sirasinda hata olustu:"
    Write-Host $_.Exception.Message
    exit 1
}

Write-Host "Kurulum tamamlandi. Lutfen PowerShell'i kapatip yeniden acin."
Pause
