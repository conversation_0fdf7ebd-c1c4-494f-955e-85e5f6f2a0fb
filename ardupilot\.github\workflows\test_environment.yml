name: test environment setup
on:
  schedule:
    - cron: '0 0 * * 6'  # every saturday at midnight
  workflow_dispatch:
  push:
    paths:
      - '.github/workflows/test_environment.yml'
      - 'Tools/environment_install/**'

  pull_request:
    paths:
      - '.github/workflows/test_environment.yml'
      - 'Tools/environment_install/**'


concurrency:
  group: ci-${{github.workflow}}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  build:
    runs-on: ubuntu-22.04
    container:
      image: ${{matrix.os}}:${{matrix.name}}
      options: --privileged
    strategy:
      fail-fast: false  # don't cancel if a job from the matrix fails
      matrix:
        include:
          - os: ubuntu
            name: focal
          - os: ubuntu
            name: jammy
          - os: ubuntu
            name: noble
          - os: archlinux
            name: latest
          - os: debian
            name: bookworm
          - os: debian
            name: bullseye
    steps:
      - name: Install Git
        timeout-minutes: 30
        env:
          DEBIAN_FRONTEND: noninteractive
          TZ: Europe/Paris
        shell: 'script -q -e -c "bash {0}"'
        run: |
          case ${{matrix.os}} in
          *"ubuntu"*)
          apt-get update && apt-get install --no-install-recommends -qy \
          lsb-release \
          sudo \
          git \
          software-properties-common
          add-apt-repository ppa:git-core/ppa -y
          apt-get update && apt-get install --no-install-recommends -qy git
          ;;
          *"debian"*)
          apt-get update && apt-get install --no-install-recommends -qy \
          lsb-release \
          sudo \
          git \
          software-properties-common
          ;;
          *"archlinux"*)
          pacman -Syu --noconfirm --needed git sudo
          ;;
          esac

      # git checkout the PR
      - uses: actions/checkout@v4
        with:
            submodules: 'recursive'
      - name: test install environment ${{matrix.os}}.${{matrix.name}}
        timeout-minutes: 60
        env:
          DISABLE_MAVNATIVE: True
          DEBIAN_FRONTEND: noninteractive
          TZ: Europe/Paris
          SKIP_AP_GIT_CHECK: 1
        shell: 'script -q -e -c "bash {0}"'
        run: |
          PATH="/github/home/<USER>/bin:$PATH"
          ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone
          sed -i 's/\$EUID/\$ID/' Tools/environment_install/install-prereqs-ubuntu.sh
          sed -i 's/sudo usermod/\#sudo usermod/' Tools/environment_install/install-prereqs-ubuntu.sh
          sed -i 's/sudo usermod/\#sudo usermod/' Tools/environment_install/install-prereqs-arch.sh
          case ${{matrix.os}} in
            *"ubuntu"*)
            echo 'debconf debconf/frontend select Noninteractive' | debconf-set-selections
            Tools/environment_install/install-prereqs-ubuntu.sh -qy
            ;;
            *"debian"*)
            Tools/environment_install/install-prereqs-ubuntu.sh -qy
            ;;
            *"archlinux"*)
            cp /etc/skel/.bashrc /root
            cp /etc/skel/.bashrc /github/home
            git config --global --add safe.directory ${GITHUB_WORKSPACE}
            Tools/environment_install/install-prereqs-arch.sh -qy
            ;;
          esac

      - name: test build STIL ${{matrix.os}}.${{matrix.name}}
        env:
          DISABLE_MAVNATIVE: True
          DEBIAN_FRONTEND: noninteractive
          TZ: Europe/Paris
        shell: 'script -q -e -c "bash {0}"'
        run: |
          git config --global --add safe.directory ${GITHUB_WORKSPACE}
          source ~/.bashrc
          source $HOME/venv-ardupilot/bin/activate || true
          git config --global --add safe.directory /__w/ardupilot/ardupilot
          ./waf configure
          ./waf rover

      - name: test build Chibios ${{matrix.os}}.${{matrix.name}}
        if: matrix.os != 'alpine'
        env:
          DISABLE_MAVNATIVE: True
          DEBIAN_FRONTEND: noninteractive
          TZ: Europe/Paris
        shell: 'script -q -e -c "bash {0}"'
        run: |
          git config --global --add safe.directory ${GITHUB_WORKSPACE}
          source ~/.bashrc
          source $HOME/venv-ardupilot/bin/activate || true
          case ${{matrix.os}} in
            *"archlinux"*)
              export PATH=/opt/gcc-arm-none-eabi-10-2020-q4-major/bin:$PATH
              export PATH=/__w/ardupilot/ardupilot/ardupilot/Tools/autotest:$PATH
              ;;
          esac
          git config --global --add safe.directory /__w/ardupilot/ardupilot
          ./waf configure --board CubeOrange
          ./waf plane

  build-alpine:  # special case for alpine as it doesn't have bash by default
    runs-on: ubuntu-22.04
    container:
      image: alpine:latest
      options: --privileged
    steps:
      - name: Install Git
        timeout-minutes: 30
        env:
          DEBIAN_FRONTEND: noninteractive
          TZ: Europe/Paris
        run: |
          apk update && apk add --no-cache git
      - uses: actions/checkout@v4
        with:
          submodules: 'recursive'
      - name: test install environment alpine
        timeout-minutes: 60
        env:
          DISABLE_MAVNATIVE: True
          TZ: Europe/Paris
          SKIP_AP_GIT_CHECK: 1
        run: |
          PATH="/github/home/<USER>/bin:$PATH"
          Tools/environment_install/install-prereqs-alpine.sh
      - name: test build STIL alpine
        env:
          DISABLE_MAVNATIVE: True
          TZ: Europe/Paris
        run: |
          git config --global --add safe.directory ${GITHUB_WORKSPACE}
          git config --global --add safe.directory /__w/ardupilot/ardupilot
          ./waf configure
          ./waf rover