name: colcon build/test

on:
  push:
    paths-ignore:
      # remove not tests vehicles
      - 'AntennaTracker/**'
      - 'ArduSub/**'
      - 'Blimp/**'
      - 'Rover/**'
      # remove esp32 HAL
      - 'libraries/AP_HAL_ESP32/**'
      # remove non SITL directories
      - 'Tools/AP_Bootloader/**'
      - 'Tools/bootloaders/**'
      - 'Tools/CHDK-Script/**'
      - 'Tools/CodeStyle/**'
      - 'Tools/completion/**'
      - 'Tools/CPUInfo/**'
      - 'Tools/debug/**'
      - 'Tools/environment_install/**'
      - 'Tools/FilterTestTool/**'
      - 'Tools/Frame_params/**'
      - 'Tools/geotag/**'
      - 'Tools/GIT_Test/**'
      - 'Tools/gittools/**'
      - 'Tools/Hello/**'
      - 'Tools/IO_Firmware/**'
      - 'Tools/mavproxy_modules/**'
      - 'Tools/Pozyx/**'
      - 'Tools/PrintVersion.py'
      - 'Tools/Replay/**'
      - 'Tools/simulink/**'
      - 'Tools/UDP_Proxy/**'
      - 'Tools/vagrant/**'
      - 'Tools/Vicon/**'
      # Discard python file from Tools/scripts as not used
      - 'Tools/scripts/**.py'
      - 'Tools/scripts/build_sizes/**'
      - 'Tools/scripts/build_tests/**'
      - 'Tools/scripts/CAN/**'
      - 'Tools/scripts/signing/**'
      # Remove autotests stuff
      - 'Tools/autotest/**'
      # Remove markdown files as irrelevant
      - '**.md'
      # Remove dotfile at root directory
      - './.dir-locals.el'
      - './.dockerignore'
      - './.editorconfig'
      - './.flake8'
      - './.gitattributes'
      - './.github'
      - './.gitignore'
      - './.pre-commit-config.yaml'
      - './.pydevproject'
      - './.valgrind-suppressions'
      - './.valgrindrc'
      - 'Dockerfile'
      - 'Vagrantfile'
      - 'Makefile'
      # Remove some directories check
      - '.vscode/**'
      - '.github/ISSUE_TEMPLATE/**'
      # Remove change on other workflows
      - '.github/workflows/test_environment.yml'

  pull_request:
    paths-ignore:
      # remove not tests vehicles
      - 'AntennaTracker/**'
      - 'ArduSub/**'
      - 'Rover/**'
      - 'Blimp/**'
      # remove esp32 HAL
      - 'libraries/AP_HAL_ESP32/**'
      # remove non SITL directories
      - 'Tools/AP_Bootloader/**'
      - 'Tools/CHDK-Script/**'
      - 'Tools/CPUInfo/**'
      - 'Tools/CodeStyle/**'
      - 'Tools/FilterTestTool/**'
      - 'Tools/Frame_params/**'
      - 'Tools/GIT_Test/**'
      - 'Tools/Hello/**'
      - 'Tools/IO_Firmware/**'
      - 'Tools/LogAnalyzer/**'
      - 'Tools/Pozyx/**'
      - 'Tools/PrintVersion.py'
      - 'Tools/Replay/**'
      - 'Tools/UDP_Proxy/**'
      - 'Tools/Vicon/**'
      - 'Tools/bootloaders/**'
      - 'Tools/completion/**'
      - 'Tools/debug/**'
      - 'Tools/environment_install/**'
      - 'Tools/geotag/**'
      - 'Tools/gittools/**'
      - 'Tools/mavproxy_modules/**'
      - 'Tools/simulink/**'
      - 'Tools/vagrant/**'
      # Discard python file from Tools/scripts as not used
      - 'Tools/scripts/**.py'
      - 'Tools/scripts/build_sizes/**'
      - 'Tools/scripts/build_tests/**'
      - 'Tools/scripts/CAN/**'
      - 'Tools/scripts/signing/**'
      # Remove autotests stuff
      - 'Tools/autotest/**'
      # Remove markdown files as irrelevant
      - '**.md'
      # Remove dotfile at root directory
      - './.dir-locals.el'
      - './.dockerignore'
      - './.editorconfig'
      - './.flake8'
      - './.gitattributes'
      - './.github'
      - './.gitignore'
      - './.pre-commit-config.yaml'
      - './.pydevproject'
      - './.valgrind-suppressions'
      - './.valgrindrc'
      - 'Dockerfile'
      - 'Vagrantfile'
      - 'Makefile'
      # Remove some directories check
      - '.vscode/**'
      - '.github/ISSUE_TEMPLATE/**'
      # Remove change on other workflows
      - '.github/workflows/test_environment.yml'

  workflow_dispatch:

concurrency:
  group: ci-${{github.workflow}}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  build-test:
    runs-on: ubuntu-22.04
    container:
      image: ardupilot/ardupilot-dev-ros:latest
    strategy:
      fail-fast: false  # don't cancel if a job from the matrix fails
    steps:
      # git checkout the PR
      - uses: actions/checkout@v4
        with:
          submodules: 'recursive'
          path: src/ardupilot
      # Put ccache into github cache for faster build
      - name: Prepare ccache timestamp
        id: ccache_cache_timestamp
        run: |
          NOW=$(date -u +"%F-%T")
          echo "timestamp=${NOW}" >> $GITHUB_OUTPUT
      - name: ccache cache files
        uses: actions/cache@v4
        with:
          path: ~/.ccache
          key: ${{github.workflow}}-ccache-${{steps.ccache_cache_timestamp.outputs.timestamp}}
          restore-keys: ${{github.workflow}}-ccache-  # restore ccache from either previous build on this branch or on master
      - name: setup ccache
        run: |
          . src/ardupilot/.github/workflows/ccache.env
      # https://ardupilot.org/dev/docs/ros2.html#installation-ubuntu
      - name: Pull in repos with vcs
        run: |
          cd src
          wget --progress=dot:giga https://raw.githubusercontent.com/ArduPilot/ardupilot/master/Tools/ros2/ros2.repos
          vcs import --recursive --debug --shallow --skip-existing < ros2.repos
      - name: Install rosdep dependencies
        shell: 'bash'
        run: |
          apt update
          rosdep update
          # Workaround for flake8 attribute error
          # https://github.com/ArduPilot/ardupilot/pull/24277#issuecomment-1632833433
          python3 -m pip install flake8==3.7.9
          source /opt/ros/humble/setup.bash
          rosdep install --from-paths src --ignore-src --default-yes
      - name: Install MAVProxy
        run: |
          # Install this specific version just to prevent rolling updates.
          # These are the latest available
          python3 -m pip install MAVProxy==1.8.67
      - name: Install local pymavlink
        working-directory: ./src/ardupilot/modules/mavlink/pymavlink
        run: |
          python3 -m pip install . -U --user
      - name: Build with colcon
        shell: 'bash'
        run: |
          source /opt/ros/humble/setup.bash
          colcon build --packages-up-to ardupilot_dds_tests --cmake-args -DBUILD_TESTING=ON
      - name: Test with colcon
        shell: 'bash'
        run: |
          source install/setup.bash
          colcon test --executor sequential --parallel-workers 0 --base-paths src/ardupilot --event-handlers=console_cohesion+
      - name: Report colcon test results
        run: |
          colcon test-result --all --verbose

