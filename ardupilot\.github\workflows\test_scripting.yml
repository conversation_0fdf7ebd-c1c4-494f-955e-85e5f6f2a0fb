name: test scripting

on:
  push:
    paths: # only run for scripting changes
      - 'libraries/AP_Scripting/tests/docs_check.py'
      - 'libraries/AP_Scripting/generator/**'
      - '**.lua'

  pull_request:
    paths: # only run for scripting changes
      - 'libraries/AP_Scripting/tests/docs_check.py'
      - 'libraries/AP_Scripting/generator/**'
      - '**.lua'

  workflow_dispatch:

concurrency:
  group: ci-${{github.workflow}}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  test-scripting:
    runs-on: ubuntu-22.04
    container: ardupilot/ardupilot-dev-base:v0.1.3
    steps:
      # git checkout the PR
      - uses: actions/checkout@v4
        with:
          submodules: 'recursive'

      - name: Register lua check problem matcher
        run: |
          echo "::add-matcher::.github/problem-matchers/Lua.json"
          echo "::remove-matcher owner=Lua-language-server-problem-matcher::"

      - name: <PERSON><PERSON>
        shell: bash
        run: |
          sudo apt-get update
          sudo apt-get -y install lua-check
          ./Tools/scripts/run_luacheck.sh

      - name: Register lua language server problem matcher
        run: |
          echo "::add-matcher::.github/problem-matchers/Lua.json"
          echo "::remove-matcher owner=Luacheck-problem-matcher::"

      - name: Language server check
        shell: bash
        run: |
          python3 -m pip install github-release-downloader
          python3 ./Tools/scripts/run_lua_language_check.py

      - name: Remove problem matchers
        run: |
          echo "::remove-matcher owner=Luacheck-problem-matcher::"
          echo "::remove-matcher owner=Lua-language-server-problem-matcher::"

      - name: Generate docs md
        shell: bash
        run: |
          ./Tools/scripts/generate_lua_docs.sh

      - name: copy docs
        run: |
          PATH="/github/home/<USER>/bin:$PATH"
          mv "libraries/AP_Scripting/docs/docs.lua" "libraries/AP_Scripting/docs/current_docs.lua"

      - name: build sitl # we don't really need to build the full code, just trigger docs re-gen with --scripting-docs, timeout after 10 seconds
        shell: bash
        run: |
          git config --global --add safe.directory ${GITHUB_WORKSPACE}
          PATH="/github/home/<USER>/bin:$PATH"
          ./waf configure --board sitl
          timeout 10 ./waf antennatracker --scripting-docs || code=$?; if [[ $code -ne 124 && $code -ne 0 ]]; then exit $code; fi

      - name: run compare
        run: |
          PATH="/github/home/<USER>/bin:$PATH"
          python ./libraries/AP_Scripting/tests/docs_check.py "./libraries/AP_Scripting/docs/docs.lua" "./libraries/AP_Scripting/docs/current_docs.lua"

      - name: Upload docs
        uses: actions/upload-artifact@v4
        with:
          name: Docs
          path: ScriptingDocs.md
          retention-days: 7
