name: test dds

on:
  push:
    paths-ignore:
      # remove not tests vehicles
      - 'AntennaTracker/**'
      - 'ArduSub/**'
      - 'Blimp/**'
      - 'Rover/**'
      # remove esp32 HAL
      - 'libraries/AP_HAL_ESP32/**'
      # remove non SITL directories
      - 'Tools/AP_Bootloader/**'
      - 'Tools/bootloaders/**'
      - 'Tools/CHDK-Script/**'
      - 'Tools/CodeStyle/**'
      - 'Tools/completion/**'
      - 'Tools/CPUInfo/**'
      - 'Tools/debug/**'
      - 'Tools/environment_install/**'
      - 'Tools/FilterTestTool/**'
      - 'Tools/Frame_params/**'
      - 'Tools/geotag/**'
      - 'Tools/GIT_Test/**'
      - 'Tools/gittools/**'
      - 'Tools/Hello/**'
      - 'Tools/IO_Firmware/**'
      - 'Tools/mavproxy_modules/**'
      - 'Tools/Pozyx/**'
      - 'Tools/PrintVersion.py'
      - 'Tools/Replay/**'
      - 'Tools/simulink/**'
      - 'Tools/UDP_Proxy/**'
      - 'Tools/vagrant/**'
      - 'Tools/Vicon/**'
      # Discard python file from Tools/scripts as not used
      - 'Tools/scripts/**.py'
      - 'Tools/scripts/build_sizes/**'
      - 'Tools/scripts/build_tests/**'
      - 'Tools/scripts/CAN/**'
      - 'Tools/scripts/signing/**'
      # Remove autotests stuff
      - 'Tools/autotest/**'
      # Remove markdown files as irrelevant
      - '**.md'
      # Remove dotfile at root directory
      - './.dir-locals.el'
      - './.dockerignore'
      - './.editorconfig'
      - './.flake8'
      - './.gitattributes'
      - './.github'
      - './.gitignore'
      - './.pre-commit-config.yaml'
      - './.pydevproject'
      - './.valgrind-suppressions'
      - './.valgrindrc'
      - 'Dockerfile'
      - 'Vagrantfile'
      - 'Makefile'
      # Remove some directories check
      - '.vscode/**'
      - '.github/ISSUE_TEMPLATE/**'
      # Remove change on other workflows
      - '.github/workflows/test_environment.yml'

  pull_request:
    paths-ignore:
      # remove not tests vehicles
      - 'AntennaTracker/**'
      - 'ArduSub/**'
      - 'Rover/**'
      - 'Blimp/**'
      # remove esp32 HAL
      - 'libraries/AP_HAL_ESP32/**'
      # remove non SITL directories
      - 'Tools/AP_Bootloader/**'
      - 'Tools/CHDK-Script/**'
      - 'Tools/CPUInfo/**'
      - 'Tools/CodeStyle/**'
      - 'Tools/FilterTestTool/**'
      - 'Tools/Frame_params/**'
      - 'Tools/GIT_Test/**'
      - 'Tools/Hello/**'
      - 'Tools/IO_Firmware/**'
      - 'Tools/LogAnalyzer/**'
      - 'Tools/Pozyx/**'
      - 'Tools/PrintVersion.py'
      - 'Tools/Replay/**'
      - 'Tools/UDP_Proxy/**'
      - 'Tools/Vicon/**'
      - 'Tools/bootloaders/**'
      - 'Tools/completion/**'
      - 'Tools/debug/**'
      - 'Tools/environment_install/**'
      - 'Tools/geotag/**'
      - 'Tools/gittools/**'
      - 'Tools/mavproxy_modules/**'
      - 'Tools/simulink/**'
      - 'Tools/vagrant/**'
      # Discard python file from Tools/scripts as not used
      - 'Tools/scripts/**.py'
      - 'Tools/scripts/build_sizes/**'
      - 'Tools/scripts/build_tests/**'
      - 'Tools/scripts/CAN/**'
      - 'Tools/scripts/signing/**'
      # Remove autotests stuff
      - 'Tools/autotest/**'
      # Remove markdown files as irrelevant
      - '**.md'
      # Remove dotfile at root directory
      - './.dir-locals.el'
      - './.dockerignore'
      - './.editorconfig'
      - './.flake8'
      - './.gitattributes'
      - './.github'
      - './.gitignore'
      - './.pre-commit-config.yaml'
      - './.pydevproject'
      - './.valgrind-suppressions'
      - './.valgrindrc'
      - 'Dockerfile'
      - 'Vagrantfile'
      - 'Makefile'
      # Remove some directories check
      - '.vscode/**'
      - '.github/ISSUE_TEMPLATE/**'
      # Remove change on other workflows
      - '.github/workflows/test_environment.yml'

  workflow_dispatch:

concurrency:
  group: ci-${{github.workflow}}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  build:
    runs-on: ubuntu-22.04
    container:
      image: ardupilot/ardupilot-dev-ros:latest
      options: --user 1001
    strategy:
      fail-fast: false  # don't cancel if a job from the matrix fails
      matrix:
        config: [
            sitl,
            stm32h7
        ]
    steps:
      # git checkout the PR
      - uses: actions/checkout@v4
        with:
          submodules: 'recursive'
      # Put ccache into github cache for faster build
      - name: Prepare ccache timestamp
        id: ccache_cache_timestamp
        run: |
          NOW=$(date -u +"%F-%T")
          echo "timestamp=${NOW}" >> $GITHUB_OUTPUT
      - name: ccache cache files
        uses: actions/cache@v4
        with:
          path: ~/.ccache
          key: ${{github.workflow}}-ccache-${{ matrix.config }}-${{steps.ccache_cache_timestamp.outputs.timestamp}}
          restore-keys: ${{github.workflow}}-ccache-${{ matrix.config }}-  # restore ccache from either previous build on this branch or on master
      - name: setup ccache
        run: |
          . .github/workflows/ccache.env
      - name: test ${{matrix.config}}
        env:
          CI_BUILD_TARGET: dds-${{matrix.config}}
        shell: 'script -q -e -c "bash {0}"'
        run: |
          git config --global --add safe.directory ${GITHUB_WORKSPACE}
          PATH="/github/home/<USER>/bin:$PATH"
          Tools/scripts/build_ci.sh

      - name: Archive buildlog artifacts
        uses: actions/upload-artifact@v4
        if: failure()
        with:
           name: fail-${{matrix.config}}
           path: /tmp/buildlogs
           retention-days: 14
