name: Cygwin Build

on:
  push:
    paths-ignore:
      # remove other vehicles
      - 'AntennaTracker/**'
      - 'Blimp/**'
      # remove non SITL HAL
      - 'libraries/AP_HAL_ChibiOS/**'
      - 'libraries/AP_HAL_ESP32/**'
      - 'libraries/AP_HAL_QURT/**'
      # remove non SITL directories
      - 'Tools/AP_Bootloader/**'
      - 'Tools/AP_Periph/**'
      - 'Tools/CHDK-Script/**'
      - 'Tools/CPUInfo/**'
      - 'Tools/CodeStyle/**'
      - 'Tools/FilterTestTool/**'
      - 'Tools/Frame_params/**'
      - 'Tools/GIT_Test/**'
      - 'Tools/Hello/**'
      - 'Tools/IO_Firmware/**'
      - 'Tools/Linux_HAL_Essentials/**'
      - 'Tools/Pozyx/**'
      - 'Tools/PrintVersion.py'
      - 'Tools/Replay/**'
      - 'Tools/UDP_Proxy/**'
      - 'Tools/Vicon/**'
      - 'Tools/bootloaders/**'
      - 'Tools/completion/**'
      - 'Tools/debug/**'
      - 'Tools/environment_install/**'
      - 'Tools/geotag/**'
      - 'Tools/gittools/**'
      - 'Tools/mavproxy_modules/**'
      - 'Tools/simulink/**'
      - 'Tools/vagrant/**'
      # Discard python file from Tools/scripts as not used
      - 'Tools/scripts/**.py'
      - 'Tools/scripts/build_sizes/**'
      - 'Tools/scripts/build_tests/**'
      - 'Tools/scripts/CAN/**'
      - 'Tools/scripts/signing/**'
      # Remove autotest
      - 'Tools/autotest/**'
      # Remove markdown files as irrelevant
      - '**.md'
      # Remove dotfile at root directory
      - './.dir-locals.el'
      - './.dockerignore'
      - './.editorconfig'
      - './.flake8'
      - './.gitattributes'
      - './.github'
      - './.gitignore'
      - './.pre-commit-config.yaml'
      - './.pydevproject'
      - './.valgrind-suppressions'
      - './.valgrindrc'
      - 'Dockerfile'
      - 'Vagrantfile'
      - 'Makefile'
      # Remove some directories check
      - '.vscode/**'
      - '.github/ISSUE_TEMPLATE/**'
      # Remove change on other workflows
      - '.github/workflows/test_environment.yml'

  pull_request:
    paths-ignore:
      # remove other vehicles
      - 'AntennaTracker/**'
      - 'Blimp/**'
      # remove non SITL HAL
      - 'libraries/AP_HAL_ChibiOS/**'
      - 'libraries/AP_HAL_ESP32/**'
      - 'libraries/AP_HAL_QURT/**'
      # remove non SITL directories
      - 'Tools/AP_Bootloader/**'
      - 'Tools/AP_Periph/**'
      - 'Tools/bootloaders/**'
      - 'Tools/CHDK-Script/**'
      - 'Tools/CodeStyle/**'
      - 'Tools/completion/**'
      - 'Tools/CPUInfo/**'
      - 'Tools/debug/**'
      - 'Tools/environment_install/**'
      - 'Tools/FilterTestTool/**'
      - 'Tools/Frame_params/**'
      - 'Tools/geotag/**'
      - 'Tools/GIT_Test/**'
      - 'Tools/gittools/**'
      - 'Tools/Hello/**'
      - 'Tools/IO_Firmware/**'
      - 'Tools/Linux_HAL_Essentials/**'
      - 'Tools/LogAnalyzer/**'
      - 'Tools/mavproxy_modules/**'
      - 'Tools/Pozyx/**'
      - 'Tools/PrintVersion.py'
      - 'Tools/Replay/**'
      - 'Tools/simulink/**'
      - 'Tools/UDP_Proxy/**'
      - 'Tools/vagrant/**'
      - 'Tools/Vicon/**'
      # Discard python file from Tools/scripts as not used
      - 'Tools/scripts/**.py'
      - 'Tools/scripts/build_sizes/**'
      - 'Tools/scripts/build_tests/**'
      - 'Tools/scripts/CAN/**'
      - 'Tools/scripts/signing/**'
      # Remove autotest
      - 'Tools/autotest/**'
      # Remove markdown files as irrelevant
      - '**.md'
      # Remove dotfile at root directory
      - './.dir-locals.el'
      - './.dockerignore'
      - './.editorconfig'
      - './.flake8'
      - './.gitattributes'
      - './.github'
      - './.gitignore'
      - './.pre-commit-config.yaml'
      - './.pydevproject'
      - './.valgrind-suppressions'
      - './.valgrindrc'
      - 'Dockerfile'
      - 'Vagrantfile'
      - 'Makefile'
      # Remove some directories check
      - '.vscode/**'
      - '.github/ISSUE_TEMPLATE/**'
      # Remove change on other workflows
      - '.github/workflows/test_environment.yml'

  workflow_dispatch:

concurrency:
  group: ci-${{github.workflow}}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  build:
    runs-on: 'windows-latest'

    steps:
      - uses: actions/checkout@v4
        with:
          submodules: 'recursive'
      - name: Prepare ccache timestamp
        id: ccache_cache_timestamp
        shell: bash
        run: |
          NOW=$(date -u +"%F-%T")
          echo "timestamp=${NOW}" >> $GITHUB_OUTPUT
          WORKFLOWNAME="${{github.workflow}}"
          NAME_DASHED=${WORKFLOWNAME//+( )/_}
          echo "cache-key=${NAME_DASHED}" >> $GITHUB_OUTPUT

      - uses: cygwin/cygwin-install-action@v5
        with:
          packages: cygwin64 gcc-g++=10.2.0-1 ccache python37 python37-future python37-lxml python37-pip python37-setuptools python37-wheel git procps gettext
          add-to-path: false
      # Put ccache into github cache for faster build
      - name: setup ccache
        env:
          PATH: /usr/bin:$(cygpath ${SYSTEMROOT})/system32
        shell: C:\cygwin\bin\bash.exe -eo pipefail '{0}'
        run: >-
          mkdir -p /cygdrive/d/a/ardupilot/ardupilot/ccache &&
          mkdir -p /usr/local/etc &&
          echo "export CCACHE_SLOPPINESS=file_stat_matches" >> ~/ccache.conf &&
          echo "export CCACHE_DIR=/cygdrive/d/a/ardupilot/ardupilot/ccache" >> ~/ccache.conf &&
          echo "export CCACHE_BASEDIR=/cygdrive/d/a/ardupilot/ardupilot" >> ~/ccache.conf &&
          echo "export CCACHE_COMPRESS=1" >> ~/ccache.conf &&
          echo "export CCACHE_COMPRESSLEVEL=6" >> ~/ccache.conf &&
          echo "export CCACHE_MAXSIZE=400M" >> ~/ccache.conf &&
          source ~/ccache.conf &&
          ccache -s
      - name: ccache cache files
        uses: actions/cache@v4
        with:
          path: D:/a/ardupilot/ardupilot/ccache
          key: ${{ steps.ccache_cache_timestamp.outputs.cache-key }}-ccache-${{steps.ccache_cache_timestamp.outputs.timestamp}}
          restore-keys: ${{ steps.ccache_cache_timestamp.outputs.cache-key }}-ccache-  # restore ccache from either previous build on this branch or on base branch
      - name: Prepare Python environment
        env:
          PATH: /usr/bin:$(cygpath ${SYSTEMROOT})/system32
        shell: C:\cygwin\bin\bash.exe -eo pipefail '{0}'
        run: >-
          python3 -m pip install --progress-bar off empy==3.3.4 pexpect &&
          python3 -m pip install --progress-bar off dronecan --upgrade &&
          cp /usr/bin/ccache /usr/local/bin/ &&
          cd /usr/local/bin && ln -s ccache /usr/local/bin/gcc &&
          ln -s ccache /usr/local/bin/g++ &&
          ln -s ccache /usr/local/bin/x86_64-pc-cygwin-gcc &&
          ln -s ccache /usr/local/bin/x86_64-pc-cygwin-g++

      - name: Build SITL
        env:
          PATH: /usr/bin:$(cygpath ${SYSTEMROOT})/system32
        shell: C:\cygwin\bin\bash.exe -eo pipefail '{0}'
        run: >-
          git config --global --add safe.directory /cygdrive/d/a/${GITHUB_REPOSITORY#$GITHUB_REPOSITORY_OWNER/}/${GITHUB_REPOSITORY#$GITHUB_REPOSITORY_OWNER/} &&
          export PATH=/usr/local/bin:/usr/bin:$(cygpath ${SYSTEMROOT})/system32 &&
          source ~/ccache.conf &&
          Tools/scripts/cygwin_build.sh &&
          ccache -s

      - name: Check build files
        id: check_files
        uses: andstor/file-existence-action@v2
        with:
          files: "artifacts/*"
          fail: true

      - name: Archive build
        uses: actions/upload-artifact@v4
        with:
          name: binaries
          path: artifacts
          retention-days: 7
