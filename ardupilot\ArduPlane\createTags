#!/usr/bin/env bash
#" Autocompletion enabled vim for arduino pde's

ctags -RV --language-force=C++ --c++-kinds=+p --fields=+iaS --extra=+q  \
	. \
	../libraries/*

# sample vimrc file
# you'll need to have omnicpp plugin for vim

#"set compatible

#" Vim5 and later versions support syntax highlighting. Uncommenting the next
#" line enables syntax highlighting by default.
#syntax on
#filetype on

#" If using a dark background within the editing area and syntax highlighting
#" turn on this option as well
#"set background=dark

#" Uncomment the following to have V<PERSON> jump to the last position when
#" reopening a file
#if has("autocmd")
  #au BufReadPost * if line("'\"") > 1 && line("'\"") <= line("$") | exe "normal! g'\"" | endif
#endif

#" Uncomment the following to have Vim load indentation rules and plugins
#" according to the detected filetype.
#if has("autocmd")
  #filetype plugin indent on
#endif

#" The following are commented out as they cause vim to behave a lot
#" differently from regular Vi. They are highly recommended though.
#set showcmd		" Show (partial) command in status line.
#set showmatch		" Show matching brackets.
#set ignorecase		" Do case insensitive matching
#set smartcase		" Do smart case matching
#set incsearch		" Incremental search
#set autowrite		" Automatically save before commands like :next and :make
#set hidden             " Hide buffers when they are abandoned
#set mouse=a		" Enable mouse usage (all modes)
#set vb

#" build tags of your own project with Ctrl-F12
#map <C-F12> :!ctags -R --sort=yes --c++-kinds=+p --fields=+iaS --extra=+q .<CR>

#" OmniCppComplete
#let OmniCpp_NamespaceSearch = 1
#let OmniCpp_GlobalScopeSearch = 1
#let OmniCpp_ShowAccess = 1
#let OmniCpp_ShowPrototypeInAbbr = 1 " show function parameters
#let OmniCpp_MayCompleteDot = 1 " autocomplete after .
#let OmniCpp_MayCompleteArrow = 1 " autocomplete after ->
#let OmniCpp_MayCompleteScope = 1 " autocomplete after ::
#let OmniCpp_DefaultNamespaces = ["std", "_GLIBCXX_STD"]

#" automatically open and close the popup menu / preview window
#"au CursorMovedI,InsertLeave * if pumvisible() == 0|silent! pclose|endif
#"set completeopt=menuone,menu,longest,preview
#set ts=4
#set sw=4
