#include "Sub.h"

// read_inertia - read inertia in from accelerometers
void Sub::read_inertia()
{
    // inertial altitude estimates
    inertial_nav.update();
    sub.pos_control.update_estimates();

    // pull position from ahrs
    Location loc;
    ahrs.get_location(loc);
    current_loc.lat = loc.lat;
    current_loc.lng = loc.lng;

    // exit immediately if we do not have an altitude estimate
    if (!AP::ahrs().has_status(AP_AHRS::Status::VERT_POS)) {
        return;
    }

    current_loc.alt = inertial_nav.get_position_z_up_cm();

    // get velocity, altitude is always absolute frame, referenced from
    // water's surface
    climb_rate = inertial_nav.get_velocity_z_up_cms();
}
