ArduPilot Plane Release Notes:
------------------------------------------------------------------
Release 4.6.0 21-May-2025

Changes from 4.6.0-beta6

1) Bug Fixes

- Plane landing fix to handle AGL or AMSL locations
- TBS bootloaders updated to avoid firmware erase on ECC error
------------------------------------------------------------------
Release 4.6.0-beta6 28-Apr-2025

Changes from 4.6.0-beta5

1) Board specific changes

- ARK FPV, LumenierLuxF765, MicroAir743, StellarF4v2, X-MAV-AP-H743v2 OSD Type defaults fixed
- BETAFPV-F405-I2C UART6 fix
- BrotherHobby F405v3 and H743 support
- FlywooF405HD VTX power pin fixed
- NarinFC-H7 support
- TBS Lucid H7 Wing support
- TBS Lucid H7 support for both I2C ports

2) Driver bug fixes and enhancements

- DroneCAN semaphore bug fixed which affected MPPT battery and Serial-over-CAN
- GPS logs altitude above ellipsoid instead of undulation
- GSOF GPS protects against port misconfiguration
- UBlox GPS configuration sped up
- Lua script battery monitor failures that could cause hardfaults fixed
- Nova GPS undulation fix
- Proximity backends increased to 5
- SCHA63T IMU temperature reporting bug fixed

3) Plane specific fixes and enhancements

- Circle mode terrain alt handling fixed
- Fence re-enable after fence breach fixed
- Prevent rotation pitch calculations from running post-rotation
- Takeoff leveloff timeout check added
- Terrain guided target intermediate alt handling fixed

4) Copter SCurve navigation fix to avoid excessive vertical speed when passing through waypoints

5) Rover circle mode and QuickTune support smaller vehicles

6) Bug Fixes and minor enhancements

- AHRS initial orientation fixed when autopilot mounted in non-default orientation
- AIRSPEED mavlink message health flags fixed
- CAMERA_FOV_STATUS message always sent (once requested), attitude is earth-frame and FOV can be decimal numbers
- DDS / ROS2 fix to provide clock_gettime only on ChibiOS boards
- Lightware I2C and HC SR04 fixed timestamp for last reading (should only affect AP_Periph devices)
- "No ap_message for mavlink id" warning removed
- Power brick2 valid flag fixed on some boards
- Pre-arm check that gyro read rate is at least the main loop rate
------------------------------------------------------------------
Release 4.6.0-beta5 31-Mar-2025

Changes from 4.6.0-beta4

1) Board specfic changes

- MFT-SEMA100 compass orientation fix
- SpeedyBee F405 AIO support

2) Driver bug fixes and enhancements

- Bdshot locking issue fix for slow boards that could cause the flight controller to crash
- BMP280 barometer error handling during init improved
- CADDX gimbal RC update rate fixed
- Hexsoon 77G and 24G radar support
- IE FuelCell generator data rate increased
- IIS2MDC compass support
- LD19/LD06 proximity sensor data length sanity check fix
- RC output cork/push fix to avoid potential motor knocking
- SBF MosaicX5 packets with invalid length ignored
- SPL06 barometer accuracy improvements
- Ublox M10 configuration fix

3) Plane specific fixes and enhancements

- Tiltrotor motor check fixed used in throttle control handover from VTOL to forward flight
- Improved QAssist and VTOL mode recovery from bad attitudes
- Fixed rudder output in VTOL modes
- Added fix for indecision in the roll controller close to inverted
- Ensure tiltrotors use consistent fixed wing and VTOL rates
- Clear inverted control flag on Q-mode entry
- Auto-deploy landing gear on all fixed wing landings when enabled
- Prevent learning bad ARSP_RATIO values when not flying

4) Rover specific fixes and enhancements

- Lua bindings for Follow mode
- WATER_DEPTH mavlink message rate fix

5) Parameter description improvements

- ADSB_LIST_ALT param desc improved
- Alphabetised type params for CAN, Mount, OptFlow, Proximity, Rangefinder, Serial Manager
- Copter's battery failsafe action gets "Brake or Land"
- LOG_FILE_BUFSIZE units fixed
- MOT_THST_HOVER range fixed
- SERIALx_BAUD value added for PPP networking

6) Bug Fixes and minor enhancements

- Arming check for EKF3 velocity innovations
- Copter's LOG_BITMASK includes PIDs by default
- DO_SET_ROI_LOCATION mission command support
- MCU min/max voltage logging fix
- MIS_OPTION parameter handling fixed
------------------------------------------------------------------
Release 4.6.0-beta4 14-Feb-2025

Changes from 4.6.0-beta3

1) Board specfic changes

- BETAFPV F405 supports DPS310 baro
- BETAFPV F405 board variants added

2) Driver enhancements

- CADDX camera gimbal support
- UltraMotion CAN servo support

3) Copter specific fixes and minor enhancements

- SysId mode uninitialised variables fixed
- ARMING_OPTIONS gets "Require position for arming" (was in FLIGHT_OPTIONS)

4) Bug Fixes and minor enhancements

- AM32 ESC passthrough fixed
- BMP581 baro initialisation fix
- DDS/ROS2 driver waits indefinitely until companion computer running ROS2 starts
- Lua script potential deadlock fix when initialising mavlink in script
- Mcast and HW CAN bridging disabled by default
- Plane's TILT log message only logged on tilt-rotors
- ROMFS race condition when opening files that caused apparent script corruption fixed
- Serial flow control support on UARTS 6 to 8
- Serial passthrough fix to restore parity upon completion
- Serial protocol parameter fix to include I-Bus Telemetry
- uAvionix ping200X driver fixes
- Video stream information lua script param index fixed (avoids param conflict with other scripts)
- ViewPro object tracking fixed
------------------------------------------------------------------
Release 4.6.0-beta3 21-Jan-2025

Changes from 4.6.0-beta2

1) Board specfic changes

- AEROFOX-H7 support
- AET-H743-Basic support
- ESP32 memory initialisation fix
- MicoAir743AIO and MicoAir743v2 support
- OrqaF405Pro supports ICM42688 IMU
- TBS Lucid Pro support

2) Plane related enhancements and bug fixes

- RC aux channel option for C++ QuikTune
- TECs uses external HAGL (if available) for landing

3) DDS/ROS2 pre-arm check and copter takeoff service

4) Bug Fixes and minor enhancements

- BATTx_OPTIONS param desc fixed
- BLHeli telemetry ESC index fixed on boards with IOMCU
- CAN manager logging critical race condition fixed (only occurred if CAN_LOGLEVEL was 1 or higher)
- DShot EDTv2 logging fixed
- ICM45686 IMU FIFO read rate fixed (could read unnecessarily quickly)
- LDRobot LD06 proximity driver prevents possible read buffer overrun
- LDRobot LD06 proximity driver averages over 2deg slices (was 12 deg)
- RunCam/OSD menu movements obey RC channel reversal
- Topotek camera gimbal pitch rate control direction fixed
- TradHeli AutoTune rate and accel limiting fixed
- VTOL-quiktune script gets QUIK_ANGLE_MAX to prevent bad oscillation if tuning algorithm fails
------------------------------------------------------------------
Release 4.6.0-beta2 11 Dec 2024

Changes from 4.6.0-beta1

1) Board specfic changes

- FoxeerF405v2 supports BMP280 baro
- KakuteH7, H7-Mini, H7-Wing, F4 support SPA06 baro
- MUPilot support
- SkySakura H743 support
- TBS Lucid H7 support
- VUAV-V7pro README documentation fixed
- X-MAV AP-H743v2 CAN pin definition fixed

2) Copter specific enhancements and bug fixes

- AutoTune fix for calc of maximum angular acceleration
- Advanced Failsafe customer build server option

3) Plane related enhancements and bug fixes

- QuadPlane fix for QLand getting stuck in pilot repositioning
- QuikTune C++ conversion (allow running quiktun on F4 and f7 boards)
- Takeoff direction fixed when no yaw source
- TECS correctly handles home altitude changes

4) Bug Fixes and minor enhancements

- AIRSPEED_AUTOCAL mavlink message only sent when required and fixed for 2nd sensor
- CAN frame logging added to ease support
- CRSF reconnection after failsafe fixed
- EKF3 position and velocity resets default to user defined source
- Ethernet IP address default 192.168.144.x
- Fence autoenable fix when both RCn_OPTION=11/Fence and FENCE_AUTOENABLE = 3 (AutoEnableOnlyWhenArmed)
- Fence pre-arm check that vehicle is within polygon fence
- Fence handling of more than 256 items fixed
- FFT protection against divide-by-zero in Jain estimator
- Frsky telemetry apparent wind speed fixed
- Inertial sensors stop sensor converging if motors arm
- Inertial sensors check for changes to notch filters fixed
- Real Time Clock allowed to shift forward when disarmed
- ROS2/DDS get/set parameter service added
- Scripting gets memory handling improvements
- Scripting promote video-stream-information to applet
- Topotek gimbal driver uses GIA message to retrieve current angle
- Tramp VTX OSD power indicator fixed
------------------------------------------------------------------
Release 4.6.0-beta1 13 Nov 2024

Changes from 4.5.7

1) Board specific changes

- AnyLeaf H7 supports compass and onboard logging
- Blitz743Pro supports CAN
- BlueRobotics Navigator supports BMP390 baro
- Bootloader ECC failure check fixed on boards with >128k bootloader space (e.g CubeRed)
- CB Unmanned Stamp H743 support
- ClearSky CSKY405 support
- CUAV-7-Nano default batt monitor fixed
- CubeRed bootloader fixes including disabling 2nd core by default
- CubeRed supports PPP networking between primary and secondary MCU
- CubeRedPrimary supports external compasses
- ESP32 main loop rate improvements
- ESP32 RC input fixes and wifi connection reliability improved
- ESP32 safety switch and GPIO pin support
- FlyingMoon no longer support MAX7456
- Flywoo F405HD-AIOv2 ELRS RX pin pulled high during boot
- Flywoo H743 Pro support
- Flywoo/Goku F405 HD 1-2S ELRS AIO v2
- FlywooF745 supports DPS310 baro
- FPV boards lose SMBus battery support (to save flash)
- GEPRC F745BTHD support
- GEPRCF745BTHD loses parachute support, non-BMP280 baros (to save flash)
- Here4FC bootloader fix for mismatch between RAM0 and periph that could prevent firmware updates
- Holybro Kakute F4 Wing support
- iFlight 2RAW H743 supports onboard logging
- JFB110 supports measuring servo rail voltage
- JFB110 supports safety switch LED
- JHEM-JHEF405 bootloader supports firmware updates via serial
- JHEMCU GF30H743 HD support
- JHEMCU-GF16-F405 autopilot support
- JHEMCU-GSF405A becomes FPV board (to save flash)
- KakuteF7 only supports BMP280 baro (to save flash)
- KakuteH7Mini supports ICM42688 IMU
- Linux auto detection of GPS baud rate fixed
- Linux board scheduler jitter reduced
- Linux board shutdown fixes
- MakeFlyEasy PixPilot-V6Pro support
- MatekF405, Pixhawk1-1M-bdshot, revo-mini loses blended GPS (to save flash)
- MatekH7A3 support Bi-directional DShot
- MicoAir405v2 and MicoAir405Mini support optical flow and OSD
- MicoAir743 internal compass orientation fixed
- MicroAir405Mini, MicroAir743, NxtPX4v2 support
- MicroAir405v2 Bi-directional DShot and LED DMA fixes
- MicroAir405v2 defined as FPV board with reduced features (to save flash)
- ModalAI VOXL2 support including Starling 2 and Starling 2 max
- mRo Control Zero Classic supports servo rail analog input
- mRo KitCAN revC fixed
- Mugin MUPilot support
- OmnibusF7v2 loses quadplane support (to save flash)
- Pixhack-v3 board added (same as fmuv3)
- Pixhawk6C bootloader supports flashing firmware from SD card
- RadiolinkPIX6 imu orientation fixed
- RadiolinkPIX6 supports SPA06 baro
- ReaperF745 V4 FC supports MPU6000 IMU
- RPI5 support
- SDModelH7v2 SERIAL3/7/9_PROTOCOL param defaults changed
- Solo serial ports default to MAVLink1
- SpeedyBeeF405Wing gets Bi-directional DShot
- SpeedyBeeF405WING loses landing gear support, some camera gimbals (to save flash)
- Spektreworks boom board support
- TrueNavPro-G4 SPI does not share DMA
- X-MAV AP-H743v2 support

2) AHRS/EKF enhancements and fixes

- AHRS_OPTION to disable fallback to DCM (affects Plane and Rover, Copter never falls back)
- AHRS_OPTION to disable innovation check for airspeed sensor
- Airspeed sensor health check fixed when using multiple sensors and AHRS affinity
- DCM support for MAV_CMD_EXTERNAL_WIND_ESTIMATE (Plane only)
- EK2 supports disabling external nav (see EK2_OPTIONS)
- EK3 External Nav position jump after switch from Optical flow removed (see EK3_SRC_OPTION=1)
- EK3 uses filtered velocity corrections for IMU position
- EKF2, EKF3, ExternalAHRS all use common origin
- EKF3 accepts set origin even when using GPS
- EKF3 allows earth-frame fields to be estimated with an origin but no GPS
- EKF3 copes better with GPS jamming
- EKF3 logs mag fusion selection to XKFS
- EKF3 wind estimation when using GPS-for-yaw fixed
- External AHRS improvements including handling variances, pre-arm origin check
- Inertial Labs External AHRS fixes
- VectorNav driver fix for handling of error from sensor
- VectorNav External AHRS enhancements including validation of config commands and logging improvements
- VectorNav support for sensors outside VN-100 and VN-300

3) Driver enhancements and bug fixes

- ADSB fix to display last character in status text sent to GCS
- Ainstein LR-D1 radar support
- Airspeed ASP5033 whoami check fixed when autopilot rebooted independently of the sensor
- AIRSPEED message sent to GCS
- Analog temperature sensor extended to 5th order polynomial (see TEMP_A5)
- ARSPD_OPTIONS to report calibration offset to GCS
- Baro EAS2TAS conversions continuously calculated reducing shocks to TECS (Plane only)
- Baro provides improved atmospheric model for high altitude flight
- BARO_ALT_OFFSET slew slowed to keep EKF happy
- BATTx_ESC_MASK param supports flexible mapping of ESCs to batteries
- BATTx_OPTION to not send battery voltage, current, etc to GCS
- Benewake RDS02U radar support
- Bi-directional DShot on IOMCU supports reversible mask
- Bi-directional DShot telemetry support on F103 8Mhz IOMCUs
- BMM350 compass support
- CAN rangefinders and proximity sensors may share a CAN bus (allows NRA24 and MR72 on a single CAN bus)
- Compass calibration world magnetic model checks can use any position source (e.g. not just GPS)
- CRSF baro and vertical speeed fixed
- CRSF RX bind command support
- DroneCAN battery monitor check to avoid memory corruption when type changed
- DroneCAN DNA server fixes including removing use of invalid node IDs, faster ID allocation, elimination of rare inability to save info
- DroneCAN EFI health check fix
- DroneCAN ESC battery monitors calculate consumed mah
- DroneCAN ESCs forced to zero when disarmed
- DroneCAN RPM message support
- DroneCAN timeout fix for auxiliary frames
- DroneCAN to serial tunneling params accepts short-hand baud rates (e.g. '57' for '57600')
- F9P, F10-N and Zed-F9P support for GPSx_GNSS_MODE to turn on/off SBAS, Galileo, Beidou and Glonass
- FuelLevel battery monitor fix to report capacity
- GPS_xxx params renamed to GPS1_xxx, GPS_xxx2 renamed to GPS2_xxx
- Hirth EFI logging includes modified throttle
- Hirth ICEngine supports reversed crank direction (see ICE_OPTIONS parameter)
- Hott and LTM telemetry deprecated (still available through custom build server)
- i-BUS telemetry support
- ICE_PWM_IGN_ON, ICE_PWM_IGN_OFF, ICE_PWM_STRT_ON, ICE_PWM_STRT_OFF replaced with SERVOx_MIN/MAX/REVERSED (Plane only)
- ICE_START_CHAN replaced with RC aux function (Plane only)
- ICEngine retry max added (see ICE_STRT_MX_RTRY)
- IE 2400 generator error message severity to GCS improved
- INA2xx battery monitor support (reads temp, gets MAX_AMPS and SHUNT params)
- MCP9600 temperature sensor I2C address fixed
- MLX90614 temperature sensor support
- MSP GPS ground course scaling fixed
- MSP RC uses scaled RC inputs (fixes issue with RCx_REVERSED having no effect)
- Networking supports reconnection to TCP server or client
- OSD params for adjusting horizontal spacing and vertical extension (see OSD_SB_H_OFS, OSD_SB_V_EXT)
- Relay inverted output support (see RELAYx_INVERTED parameter)
- ROMFS efficiency improvements
- RS-485 driver enable RTS flow control
- Sagetech MXS ADSP altitude fix (now uses abs alt instead of terrain alt)
- Septentrio GPS sat count correctly drops to zero when 255 received
- Septentrio supports selecting constellations (see GPS_GNSS_MODE)
- Single LED for user notification supported
- SPA06 baro supported
- Sum battery monitor optionally reports minimum voltage instead of average
- Sum battery monitor reports average temp
- Torqeedo dual motor support (see TRQ1, TRQ2 params)
- Ublox GPS driver uses 64 bit time for PPS interrupt (avoids possible dropout at 1hour and 12 minutes)
- UBlox GPS time ignored until at least 2D fix
- VideoTX supports additional freq bands (RushFPV 3.3Ghz)
- Volz logs desired and actual position, voltage, current, motor and PCB temp
- Volz server feedback and logging fixed
- Volz servo output in its own thread resulting in smoother movements
- W25N02KV flash support

4) Networking enhancements and fixes

- Allow multiple UDP clients to connect/disconnect/reconnect
- Ethernet supports faster log downloading (raised SDMMC clock limit on H7)

5) Camera and gimbal enhancements

- Alexmos precision improved slightly
- CAMERA_CAPTURE_STATUS mavlink msg sent to GCS (reports when images taken or video recorded, used by QGC)
- CAMERA_FOV_STATUS mavlink reports lat,lon of what camera is pointing at
- DO_MOUNT_CONTROL yaw angle interpreted as body-frame (was incorrectly being interpreted as earth-frame)
- Dual serial camera gimbal mounts fixed
- Lua script bindings to send CAMERA_INFORMATION and VIDEO_STREAM_INFORMATION messages to GCS
- Retract Mount2 aux function added (see RCx_OPTION = 113)
- Servo gimbal reported angles respect roll, pitch and yaw limits
- Siyi driver sends autopilot location and speed (recorded in images via EXIF)
- Siyi picture and video download scripts
- Siyi ZT6 and ZT30 support sending min, max temperature (see CAMERA_THERMAL_RANGE msg)
- Siyi ZT6 and ZT30 thermal palette can be changed using camera-change-setting.lua script
- Siyi ZT6 hardware id and set-lens fixed
- Topotek gimbal support
- Trigger distance ignores GPS status and only uses EKF reported location

6) Harmonic notch enhancements

- Harmonic notch is active in forward flight on quadplanes
- Harmonic notch filter freq clamping and disabling reworked
- Harmonic notch handles negative ESC RPMs
- Harmonic notch supports per-motor throttle-based harmonic notch

7) Copter specific enhancements and bug fixes

- Attitude control fix to dt update order (reduces rate controller noise)
- Auto mode fix to avoid prematurely advancing to next waypoint if given enough time
- Auto mode small target position jump when takeoff completes removed
- Auto mode yaw drift when passing WP removed if CONDITION_YAW command used and WP_YAW_BEHAVIOR = 0/None
- Auto, Guided flight mode pause RC aux switch (see RCx_OPTION = 178)
- AutoRTL (e.g. DO_LAND_START) uses copter stopping point to decide nearest mission command
- AutoRTL mode supports DO_RETURN_PATH_START (Copter, Heli only)
- AutoTune fix to prevent spool up after landing
- AutoTune performance and safety enhancements (less chance of ESC desync, fails when vehicle likely can't be tuned well)
- Autotune test gains RC aux switch function allows testing gains in any mode (see RCx_OPTION = 180)
- Config error avoided if auto mode is paused very soon after poweron
- FLIGHT_OPTIONS bit added to require position estimate before arming
- Follow mode slowdown calcs fixed when target is moving
- Ground oscillation suppressed by reducing gains (see ATC_LAND_R/P/Y_MULT)
- Guided mode internal error fix when taking off using SET_ATTITUDE_CONTROL message
- Guided mode internal error resolved when switching between thrust or climb rate based altitude control
- Guided mode yaw fixed when WP_YAW_BEHAVIOR = 0/None and CONDITION_YAW command received containing relative angle
- Landing detector fixed when in stabilize mode at full throttle but aircraft is upside down
- Landing detector logging added to ease support (see LDET message)
- Loiter unlimited command accepts NaNs (QGC sends these)
- Mavlink SYSTEM_STATUS set to BOOT during initialisation
- MOT_PWM_TYPE of 9 (PWMAngle) respects SERVOx_MIN/MAX/TRIM/REVERSED param values
- Payload place bug fix when aborted because gripper is already released
- RC based tuning (aka CH6 tuning) can use any RC aux function channel (see RCx_OPTION = 219)
- RTL_ALT minimum reduced to 30cm
- SystemID position controller support (Copter and Heli)
- TriCopter motor test and slew-up fixed
- WPNAV_SPEED min reduced to 10 cm/s (Copter only)
- Loiter mode zeros desired accel when re-entering from Auto during RC failsafe

8) TradHeli specific enhancements

- Autorotation yaw behaviour fix
- Autotune improvements including using low frequency dwell for feedforward gain tuning and conducting sweep in attitude instead of rate
- Blade pitch angle logging added (see SWSH log message)
- Constrain cyclic roll for intermeshing
- ICE / turbine cool down fix
- Inverted flight extended to non manual throttle modes
- Inverted flight transitions smoothed and restricted to only Stabilize mode
- SWSH logging fix for reversed collectives

9) Plane specific enhancements and bug fixes

- AIRSPEED_STALL holds vehicle stall speed and is used for minimum speed scaling
- Allow for different orientations of landing rangefinder
- Assistance requirements evaluted on mode change
- FBWB/CRUISE climb/sink rate limited by TECS limits
- FLIGHT_OPTION to immediately climb in AUTO mode (not doing glide slope)
- Glider pullup support (available only through custom build server)
- Loiter breakout improved to better handle destinations inside loiter circle
- Manual mode throttle made consistent with other modes (e.g battery comp and watt limit is done if enabled)
- Mavlink GUIDED_CHANGE_ALTITUDE supports terrain altitudes
- Minimum throttle not applied during SLT VTOL airbrake (reduces increase in airspeed and alt during back transition)
- Q_APPROACH_DIST set minimum distance to use the fixed wing approach logic
- Quadplane assistance check enhancements
- Quadplane Deca frame support
- Quadplane gets smoother takeoff by input shaping target accel and velocity
- Servo wiggles in altitude wait staged to be one after another
- Set_position_target_global_int accepts MAV_FRAME_GLOBAL_RELATIVE_ALT and MAV_FRAME_GLOBAL_TERRAIN_ALT frames
- Takeoff airspeed control improved (see TKOFF_MODE, TKOFF_THR_MIN)
- Takeoff fixes for fence autoenable
- Takeoff improvements including less overshoot of TKOFF_ALT
- TECS reset along with other controllers (important if plane dropped from balloon)
- Tilt quadplane ramp of motors on back transition fixed
- Tiltrotor tilt angles logged
- TKOFF_THR_MIN applied to SLT transitions
- Twin motor planes with DroneCAN ESCs fix to avoid max throttle at arming due to misconfiguration
- VTOLs switch to QLAND if a LONG_FAILSAFE is triggered during takeoff

10) Rover specific enhancements and bug fixes

- Auto mode reversed state maintained if momentarily switched to Hold mode
- Circle mode tracks better and avoids getting stuck at circle edge
- Flight time stats fixed
- MAV_CMD_NAV_SET_YAW_SPEED deprecated
- Omni3Mecanum frame support
- Stopping point uses max deceleration (was incorrectly using acceleration)
- Wheel rate controller slew rate fix

11) Antenna Tracker specific enhancements and bug fixes

- Never track lat,lon of 0,0

12) Scripting enhancements

- advance-wp.lua applet supports advancing Auto mode WP via RC switch
- AHRS_switch.lua supports switching between EKF2 and EKF3 via RC switch
- battery_internal_resistance_check.lua monitors battery resistance
- CAN:get_device returns nil for unconfigured CAN device
- copter_terrain_brake.lua script added to prevent impact with terrain in Loiter mode (Copter only)
- Copter:get_target_location, update_target_location support
- crosstrack_restore.lua example allows returning to previous track in Auto (Plane only)
- Display text on OLED display supported
- Docs improved for many bindings
- EFI get_last_update_ms binding
- EFI_SkyPower.lua driver accepts 2nd supply voltage
- ESC_slew_rate.lua example script supports testing ESCs
- Filesystem CRC32 check to allow scripts to check module versions
- forced arming support
- GPIO get/set mode bindings (see gpio:set_mode, get_mode)
- GPS-for-yaw angle binding (see gps:gps_yaw_deg)
- Halo6000 EFI driver can log all CAN packets for easier debugging
- handle_external_position_estimate binding allows sending position estimate from lua to EKF
- I2C:transfer support
- IMU gyros_consistent and accels_consistent bindings added
- INF_Inject.lua driver more robust to serial errors, improved logging, throttle and ignition control
- INS bindings for is calibrating, gyro and accel sensor values
- IPV4 address bindings (see SocketAPM_ipv4_addr_to_string) to allow UDP server that responds to individual clients
- Logging of booleans supported
- Lua language checks improved (finds more errors)
- MAVLink commands can be called from scripting
- MCU voltage binding (see analog:mcu_voltage)
- NMEA 2000 EFI driver (see EFI_NMEA2k.lua)
- "open directory failed" false warning when scripts in ROMFS fixed
- Param_Controller.lua supports quickly switching between 3 parameter sets via RC switch
- Pass by reference values are always initialized
- pelco_d_antennatracker.lua applet supports sending Pelco-D via RS-485 to PTZ cameras
- plane_aerobatics.lua minor enhancements
- REPL applet (read-evaluate-print-loop, see repl.lua) for interactive testing and experimentation
- "require" function failures in rare circumstances fixed
- "require" function works for modules in ROMFS (e.g. not on SD card)
- revert_param.lua supports more params (e.g ATC_RATE_R/P/Y, PTCH2SRV_TCONST, RLL2SRV_TCONST, TECS_)
- Scripts may receive mavlink messages which fail CRC (e.g messages which FC does not understand)
- SD card formatting supported
- Serial device simulation support (allows Lua to feed data to any supported serial protocol for e.g. sensor simulation)
- set_target_throttle_rate_rpy allows rate control from scripts (new for Copter)
- sitl_standby_sim.lua example shows how to switch between redundant flight controllers using an RC switch
- Slung payload oscillation suppression applet added (see copter-slung-payload.lua)
- Temperature sensor bindings added
- uint64 support
- Various performance and memory usage optimizations
- VTOL-quicktune.lua minor enhancements including increasing YAW_FLTE_MAX to 8
- x-quad-cg-allocation.lua applet corrects for the CoM discrepancy in a quad-X frame

13) GCS / mavlink related changes and fixes

- BATTERY2 message deprecated (replaced by BATTERY_STATUS)
- CMD_MISSION_START/STOP rejected if first-item/last-item args provided
- Deny attempts to upload two missions simultaneously
- Fence and Rally points may be uploaded using FTP
- GPS_INPUT and HIL_GPS handles multiple GPSs
- HIGHRES_IMU mavlink message support (used by companion computers to receive IMU data from FC)
- MAV_CMD_COMPONENT_ARM_DISARM accepts force arm magic value of 21196
- MAV_CMD_DO_SET_SAFETY_SWITCH_STATE support
- MAV_CMD_SET_HAGL support (Plane only)
- MAVFTP respects TX buffer flow control to improve FTP on low bandwidth links
- MAVLink receiver support (RADIO_RC_CHANNELS mavlink message)
- Message interval supports TERRAIN_REPORT msg
- Mission upload may be cancelled using MISSION_CLEAR_ALL
- MOUNT_CONFIGURE, MOUNT_CONTROL messages deprecated
- RC_CHANNELS_RAW deprecated
- Serial passthrough supports parity allowing STM32CubeProgrammer to be used to program FrSky R9 receivers
- SET_ATTITUDE_TARGET angular rate input frame fixed (Copter only)
- TIMESYNC and NAMED_VALUE_FLOAT messages not sent on high latency MAVLink ports

14) Logging enhancements and fixes

- AC_PID resets and I-term sets logged
- ANG provides attitude at high rate (equivalent to ATT)
- ATT logs angles as floats (better resolution than ints)
- CAND message gets driver index
- DCM log message includes roll/pitch and yaw error
- EDT2 log msg includes stress and status received via extended DShot Telemetry v2
- EFI ECYL cylinder head and exhaust temp logs in degC (was Kelvin)
- ESCX log msg includes DroneCAN ESCs status, temp, duty cycle and power pct
- FMT messages written as required instead of all at beginning
- Logging restarted after download completes when LOG_DISARMED = 1
- MISE msg logs active mission command (CMD logged when commands are uploaded)
- ORGN message logging fixed when set using SET_GPS_GLOBAL_ORIGIN
- RPM sensor logging gets instance field, quality and health fields
- Short filename support removed (e.g log1.BIN instead of 00000001.BIN)
- Temperature sensor logging option for only sensors with no source (see TEMP_LOG)
- UART data rates logged at 1hz (see UART message)

15) ROS2 / DDS support

- Airspeed published
- Battery topic reports all available batteries
- Compile-time configurable rates for each publisher
- DDS_TIMEOUT_MS and DDS_MAX_RETRY set timeout and num retries when client pings XRCE agent
- GPS global origin published at 1 Hz
- High frequency raw imu data transmission
- Joystick support
- Support sending waypoints to Copter and Rover
- Remove the XML refs file in favor of binary entity creation

16) Safety related enhancements and fixes

- Accel/Gyro inconsistent message fixed for using with only single IMU
- Battery monitor failure reported to GCS, triggers battery failsafe but does not take action
- Far from EKF origin pre-arm check removed (Copter only)
- Fence breach warning message slightly improved
- Fence enhancements incluiding alt min fence (Copter only, see FENCE_AUTOENABLE, FENCE_OPTIONS)
- Fences can be stored to SD Card (see BRD_SD_FENCE param)
- ICEngine stopped when in E-Stop or safety engaged (Plane only)
- LEDs flash green lights based on EKF location not GPS
- Parachute option to skip disarm before parachute release (see CHUTE_OPTIONS)
- Plane FENCE_AUTOENABLE of 1 or 2 deprecation warning added
- Pre-arm check if OpenDroneID is compiled in but disabled
- Pre-arm check of duplicate RC aux functions fixed (was skipping recently added aux functions)
- Pre-arm checks alert user more quickly on failure
- Prearm check for misconfigured EAHRS_SENSORS and GPS_TYPE
- Proximity sensor based avoidance keeps working even if one proximity sensor fails (Copter, Rover)
- RC aux functions for Arm, Disarm, E-Stop and Parachute ignored when RC is first turned on
- Warning of duplicate SERIALx_PROTOCOL = RCIN

17) Other bug fixes and minor enhancements

- Accel cal fixed for auxiliary IMUs (e.g. IMU4 and higher)
- Bootloader fix to reduce unnecessary mass erasing of flash when using STLink or Jlink tools
- Bootloader rejects allocation of broadcast node ID
- CAN forward registering/de-registering fix (affected Mission Planner DroneCAN UI)
- Dijkstras fix to correct use of uninitialised variable
- DShot rates are not limited by NeoPixel rates
- Ethernet and CAN bootloader fix to prevent getting stuck in bootloader mode
- Filesystem does not show entries for empty @ files
- Filesystem efficiency improvements when reading files
- Flight statistics only reset if user sets STAT_RESET to zero (avoids accidental reset)
- Flight time statistics updated on disarm (avoids issue if vehicle powered-down soon after disarm)
- Internal error thrown if we lose parameters due to saving queue being too small
- MAVLink via DroneCAN baud rate fix
- SPI pins may also be used as GPIOs
- Terrain cache size configurable (see TERRAIN_CACHE_SZ)

18) Developer focused fixes and enhancements

- AP_Camera gets example python code showing how to use GStreamer with UDP and RTSP video streams
- Cygwin build fix for non-SITL builds
- Cygwin build fixed with malloc wrap
- DroneCAN and scripting support FlexDebug messages (see CAN_Dn_UC_OPTION, FlexDebug.lua)
- EKF3 code generator documentation and cleanup
- GPS jamming simulator added
- MacOS compiler warnings reduced
- SFML joystick support
- SITL support for OpenBSD
- Text warning if older Fence or Rally point protocols are used
------------------------------------------------------------------
Release 4.5.7 08 Oct 2024

Changes from 4.5.7-beta1

1) Reverted Septentrio GPS sat count correctly drops to zero when 255 received
------------------------------------------------------------------
Release 4.5.7-beta1 26 Sep 2024

Changes from 4.5.6

1) Bug fixes and minor enhancements

- VUAV-V7pro support
- CUAV-7-Nano correction for LEDs and battery volt and current scaling
- DroneCAN deadlock and saturation of CAN bus fixed
- DroneCAN DNA server init fix (caused logging issues and spam on bus)
- F4 boards with inverter support correctly uninvert RX/TX
- Nanoradar M72 radar driver fix for object avoidance path planning
- Plane fix to ability to disable the takeoff atititude checks
- RC support for latest version of GHST
- Septentrio GPS sat count correctly drops to zero when 255 received

2) ROS2/DDS and other developer focused enhancements

- AP quaternions normalised for ROS2 to avoid warnings
- Dependencies fixed for easier installation
- ROS2 SITL launch file enhancements including displaying console and map
- ROS_DOMAIN_ID param added to support multiple vehicles or instances of ROS2
- Python 3.12 support
------------------------------------------------------------------
Release 4.5.6 03 Sep 2024

No changes from 4.5.6-beta1
------------------------------------------------------------------
Release 4.5.6-beta1 20 Aug 2024

Changes from 4.5.5

1) Board specific enhancements and bug fixes

- 3DR Control Zero H7 Rev G support
- CUAV-7-Nano support
- FoxeerF405v2 servo outputs increased from 9 to 11
- Holybro Pixhawk6C hi-power peripheral overcurrent reporting fixed
- iFlight 2RAW H7 support
- MFT-SEMA100 support
- TMotorH743 support BMI270 baro
- ZeroOneX6 support

2) Minor enhancements and bug fixes

- Cameras using MAVLink report vendor and model name correctly
- DroneCAN fix to remove occasional NodeID registration error
- GPS NMEA and GSoF driver ground course corrected (now always 0 ~ 360 deg)
- ICP101XX barometer slowed to avoid I2C communication errors
- IMU temp cal param (INSn_ACCSCAL_Z) stored correctly when bootloader is flashed
- IMU gyro/accel duplicate id registration fixed to avoid possible pre-arm failure
- Logging to flash timestamp fix
- OSD displays ESC temp instead of motor temp
- PID controller error calculation bug fix (was using target from prev iteration)
- Relay on MAIN pins fixed
------------------------------------------------------------------
Release 4.5.5 1st Aug 2024

No changes from 4.5.5-beta2
------------------------------------------------------------------
Release 4.5.5-beta2 27 July 2024

Changes from 4.5.5-beta1

1) Board specific enhancements and bug fixes

- CubeRed's second core disabled at boot to avoid spurious writes to RAM
- CubeRed bootloader's dual endpoint update method fixed
------------------------------------------------------------------
Release 4.5.5-beta1 1st July 2024

Changes from 4.5.4

1) Board specific enhancements and bug fixes

- fixed IOMCU transmission errors when using bdshot
- update relay parameter names on various boards
- add ASP5033 airspeed in minimal builds
- added RadiolinkPIX6
- fix Aocoda-RC H743Dual motor issue
- use ICM45686 as an ICM20649 alternative in CubeRedPrimary

2) System level minor enhancements and bug fixes

- correct use-after-free in script statistics
- added arming check for eeprom full
- fixed a block logging issue which caused log messages to be dropped
- enable Socket SO_REUSEADDR on LwIP
- removed IST8310 overrun message
- added Siyi ZT6 support
- added BTFL sidebar symbols to the OSD
- added CRSF extended link stats to the OSD
- use the ESC with the highest RPM in the OSD when only one can be displayed
- support all Tramp power levels on high power VTXs
- emit jump count in missions even if no limit
- improve the bitmask indicating persistent parameters on bootloader flash	
- fix duplicate error condition in the MicroStrain7

3) AHRS / EKF fixes

- fix infinite climb bug when using EK3_OGN_HGT_MASK

4) Plane specific changes

- fix rangefinder correction when terrain following is off
- correct description of MIN_GROUNDSPEED parameter
- correct Q_TRIM_PITCH description
- ensure the dshot type gets set at startup

5) Other minor enhancements and bug fixes

- specify pymonocypher version in more places
- added DroneCAN dependencies to custom builds

------------------------------------------------------------------
Release 4.5.4 12th June 2024

Changes from 4.5.3

Disable highres IMU sampling on ICM42670 fixing an issue on some versions of Pixhawk6X

------------------------------------------------------------------
Release 4.5.3 28th May 2024

No changes from 4.5.3-beta1
------------------------------------------------------------------
Release 4.5.3-beta1 16th May 2024

Changes from 4.5.2

1) Board specific enhancements and bug fixes

- correct default GPS port on MambaH743v4
- added SDMODELV2
- added iFlight Blitz H7 Pro
- added BLITZ Wing H743
- added highres IMU sampling on Pixhawk6X

2) System level minor enhancements and bug fixes

- fixed rare crash bug in lua scripting on script fault handling
- fixed Neopixel pulse proportions to work with more LED variants
- fixed timeout in lua rangefinder drivers
- workaround hardware issue in IST8310 compass
- allow FIFO rate logging for highres IMU sampling

3) Plane specific changes

- fixed cancelling of FWD_GAIN setting for tiltrotors

------------------------------------------------------------------
Release 4.5.2 14th May 2024

No changes from 4.5.2-beta1
------------------------------------------------------------------
Release 4.5.2-beta1 29th April 2024

Changes from 4.5.1

1) Board specific enhancements and bug fixes

- FoxeerF405v2 support
- iFlight BLITZ Mini F745 support
- Pixhawk5X, Pixhawk6C, Pixhawk6X, Durandal power peripherals immediately at startup

2) System level minor enhancements and bug fixes

- Camera lens (e.g. RGB, IR) can be selected from GCS or during missions using set-camera-source
- Crashdump pre-arm check added
- Gimbal gets improved yaw lock reporting to GCS
- Gimbal default mode fixed (MNTx_DEFLT_MODE was being overriden by RC input)
- RM3100 compass SPI bus speed reduced to 1Mhz
- SBUS output fix for channels 1 to 8 also applying to 9 to 16
- ViewPro gimbal supports enable/disable rangefinder from RC aux switch
- Visual Odometry delay fixed (was always using 1ms delay, see VISO_DELAY_MS)
- fixed serial passthrough to avoid data loss at high data rates

3) AHRS / EKF fixes

- Compass learning disabled when using GPS-for-yaw
- GSF reset minimum speed reduced to 1m/s (except Plane which remains 5m/s)
- MicroStrain7 External AHRS position quantization bug fix
- MicroStrain7 init failure warning added
- MicroStrain5 and 7 position and velocity variance reporting fix

4) Plane specific changes

- Drop min Q_TRANSITION_MS to 500ms
- FBWB/CRUISE missing zero crossing of elevator input fix
- PTCH_LIM_MIN_DEG param units fixed to be deg

5) Other minor enhancements and bug fixes

- DDS_UDP_PORT parameter renamed (was DDS_PORT)
- Harmonic Notch bitmask parameter conversion fix (see INS_HNTCH_HMNCS)

Release 4.5.1 8th April 2024
----------------------------

This release fixes a critical bug in the CRSF R/C protocol parser that
can lead to a handfault and a vehicle crashing. A similar fix was
applied to the GHST protocol, although we believe that GHST could not
be affected by the bug, so this was just a precaution.

There are no other changes in this release.

Release 4.5.0 2nd April 2024
----------------------------

No changes from beta4

Release 4.5.0-beta4 22nd March 2024
-----------------------------------

Changes from 4.5.0-beta3

1) system changes

- fixed a cache corruption issue with microSD card data on H7 based boards
- rename parameter NET_ENABLED to NET_ENABLE
- fixed FDCAN labels for adding new H7 boards
- avoid logging dma.txt to save CPU
- fixed roll/pitch in viewpro driver
- added band X in VideoTX
- fixed quaternion attitude reporting for Microstrain external AHRS
-  add RPLidarC1 proximity support

2) new boards
- added MicoAir405v2
- add Orqa F405 Pro

3) plane specific changes
- fixed flare on TVBS quadplanes with throttle stick input


Release 4.5.0-beta3 14th March 2024
-----------------------------------

Changes from 4.5.0-beta2

1) Board specific changes
- added PixFlamingo F7 board
- support ICM42688 on BlitzF745AIO
- fixed IMU orientation of CubeRedSecondary
- enable all FPV features on SpeedyBeeF405WING

2) System level changes

- improved robustness of CRSF parser
- reduced memory used by DDS/ROS2
- added filesystem crc32 binding in lua scripting
- support visual odometry quality metric and added autoswitching lua script
- allow for expansion of fence storage to microSD for larger pologon fences
- allow FTP upload of fence and rally points
- fixed vehicle type of ship simulation for ship landing
- make severity level depend on generator error level in IE 2400 generator
- speed up initial GPS probe by using SERIALn_BAUD first
- allow NanoRadar radar and proximity sensor to share the CAN bus
- added MR72 CAN proximity sensor
- only produce *_with_bl.hex not *.hex in builds if bootloader available
- fixed check for GPS antenna separation in moving baseline yaw
- added GPS_DRV_OPTIONS options for fully parsing RTCMv3 stream
- fixed logging of RTCM fragments in GPS driver
- fixed video recording while armed
- robostness and logging improvements for ExternalAHRS
- fixed RPM from bdshot on boards with IOMCU
- fixed accel cal simple to remove unused IMUs

3) Plane specific changes
- added support for precision landing of quadplanes
- fixed speed tuning of fixed wing aircraft without an airspeed sensor
- improved pitch control in high speed VTOL flight
- added TECS option for rapid descent in fixed wing aircraft

Release 4.5.0-beta2 14th February 2024
--------------------------------------

Changes from 4.5.0-beta1:

1) New Autopilots supported
 - YJUAV_A6Ultra
 - AnyLeaf H7

2) System level changes
 - fixed float rounding issue in HAL_Linux millis and micros functions
 - fixed loading of defaults.parm parameters for dynamic parameter subtrees
 - fixed discrimination between GHST and CRSF protocols
 - fixed bug in DroneCAN packet parsing for corrupt packets that could cause a crash
 - fixed handling of network sockets in scripting when used after close
 - fixed bit timing of CANFD buses

3) Plane specific changes
 - fixed handling of force arming when safety is enabled for VTOL motor state

4) Camera and gimbal enhancements
 - wait for non-zero camera version in SIYI driver

5) Miscellaneous
 - do relay parameter conversion for parachute parameters if ever has been used
 - broaden acceptance criteria for GPS yaw measurement for moving baseline yaw


Release 4.5.0-beta1 30th January 2024
-------------------------------------

Changes from 4.4.4

1) New autopilots supported
    - ACNS-F405AIO
    - Airvolute DCS2 onboard FMU
    - Aocoda-RC-H743Dual
    - BrainFPV RADIX 2 HD
    - CAN-Zero
    - CM4Pilot
    - CubeRed
    - Esp32-tomte76, esp32nick, esp32s3devkit
    - FlyingMoonH743
    - Flywoo F405 Pro
    - FlywooF405S-AIO with alternative IMUs
    - Here4 GPS as flight controller
    - Holybro 6X revision 6
    - Holybro6X-45686 with 3x ICM45686 IMUs
    - JAE JFB110
    - KakuteH7 using ICM42688
    - PixFlamingo (uses STM32L4PLUS CPU)
    - PixPilot-C3
    - PixSurveyA1-IND
    - QiotekAdeptF407
    - Sierra TrueNavIC
    - SPRacing H7RF
    - SW-Nav-F405
    - YJUAV_A6
    - YJUAV_A6SE, YJUAV_A6SE_H743 Plane
2) Autopilot specific changes
    - 1MB boards lose features to save flash (Payload Place, some battery monitors, NMEA Output, bootloaders, Turtle mode)
    - CubeOrangePlus supports IMU high resolution sampling (works with ICM42688, ICM42652, ICM42670, ICM45686 IMUs)
    - F4 processors with only 1 IMU gain Triple Harmonic Notch support
    - F765-SE bdshot support on 1st 4 pin
    - F7 and H7 boards lose DMA on I2C ports (not required, limited DMA better used elsewhere)
    - FlyingMoonH743, FlyingMoonF427 2nd and 3rd IMU order swapped
    - HEEWING-F405 supports CRSF
    - MatekL431-RC bootloader added, DMA used for RC and GPS
    - PH4-mini, PH4-mini-bdshot, Swan-K1 and TBS-Colibri-F7 BRD_SER4_RTSCTS param conflict fixed
    - Pixhawk6C supports BMI088 baro
    - TMotorH743, Heewing-F405 serial parameter definitions fixed
3) AHRS/EKF enhancements and fixes
    - AHRS_OPTIONS supports disabling fallback to DCM
    - BARO_ALT_OFFSET slews more slowly (was 20sec, now 1min)
    - EKF2 removed (can be re-enabled with Custom build server)
    - External AHRS support for multiple GPSs
    - InertialLabs INS-U external AHRS support
    - Lord external AHRS renamed to MicroStrain5
    - MAV_CMD_EXTERNAL_POSITION_ESTIMATE supports setting approximate position during dead-reckoning
    - Microstrain7 (aka 3DM-QG7) external AHRS support
4) Driver enhancements
    - 3DR Solo supports up to 6S batteries
    - Airspeed health checks vs GPS use 3D velocity
    - BDshot on the first four channels of boards with F103-based IOMCUs (e.g. Pixhawk 6X)
    - Dshot on all IOMCU channels on all boards with an IOMCU (e.g. all CubePilot autopilots)
    - Dshot commands (e.g. motor reversal abd beeps) and EDT supported on IOMCU
    - DroneCAN battery monitors calculate consumed energy if battery doesn't provide directly
    - DroneCAN RC and Ghost RC protocol support
    - EFI MAVLink driver
    - Extended DShot Telemetry support (requires BLHeli32 ver 32.10 or BlueJay, set SERVO_DSHOT_ESC=3 or 4)
    - GPS L5 band health override to enable L5 signal use (see GPS_DRV_OPTIONS)
    - GPS-for-yaw works at lower update rate (3hz minimum)
    - GSOF GPS supports GPS_COM_PORT parameter
    - Hirth ICEngine support
    - ICE option to enable/disable starting while disarmed
    - ICE support for starter via relay
    - IMUDATA serial protocol outputs raw IMU data on serial port (only available using custom build server)
    - Innomaker LD06 360deg lidar support
    - Intelligent Energy fuel cells new protocol support
    - IRC Tramp supports 1G3 bands A and B
    - IRC Ghost support
    - JAE JRE-30 radar
    - KDECAN driver rewrite (params moved to KDE_, works on all vehicles)
    - MCP9601 temperature sensor support
    - NanoRadar NRA24 rangefinder support
    - NeoPixelsRGB support
    - NoopLoop TOFSense, TOFSenseF-I2c rangefinder support
    - OSD shows flashing horizon when inverted
    - OSD2 support (e.g. second OSD)
    - QMC5883P compass support
    - Relay refactored to support RELAYx_FUNCTION, RELAY_STATUS message support added
    - Reventech fuel level support (extends existing analog driver, see BATT_FL_xx parameters)
    - RPLidarS1 360 deg lidar support and improved reliability for all RPLidars
    - SBF GPS supports yaw from dual antennas
    - Temperature sensor using analog voltages supported
    - Trimble PX-1 support added as a GPS
    - Winch driver enhancements including stuck protection, option for spin-free on startup
5) Control and navigation changes and enhancements
    - Auto missions can always be cleared while disarmed (would fail if mission still running)
    - DO_ENGINE_CONTROL allows starting engine even when disarmed
    - DO_SET_MISSION_CURRENT command can reset mission (see Reset Mission field)
    - DO_SET_SERVO, DO_REPEAT_SERVO work with servo outputs set to RCInxScaled
    - Fractional Loiter Turn Support in missions
    - HarmonicNotch supports up to 16 harmonics
    - JUMP command sends improved text msg to pilot (says where will jump to)
    - MAV_CMD_AIRFRAME_CONFIGURATION can control landing gear on all vehicles
    - MOT_OPTIONS allows voltage compensation to use raw battery voltages (instead of current corrected voltage)
    - PID controllers get DFF/D_FF (derivative feed-forward), NTF (target notch filter index) and NEF (error notch filter index)
    - PID controllers get PDMX param to limit P+D output (useful for large vehicles and/or slow actuators)
    - PID notch filter configured via new filter library using FILT parameters
    - VTOLs send ATTITUDE_TARGET messages to GCS
6) Plane specific enhancements
    - AUTOTUNE_OPTIONS allows disabling filter updates
    - Harmonic Notch frequencies can be logged at full rate
    - L1 controller checks heading and ground track agree to improve strong headwind edge case
    - Land airspeed default is halfway between min and cruise
    - LoiterAltQLand mode re-uses Loiter point if available
    - AUTO mode landing abort aux switch renamed
    - Q_M_SPOOL_TIM_DN allows slower spool down of motors
    - Quadplane use of forward throttle in VTOL modes improved
    - Tailsitters use motor I term for pitch control if no pitch surfaces are setup
    - Takeoff mode holds down elevator on taildraggers
    - Transition time may be no less than 2 seconds (see TRANSITION_MS)
    - VTOL angle controller gets feed-forward scaling (see Q_OPTIONS)
7) Parameters renamed and rescaled
    - COMPASS_TYPEMASK renamed to COMPASS_DISBLMSK
    - SYS_NUM_RESETS replaced by STAT_BOOTCNT
    - AIRSPEED_CRUISE replaces TRIM_ARSPD_CM
    - AIRSPEED_MIN replaces ARSPD_FBW_MIN
    - AIRSPEED_MAX replaces ARSPD_FBW_MAX
    - CRUISE_ALT_FLOOR replaces ALT_HOLD_FBWCM
    - LAND_FINAL_SPD replaces LAND_SPEED
    - LAND_PITCH_DEG replaces LAND_PITCH_CD
    - MIN_GROUNDSPEED replaces MIN_GNDSPD_CM
    - PTCH_LIM_MAX_DEG replaces LIM_PITCH_MAX
    - PTCH_LIM_MIN_DEG replaces LIM_PITCH_MIN
    - PTCH_TRIM_DEG replaces TRIM_PITCH_CD
    - Q_LAND_FINAL_SPD replaces Q_LAND_SPEED
    - Q_PILOT_ACCEL_Z replaces Q_ACCEL_Z
    - Q_PILOT_SPD_UP replaces Q_VELZ_MAX
    - Q_PILOT_SPD_DN replaces Q_VELZ_MAX_DN
    - ROLL_LIMIT_DEG replaces LIM_ROLL_CD
    - RTL_ALTITUDE replaces ALT_HOLD_RTL
8) ROS2 / DDS support
    - Added support for EProsima MicroXRCEDDS as a transport in SITL and hardware
    - Added sensor data topic support such as NavSatStatus and BatteryState
    - Added a new AP_ExternalControl library for generic control support in SI units
    - Added support for building ArduPilot with the colcon build system
    - Added high level goal interface (waypoints) similar to MAVLink global position
    - DDS topics comply with ROS REP-147
    - Added Gazebo Garden and Gazebo Harmonic simulation with examples
    - Added support for ROS 2 services such as Arm and Mode control
    - Added high level goal interface for waypoints
    - Wiki updated to support ROS 2
    - Added ROS 2 launch scripts for SITL, MAVProxy and micro-ROS agent
    - Add pytests for DDS client and ROS 2 launch scripts and integrate into CI
9) Camera and gimbal enhancements
    - Calculates location where camera gimbal is pointing (see CAMERA_FOV_STATUS)
    - CAMx_MNT_INST allows specifying which mount camera is in
    - Camera lens (e.g. live video stream) selectable using RC aux function
    - Interval timer (for taking pictures at timed intervals)
    - Image tracking support (ViewPro only)
    - MAVLink Gimbal Protocol v2 support for better GCS integration
    - MNTx_SYSID_DFLT allows easier pointing gimbal at another vehicle
    - MOUNT_CONTROL, MOUNT_CONFIGURE messages deprecated
    - RangeFinder support (only logged, only supported on Siyi, Viewpro)
    - Pilot's RC input re-takes manual control of gimbal (e.g. switches to RC_TARGETING mode)
    - Siyi driver gets Zoom control, sends autopilot attitude and time (reduces leans)
    - Video recording may start when armed (see CAMx_OPTIONS)
    - ViewPro driver (replaces equivalent Lua driver)
    - Xacti camera gimbal support
    - Zoom percentage support (for both missions and GCS commands)
10) Logging and reporting changes
    - Battery logging (e.g. BAT) includes health, temperature, state-of-health percentage
    - CAM and MNT messages contain camera gimbal's desired and actual angles
    - CTUN includes airspeed estimate type (e.g. sensor, EKF3 estimate)
    - INS_RAW_LOG_OPT allows raw, pre-filter and post-filter sensor data logging (alternative to "batch logging", good for filtering analysis)
    - PID logging gets reset and I-term-set flags
    - Rangefinder logging (e.g. RFND) includes signal quality
    - RC aux functions sorted alphabetically for GCS
    - RC logging (RCI, RCI2) include valid input and failsafe flags
    - RTK GPS logging includes number of fragments used or discarded
    - TEC2 provides extended TECS controller logging
    - TSIT provides tail sitter speed scaling values
11) Scripting enhancements
    - Autopilot reboot support
    - Baro, Compass, IMU, IOMCU health check support
    - Battery cell voltage bindings
    - Battery driver support
    - BattEsimate.lua applet estimates SoC from voltage
    - Camera and Mount bindings improved
    - CAN input packet filtering reduces work required by Lua CAN drivers
    - DJI RS2/RS3 gimbal driver supports latest DJI firmware version (see mount-djirs2-driver.lua)
    - EFI drivers for DLA serial, InnoFlight Inject EFI driver
    - EFI bindings improved
    - Fence support
    - Generator drivers for Halo6000, Zhuhai SVFFI
    - GCS failsafe support
    - Hobbywing_DataLink driver (see Hobbywing_DataLink.lua)
    - is_landing, is_taking_off bindings
    - led_on_a_switch.lua sets LED brightness from RC switch
    - MAVLink sending and receiving support
    - Mission jump_to_landing_sequence binding
    - mount-poi.lua upgraded to applet, sends better feedback, can lock onto Location
    - Networking/Ethernet support
    - Plane dual-aircraft synchronised aerobatics
    - Proximity driver support
    - Rangefinder drivers can support signal quality
    - revert_param.lua applet for quickly reverting params during tuning
    - RockBlock.lua applet supports setting mode, fix for battery voltage reporting
    - Serial/UART reading performance improvement using readstring binding
    - sport_aerobatics.lua rudder control fixed
    - Thread priority can be set using SCR_THD_PRIORITY (useful for Lua drivers)
    - Wind alignment and head_wind speed bindings
12) Safety related enhancements and fixes
    - Advanced GCS failsafe action to switch to Auto mode
    - Advanced GCS failsafe timeout configurable (see AFS_GCS_TIMEOUT)
    - Arm in AUTO/TAKEOFF modes only after stick returns to center
    - Arm/Disarmed GPIO may be disabled using BRD_OPTIONS
    - Arming allowed with Fence enabled but without a compass (previously failed)
    - Arming check of compass vs world magnetic model to detect metal in ground (see ARMING_MAGTHRESH)
    - Arming check of GPIO pin interrupt storm
    - Arming check of Lua script CRC
    - Arming check of mission loaded from SD card
    - Arming check of Relay pin conflicts
    - Arming check to allow Tricopter-Plane with no yaw servo
    - Arming check of emergency stop skipped if emergency stop aux function configured
    - Arming failures reported more quickly when changing from success to failed
    - ARMING_OPTIONS allows supressing "Armed", "Disarmed" text messages
    - BRD_SAFETY_MASK extended to apply to CAN ESCs and servos
    - Buzzer noise for gyro calibration and arming checks passed
    - FENCE_OPTIONS supports union OR intersection of all polygon fences
    - FLTMODE_GCSBLOCK blocks GCS from changing vehicle to specified modes
    - Long failsafe action to switch to Auto mode (see FS_LONG_ACTN)
    - Main loop lockup recovery by forcing mutex release (only helps if caused by software bug)
    - Parachute releases causes disarm and ICE shutoff
    - Rally points supports altitude frame (AMSL, Relative or Terrain)
    - RC failsafe does not trigger until RC has been received at least once
    - SERVO_RC_FS_MSK allows outputs using RC passthrough to move to SERVOx_TRIM on RC failsafe
    - Takeoff mode gets failsafe protections
13) System Enhancements
    - CAN port can support a second CAN protocol on the same bus (2nd protocol must be 11 bit, see CAN_Dn_PROTOCOL2)
    - CAN-FD support (allows faster data transfer rates)
    - Crash dump info logged if main thread locksup (helps with root cause analysis)
    - Ethernet/Networking support for UDP and TCP server and client (see NET_ENABLED) and PPP (see SERIALx_PROTOCOL)
    - Firmware flashing from SD card
    - Linux board SBUS input decoding made consistent with ChibiOS
    - Linux boards support DroneCAN
    - Parameter defaults stored in @ROMFS/defaults.parm
    - SD Card formatting supported on all boards
    - Second USB endpoint defaults to MAVLink (instead of SLCAN) except on CubePilot boards
    - Serial over DroneCAN (see CAN_D1_UC_SER_EN) useful for configuring F9P DroneCAN GPSs using uCenter
14) Custom Build server include/exclude features extended to include
    - APJ Tools
    - Bootloader flashing
    - Button
    - Compass calibration
    - DroneCAN GPS
    - ExternalAHRS (e.g. MicroStrain, Vectornav)
    - Generator
    - Highmark Servo
    - Hobbywing ESCs
    - Kill IMU
    - Payload Place
    - Plane BlackBox arming allows Plane to be used as logger (see ARMING_BBOX_SPD)
    - Plane's TX Tuning
    - Precision landing
    - Proximity sensor
    - RC Protocol
    - Relay
    - SBUS Output
    - ToneAlarm
    - Winch
15) Developer specific items
    - ChibiOS upgrade to 21.11
    - UAVCAN replaced with DroneCAN
    - AUTOPILOT_VERSION_REQUEST message deprecated (use REQUEST_MESSAGE instead)
    - PREFLIGHT_SET_SENSOR_OFFSETS support deprecated (was unused by all known ground stations)
    - MISSION_SET_CURRENT message deprecated (use DO_SET_MISSION_CURRENT command instead)
    - MISSION_CURRENT message sends num commands and stopped/paused/running/complete state
    - Python version requirement increased to 3.6.9
    - mavlink_parse.py shows all suported mavlink messages
    - COMMAND_INT messages can be used for nearly all commands (previously COMMAND_LONG)
16) Bug fixes:
    - 3DR Solo gimbal mavlink routing fixed
    - Airbrakes auxiliary function fixed
    - Airspeed health always checked before use (may not have been checked when using "affinity")
    - always ignore invalid pilot input throttle
    - Bootloop fixed if INS_GYRO_FILTER set too high
    - Button Internal Error caused by floating pin or slow device power-up fixed
    - CAN Compass order maintained even if compass powered up after autopilot
    - Compass device IDs only saved when calibrated to ensure external compasses appear as primary on new boards
    - Cruise mode locks in heading only once moving forwards (improves VTOL transition reliability in high winds)
    - Currawong ECU EFI does not send exhaust gas temperature
    - DO_REPOSITION interprets NaN as zero
    - DCM fallback in order to get better GPS is disabled if GPS is not used
    - DJI RS2/RS3 gimbal reported angle fix
    - DO_SET_ROI, ROI_LOCATION, ROI_NONE bug fix that could lead to gimbal pointing at old target
    - Fix throttle going bellow min in fbwa RC failsafe
    - Generator parameter init fix (defaults might not always have been loaded correctly)
    - GPS_TC_BLEND parameter removed (it was unused)
    - Ground speed undershoot correction during loss of GPS fixed
    - Guided mode heading control anti windup fix
    - Harmonic Notch gets protection against extremely low notch filter frequencies
    - Home altitude change while navigating handled correctly (previously led to sudden demanded height change)
    - IE 650/800 Generators report fuel remaining
    - INS calibration prevents unlikely case of two calibrations running at same time
    - LPS2XH Baro supported over I2C fixed
    - MatekH743 storage eeprom size fixed
    - MAVLink routing fix to avoid processing packet meant for another vehicle
    - Mount properly enforces user defined angle limits
    - MPU6500 IMU filter corrected to 4k
    - nav_roll (aka target roll) calculation improved
    - NMEA output time and altitude fixed
    - OSD gets labels for all supported serial protocols
    - OSD RF panel format fixed
    - Reset mission if in landing sequence while also disarmed and on the ground (avoids pre-arm check)
    - RTL_AUTOLAND with rally points fix (could skip climb to rally point's altitude)
    - RobotisServo initialisation fix
    - RPM accuracy and time wrap handling improved
    - Sagetech ADSB MXS altitude fix (needs amsl, was sending alt-above-terrain)
    - SageTechMXS ADSB climb rate direction fixed
    - SBUS out exactly matches SBUS in decoding
    - Serial port RTS pins default to pulldown (SiK radios could getting stuck in bootloader mode)
    - SERIALx_ parameters removed for ports that can't actually be used
    - Servo gimbal attitude reporting fix
    - Servo output fix when using scaled RC passthrough (e.g. SERVOx_FUNCTION = RCinXScaled)
    - Siyi continuous zoom stutter fixed
    - Siyi gimbal upside-down mode fixed (avoid bobbing if upside-down)
    - TECS's max deceleration scales properly with vehicle velocity
    - ST24 RC protocol fixed
    - STM32L496 CAN2 init fix (AP_Periph only?)
    - VFR_HUD climb rate reports best estimate during high vibration events (previously it would stop updating)
    - Visual Odometry healthy check fix in case of out-of-memory
    - VTX_MAX_POWER restored (allows setting radio's power)

Release 4.4.4 19th December 2023
--------------------------------

Changes from 4.4.3:

- CubeOrange Sim-on-hardware compilation fix
- RADIX2HD supports external I2C compasses
- SpeedyBeeF405v4 support
- DroneCAN battery monitor with cell monitor SoC reporting fix
- ProfiLED output fixed in both Notify and Scripting
- NTF_LED_TYPES parameter description fixed (was missing IS31FL3195)
- Scripting bug that could cause crash if parameters were added in flight
- STAT_BOOTCNT param fix (was not updating in some cases)
- don't query hobbywing DroneCAN ESC IDs while armed
- clamp empy version to prevent build errors

Release 4.4.3 14th November 2023
--------------------------------

Changes from 4.4.2:

 - fixed setup of ICM45486 IMU on CubeOrangePlus-BG edition
 - disable AFSR on IxM42xxx IMUs to prevent gyro bias for "stuck" gyros
 - fixed AK09916 compass being non-responsive
 - implement GPS_DRV_OPTION for using ellipsoid height in more GPS drivers
 - fixed SIYI AP_Mount parsing bug
 - configuration fixes for BETAFTP-F405 boards
 - fixed origin versus home relative bug in quadplane landing and guided takeoff
 - correct mavlink response for no airspeed sensor on preflight calibration
 - protect against notch filtering with uninitialised RPM source in ESC telemetry
 - allow lua scripts to populate full ESC telemetry data
 - added YJUAV_A6SE_H743 support
 - fixed uBlox M10 GPS support on boards with 1M flash

Release 4.4.2 23th October 2023
-------------------------------

Changes from 4.4.1

- BETAFPV-F405 support
- MambaF405v2 battery and serial setup corrected
- mRo Control Zero OEM H7 bdshot support
- SpeedyBee-F405-Wing gets VTX power control
- SpeedyBee-F405-Mini support
- T-Motor H743 Mini support
- EKF3 supports baroless boards
- INA battery monitor supports config of shunt resistor used (see BATTx_SHUNT)
- BMI088 IMU error value handling fixed to avoid occasional negative spike
- Dev environment CI autotest stability improvements
- OSD correct DisplayPort BF MSP symbols
- OSD option to correct direction arrows for BF font set
- Sensor status reporting to GCS fixed for baroless boards
- added opendroneid option to auto-store IDs in persistent flash
- fixed TECS bug that could cause inability to climb or descend
- fixed race condition when starting TECS controlled mode
- fixed RTL with rally point and terrain follow
- protect against invalid data in SBUS for first 4 channels
- added build type to VER message
- allow moving baseline rover at 3Hz
- use RC deadzones in stick mixing


Release 4.4.1 26th September 2023
---------------------------------

No changes from beta2

Release 4.4.1-beta2 12th September 2023
--------------------------------------

Changes from 4.4.1-beta1

- Airbotf4 features minimised to build for 4.4
- ChibiOS clock fix for 480Mhz H7 boards (affected FDCAN)
- H750 external flash optimisations for to lower CPU load
- MambaF405Mini fixes to match manufacturer's recommended wiring
- RADIX2 HD support
- RPI hardware version check fix
- YJUAV_A6SE support

Release 4.4.1-beta1 5th September 2023
--------------------------------------

Changes from 4.4.1

- support Himark DroneCAN servos
- support Hobbywing DroneCAN ESCs
- fixed control surface deflection on quadplanes in VTOL takeoff wait
- fixed bug in parameter default handling in SITL
- allow selection of mission sdcard storage on custom.ardupilot.org
- added support for SDMODELH7V1
- fixed battery monitor default for QiotekZealotF427 and QiotekZealotH743
- support 8 bit directional dshot channels on KakuteH7-wing
- improved handling of high vibration in EKF3 with new EK3_GLITCH_RADIUS options
- allow reset of battery SoC for DroneCAN battery monitors
- update GPIOs for Navigator board in HAL_Linux
- pull RTS lines low on Pixhawk6C on startup
- added log_file_content in scripting for aerobatics
- added asymmetry factor for skid steering on rovers
- updated defaults for luminousbee5 boards

Happy flying!

Release 4.4.0 18th August 2023
------------------------------

No changes from beta5

Release 4.4.0-beta5 11th August 2023
------------------------------------

Changes from 4.4.0-beta4

- fixed handling of missing DroneCAN airspeed packet
- fixed reset of target altitude in plane GUIDED mode
- added SIYI N7 flight controller
- fixed auto-enable of fence with forced arm
- fixed race condition that caused notch filter gyro glitches
- fixed bug with RTK injection for DroneCAN

Release 4.4.0 beta4
-------------------

Changes from 4.4.0-beta3

1) flight controller specific changes
    - Diatone-Mamba-MK4-H743v2 uses SPL06 baro (was DPS280)
    - DMA for I2C disabled on F7 and H7 boards
    - Foxeer H743v1 default serial protocol config fixes
    - HeeWing-F405 and F405v2 support
    - iFlight BlitzF7 support
2) Scripts may take action based on VTOL motor loss
3) Bug fixes
    - BLHeli returns battery status requested via MSP (avoids hang when using esc-configurator)
    - CRSFv3 rescans at baudrates to avoid RX loss
    - EK3_ABIAS_P_NSE param range fix
    - Scripting restart memory corruption bug fixed
    - Siyi A8/ZR10 driver fix to avoid crash if serial port not configured

Release 4.4.0 beta3
-------------------

This is the third beta of plane 4.4.0. This includes some important
fixes since beta2

1) flight controller specific changes
 - Holybro KakuteH7-Wing support
 - JFB100 external watchdog GPIO support added
 - Pixhawk1-bdshot support
 - Pixhawk6X-bdshot support
 - SpeedyBeeF4 loses bdshot support

3) Camera and Gimbal related changes
 - DO_SET_ROI_NONE command support added

4) Bug fixes
 - ADSB sensor loss of transceiver message less spammy
 - AutoTune Yaw rate max fixed
 - EKF vertical velocity reset fixed on loss of GPS
 - GPS pre-arm failure message clarified
 - SERVOx_PROTOCOL "SToRM32 Gimbal Serial" value renamed to "Gimbal" because also used by Siyi
 - SERIALx_OPTION "Swap" renamed to "SwapTXRX" for clarity
 - SBF GPS ellipsoid height fixed
 - Ublox M10S GPS auto configuration fixed
 - ZigZag mode user takeoff fixed (users could not takeoff in ZigZag mode previously)
 - fixed memory corruption bug with scripting restart

5) Device drivers
 - added LP5562 I2C LED driver
 - added IS31FL3195 LED driver

6) Applet changes
 - added QUIK_MAX_REDUCE parameter to VTOL quicktune lua applet

7) Plane specific changes
 - fixed takeoff mode to ensure climb to takeoff alt before turning
 - fixed error in quadplane wait for rudder neutral
 - improved handling of forward throttle during VTOL landing
 - fixed TECS state reset in VTOL auto modes
 - fixed early exit from loiter to alt
 - fixed display of started airspeed wait on forward transition


Release 4.4.0 beta2
-------------------

This is the second beta of plane 4.4.0. This includes some important
fixes since beta1.

The full list of changes is:

 - added SpeedyBeeF405WING, JSB100 and FoxeerH743
 - added LOG_DISARMED=3 support and LOG_DARM_RATEMAX
 - fixed error handling for being out of memory in EKF initialisation
 - fixed a bug in RC input handling on the IOMCU
 - fixed a bug handling ICE engine start after altitude reached
 - adjust EKF3 accel bias process noise for greater robustness
 - fixed an EKF3 bug in accel bias calculations
 - cope with compassmot impacting GSF yaw numerical stability
 - fixed an AUTOTUNE/QAUTOTUNE bug in yaw tuning
 - support INA228 and INA238 I2C battery monitors
 - always log rate PID slew limiters even when slew limit is zero
 - added MambaF4050v2 for new IMU, bdshot and DMA on UART1
 - update for FoxeerH743v1 GA release
 - reduced IMU init speed on MatekH743
 - move LED serial processing to its own thread
 - fixed parameter documentation for BRD_SAFETYOPTION
 - don't reject airspeed using EKF innovation if dead-reckoning
 - fixed USB pass-thru on 2nd USB endpoint

Release 4.3.7 31st May 2023
---------------------------

This stable release is for the 4.3.x stable series and is being done
because of a serious bug that has been found with RC input on boards
that use an IOMCU for RC input (boards with a separate set of 8 "main"
outputs from "aux" outputs).

The bug was that when RC input is lost and the receiver is one that
uses "no pulses" for loss of RC input then there is a chance that when
the RC link is regained that ArduPilot will not regain RC control and
will continue in RC failsafe.

The bug is an old one, first introduced in the 4.0.6 release in
September 2020. The bug does not occur often which is why it has been
such a long time before it was noticed. We would like to thank CUAV
for noticing and reporting the bug!

This release also has some other changes, some of which are to sync
with the Copter 4.3.6 release (which will go to 4.3.7 with this RC
input bug fix) and some are other bugs found since the 4.3.5 plane
release.

This release skips the 4.3.6 number to sync with copter.

The full list of changes is:

 - fixed a fault in the INS batch sampler code if you change the INS_LOG_BAT_CNT parameter without rebooting
 - fixed the RC input on IOMCU bug explained above
 - fixed a bug in ICE engine control if you do a "delay engine start" mission command while flying
 - added MCU voltage monitoring for the H757 microcontroller (eg. CubeOrangePlus)
 - servo gimbal mount yaw handling fix (only affects 3-axis servo gimbals)
 - PiccoloCAN fix for ESC voltage and current scaling
 - Gremsy gimbal fix when attached to autopilot's serial3 (or higher)
 - added CubeOrangePlus-bdshot build
 - fixed a bug in handling bad UART data in the megasquirt serial EFI driver
 - added -g option for configuring with debug symbols without full debug (helped with RCIN bug diagnosis)
 - fixed airmode switch for QACRO and QSTABILIZE modes
 - fixed a rare memory corruption bug in the STM32H757
 - fixed an EKF3 bug in accel bias calculations
 - adjust EKF3 accel bias process noise for greater robustness
 - cope with compassmot impacting GSF yaw numerical stability


Please test and report any issues!

Release 4.4.0 beta1
-------------------

This is the first beta of plane 4.4.0. It is a big release with lots
of changes. Many thanks to all the people who have contributed!

1) New autopilots supported
    - ESP32
    - Flywoo Goku F405S AIO
    - Foxeer H743v1
    - MambaF405-2022B
    - PixPilot-V3
    - PixSurveyA2
    - rFCU H743
    - ThePeach K1/R1

2) Autopilot specific changes
    - Bi-Directional DShot support for CubeOrangePlus-bdshot, CUAVNora+, MatekF405TE/VTOL-bdshot, MatekL431, Pixhawk6C-bdshot, QioTekZealotH743-bdshot
    - Bi-Directional DShot up to 8 channels on MatekH743
    - BlueRobotics Navigator supports baro on I2C bus 6
    - BMP280 baro only for BeastF7, KakuteF4, KakuteF7Mini, MambaF405, MatekF405, Omnibusf4 to reduce code size (aka "flash")
    - CSRF and Hott telemetry disabled by default on some low power boards (aka "minimised boards")
    - Foxeer Reaper F745 supports external compasses
    - OmnibusF4 support for BMI270 IMU
    - OmnibusF7V2-bdshot support removed
    - KakuteF7 regains displayport, frees up DMA from unused serial port
    - KakuteH7v2 gets second battery sensor
    - MambaH743v4 supports VTX
    - MatekF405-Wing supports InvensenseV3 IMUs
    - PixPilot-V6 heater enabled
    - Raspberry 64OS startup crash fixed
    - ReaperF745AIO serial protocol defaults fixed
    - SkystarsH7HD (non-bdshot) removed as users should always use -bdshot version
    - Skyviper loses many unnecessary features to save flash
    - UBlox GPS only for AtomRCF405NAVI, BeastF7, MatekF405, Omnibusf4 to reduce code size (aka "flash")
    - VRBrain-v52 and VRCore-v10 features reduced to save flash

3) Driver enhancements
    - ARK RTK GPS support
    - BMI088 IMU filtering and timing improved, ignores bad data
    - CRSF OSD may display disarmed state after flight mode (enabled using RC_OPTIONS)
    - Daiwa winch baud rate obeys SERIALx_BAUD parameter
    - EFI supports fuel pressure and ignition voltage reporting and battery failsafe
    - ICM45686 IMU support
    - ICM20602 uses fast reset instead of full reset on bad temperature sample (avoids occasional very high offset)
    - ICM45686 supports fast sampling
    - MAX31865 temp sensor support
    - MB85RS256TY-32k, PB85RS128C and PB85RS2MC FRAM support
    - MMC3416 compass orientation fix
    - MPPT battery monitor reliability improvements, enable/disable aux function and less spammy
    - Multiple USD-D1-CAN radar support
    - NMEA output rate configurable (see NMEA_RATE_MS)
    - NMEA output supports PASHR message (see NMEA_MSG_EN)
    - OSD supports average resting cell voltage (see OSD_ACRVOLT_xxx params)
    - Rockblock satellite modem support
    - Serial baud support for 2Mbps (only some hardware supports this speed)
    - SF45b lidar filtering reduced (allows detecting smaller obstacles
    - SmartAudio 2.0 learns all VTX power levels)
    - UAVCAN ESCs report error count using ESC Telemetry
    - Unicore GPS (e.g. UM982) support
    - VectorNav 100 external AHRS support
    - 5 IMUs supported

4) EKF related enhancements
    - Baro compensation using wind estimates works when climbing or descending (see BAROx_WCF_UP/DN)
    - External AHRS support for enabling only some sensors (e.g. IMU, Baro, Compass) see EAHRS_SENSORS
    - Magnetic field tables updated
    - Non-compass initial yaw alignment uses GPS course over GSF (mostly affects Planes and Rover)
    
5) Control and navigation enhancements 
    - AutoTune of attitude control yaw D gain (set AUTOTUNE_AXES=8)
    - DO_SET_ROI_NONE command turns off ROI
    - JUMP_TAG mission item support
    - Missions can be stored on SD card (see BRD_SD_MISSION)
    - NAV_SCRIPT_TIME command accepts floating point arguments
    - Pause/Resume returns success if mission is already paused or resumed
    - Payload Place support via lua script in quadplanes

7) Filtering enhancements
    - FFT notch can be run based on filtered data
    - Warn of motor noise at RPM frequency using FFT
    - In-flight FFT can better track low frequency noise
    - In-flight FFT logging improved
    - IMU data can be read and replayed for FFT analysis

8) Camera and gimbal enhancements
    - BMMCC support included in Servo driver
    - DJI RS2/RS3-Pro gimbal support
    - Dual camera support (see CAM2_TYPE)
    - Gimbal/Mount2 can be moved to retracted or neutral position
    - Gremsy ZIO support
    - IMAGE_START_CAPTURE, SET_CAMERA_ZOOM/FOCUS, VIDEO_START/STOP_CAPTURE command support
    - Parameters renamed and rescaled
        i) CAM_TRIGG_TYPE renamed to CAM1_TYPE and options have changed
        ii) CAM_DURATION renamed to CAM1_DURATION and scaled in seconds
        iii) CAM_FEEDBACK_PIN/POL renamed to CAM1_FEEBAK_PIN/POL
        iv) CAM_MIN_INTERVAL renamed to CAM1_INTRVAL_MIN and scaled in seconds
        v) CAM_TRIGG_DIST renamed to CAMx_TRIGG_DIST and accepts fractional values
    - RunCam2 4k support
    - ViewPro camera gimbal support

9) Logging changes
    - BARO msg includes 3-axis dynamic pressure useful for baro compensation of wind estimate
    - MCU log msg includes main CPU temp and voltage (was part of POWR message)
    - RCOut banner message always included in Logs
    - SCR message includes memory usage of all running scripts
    - CANS message includes CAN bus tx/rx statistics
    - OFCA (optical flow calibration log message) units added
    - Home location not logged to CMD message
    - MOTB message includes throttle output

10) Scripting enhancements
    - Generator throttle control example added
    - Heap max increased by allowing heap to be split across multiple underlying OS heaps
    - Hexsoon LEDs applet
    - Logging from scripts supports more formats
    - Parameters can be removed or reordered
    - Parameter description support (scripts must be in AP's applet or driver directory)
    - Rangefinder driver support
    - Runcam_on_arm applet starts recording when vehicle is armed
    - Safety switch, E-Stop and motor interlock support
    - Scripts can restart all scripts
    - Script_Controller applet supports inflight switching of active scripts

11) Custom build server enhancements
    - AIS support for displaying nearby boats can be included
    - Battery, Camera and Compass drivers can be included/excluded
    - EKF3 wind estimation can be included/excluded
    - PCA9685, ToshibaLED, PLAY_TUNE notify drivers can be included/excluded
    - RichenPower generator can be included/excluded
    - RC SRXL protocol can be excluded
    - SIRF GPSs can be included/excluded

12) Safety related enhancements and fixes
    - "EK3 sources require RangeFinder" pre-arm check fix when user only sets up 2nd rangefinder (e.g. 1st is disabled)
    - Pre-arm check that low and critical battery failsafe thresholds are different
    - Pre-arm message fixed if 2nd EKF core unhealthy
    - Pre-arm check if reboot required to enabled IMU batch sampling (used for vibe analysis)

13) Minor enhancements
    - Boot time reduced by improving parameter conversion efficiency
    - BRD_SAFETYENABLE parameter renamed to BRD_SAFETY_DEFLT
    - Compass calibration auxiliary switch function (set RCx_OPTION=171)
    - Disable IMU3 auxiliary switch function (set RCx_OPTION=110)
    - MAVFTP supports file renaming
    - MAVLink in-progress reply to some requests for calibration from GCS

14) Bug fixes:
    - ADSB telemetry and callsign fixes
    - Battery pct reported to GCS limited to 0% to 100% range
    - Bi-directional DShot fix on H7 boards after system time wrap (more complete fix than in 4.3.6)
    - DisplayPort OSD screen reliability improvement on heavily loaded OSDs especially F4 boards
    - DisplayPort OSD artificial horizon better matches actual horizon
    - EFI Serial MS bug fix to avoid possible infinite loop
    - EKF3 Replay fix when COMPASS_LEARN=3
    - ESC Telemetry external temp reporting fix
    - Fence upload works even if Auto mode is excluded from firmware
    - FMT messages logged even when Fence is exncluded from firmware (e.g. unselected when using custom build server)
    - Hardfault avoided if user changes INS_LOG_BAT_CNT while batch sampling running
    - ICM20649 temp sensor tolerate increased to avoid unnecessary FIFO reset
    - IMU detection bug fix to avoid duplicates
    - IMU temp cal fix when using auxiliary IMU
    - Message Interval fix for restoring default rate https://github.com/ArduPilot/ardupilot/pull/21947
    - RADIO_STATUS messages slow-down feature never completely stops messages from being sent
    - SERVOx_TRIM value output momentarily if SERVOx_FUNCTION is changed from Disabled to RCPassThru, RCIN1, etc.  Avoids momentary divide-by-zero
    - Scripting file system open fix
    - Scripting PWM source deletion crash fix
    - MAVFTP fix for low baudrates (4800 baud and lower)
    - ModalAI VOXL reset handling fix
    - MPU6500 IMU fast sampling rate to 4k (was 1K)
    - NMEA GPGGA output fixed for GPS quality, num sats and hdop
    - Position control reset avoided even with very uneven main loop rate due to high CPU load
    - Terrain offset increased from 15m to 30m (see TERRAIN_OFS_MAX) to reduce chance of "clamping"
    - Throttle notch FFT tuning param fix

15) Developer specific items
    - DroneCAN replaces UAVCAN
    - FlighAxis simulator rangefinder fixed
    - Scripts in applet and drivers directory checked using linter
    - Simulator supports main loop timing jitter (see SIM_TIME_JITTER)
    - Simulink model and init scripts
    - SITL on hardware support (useful to demo servos moving in response to simulated flight)
    - SITL parameter definitions added (some, not all)
    - Webots 2023a simulator support
    - XPlane support for wider range of aircraft

16) Plane specific changes
    - new aerobatics scripting system with flexible schedules
    - added plane-3d SITL model
    - added quadlane landing abort AUX switch
    - added TKOFF_GND_PITCH for taildragger takeoff
    - new ACRO_LOCKING=2 mode for quaternion locking with yaw rate controller
    - allow yaw rate autotune in modes other than AUTOTUNE
    - use a cone for QRTL climb close to home
    - added Y4 VTOL config for quadplanes
    - added throttle scaling for vectored yaw
    - added turn corrdination to yaw AUTOTUNE
    - added Q_OPTION for motor tilt when disarmed in fixed wing modes


Release 4.3.5 26th March  2023
------------------------------

- fixed 32 bit microsecond wrap in BDShot code

This release has a single bug fix for a critical bug for anyone using
bi-directional DShot on their vehicle. If using bi-directional DShot
and the vehicle is running its motors when 32 bit microsecond time
wraps at 71 minutes (or multiples of 71 minutes) then the bug that is
fixed in this release has an approximately 1 in 3 chance of causing
the motor to stop.

Note that the time is the time since boot, not the time since arming,
so even vehicles flying for a short time could be vulnerable if they
sit for a long time on the ground before takeoff.

Release 4.3.4 1st March  2023
-----------------------------

- support CubeOrangePlus BG edition
- enable VTX power on MambaH743v4
- probe external compasses on Foxeer Reaper F745
- fixed home update on bad GPS quality
- fixed GPS unconfigured error for non-M10 uBlox GPS
- don't allow RC protocol change on IOMCU once detected
- fixed FBWA pitch limits when in VTOL qassist
- fixed handling of zero compass diagonals
- added an output buffer to MAVCAN
- set emergency status in OpenDroneID on crash or chute deploy
- avoid logging duplicate format messages
- fixed bug in alt error arming check with BARO_FIELD_ELEV set
- fixed handling of double IOMCU reset and safety disable
- enable VTX power on MambaF405 2022
- disable PWMSource feature in lua scripts (will be back in 4.4.x)
- fixed throttle wait on rudder arming in quadplanes
- fixed earth frame accel compensation for AHRS_TRIM

Happy flying!

Release 4.3.3 Jan 19th 2023
---------------------------

 - AIRLink LTE module enable pin added
 - CUAV Nora/Nora+ bdshot firmware (allows Bi-directional DShot)
 - CubeOrange, CubeYellow gets fast reset of ICM20602
 - PixPilot-V6 support
 - Attitude and Navigation controllers use real-time dt (better handles variable or slow main loop)
 - Analog rangefinder GPIO pin arming check fixed
 - Arming check of AHRS/EKF vs GPS location disabled if GPS disabled
 - Position Controller limit handling improved to avoid overshooting and hard landings
 - PSC_ANGLE_MAX param reduction causing WPNAV_ACCEL to be set too low fixed
 - Servo gimbal yaw jump to opposite side fixed
 - Siyi A8 gimbal driver's record video feature fixed
 - SToRM32 serial gimbal driver actual angle reporting fixed (pitch and yaw angle signs were reversed)
 - Takeoff in Auto, Guided fixed when target altitude is current altitude
 - Takeoff in Auto handles baro drift before takeoff
 - Takeoff twitch due to velocity integrator init bug fixed
 - moved FTP MAVLink transfers to FTP thread and better control bandwidth
 - switch to QRTL if inside RTL radius when using Q_RTL_MODE=3 (approach)
 - check for 3 good frames for CRSF
 - allow for ELRS at 420kbaud
 - support MambaH743-v2
 - support MambaF405-2022B
 - fixed nullptr checks on new
 - fixed a bug in terrain handling for ship landing lua script checks on new

Happy flying!

Release 4.3.2 Dec 23rd 2022
---------------------------

This is a minor release with bug fixes for the 4.3.x release series.

Changes from 4.3.1:

 - improved uBlox M10 support
 - CubeOrange defaults to using 2nd IMU as primary
 - SIRF and SBP GPS disabled on BeastF7v2, MatekF405-STD, MAtekF405-Wing, omnibusf4pro
 - fixed loading of autotune gains during pilot testing
 - Fixed CAM_MIN_INTERVAL to cope with mission and pilot triggering
 - EKF3 fix when using EK3_RNG_USE_HGT/SPD params and rangefinder provides bad readings
 - Main loop slowdown after arming fixed (parameter logging was causing delays)
 - changed to 'fast task' scheme for critical loop updates
 - MAVLink commands received on private channels checked for valid target sysid
 - ModalAI camera support fixed (ODOMETRY message frame was consumed incorrectly)
 - Param reset after firmware load fixed on several boards
 - Siyi A8 gimbal support fixed
 - Windows builds move to compiling only 64-bit executables
 - ARKV6X support
 - MatekH743 supports 8 bi-directional dshot channels
 - Pixhawk1 boards support MS5607 baros
 - SpeedbyBee F405v3 support
 - DroneCAN Airspeed sensor support including hygrometer readings
 - Pre-arm warning if multiple UARTs with SERIALx_PROTOCOL = RCIN
 - Siyi gimbal support
 - Arm check warning loses duplicate "AHRS" prefix
 - AtomRCF405NAVI bootloader file name fixed
 - BRD_SAFETY_MASK fixed on boards with both FMU safety switch and IOMCU
 - Compass calibration continues even if a single compass's cal fails
 - Gremsy gimbal driver sends autopilot info at lower rate to save bandwidth
 - Invensense 42605 and 42609 IMUs use anti-aliasing filter and notch filter
 - OSD stats screen fix
 - RC input on serial port uses first UART with SERIALx_PROTOCOL = 23 (was using last)
 - RunCam caching fix with enablement and setup on 3-pos switch
 - RTK CAN GPS fix when GPSs conneted to separate CAN ports on autopilot
 - fixed yaw rate for fixed wing autotune

Release 4.3.1 24th Oct 2022
---------------------------

This is a minor release with some important fixes:

 - fixed build with gcc 11.3
 - fixed random number generator in lua core
 - scale VTOL angle P with airspeed in quadplane back-transiton
 - added support for implementing AUX functions in lua scripts
 - fixed BMI085 accel scaling
 - fixed KSXT NMEA parsing affecting position resolution
 - fixed race condition in TECS control leading to 'nod' in forward transiton
 - allow for expansion of notch filters to fix notch of fwd motors in quadplanes
 - added logging of TECS target alt
 - fixed EKF3 altitude discrepancy with GPS or baro alt change on startup
 - allow auto mode sequencing to land in a fence breach

Happy flying!

Release 4.3.0 9th Oct 2022
--------------------------

The is the first 4.3.x stable release for plane. It is a major release
with a lot of changes since 4.2.3.

Changes since 4.3.0beta3:

 - added 1M and 2M flash warning checks for for fmuv2, fmuv3 and Pixhawk1-1M firmwares
 - added support for multi-byte i2c reads in scripting

The change list from 4.2.3 stable is very long. Here are the main changes:

 - fixed BRD_SAFETY_MASK for enabling outputs when safety on
 - fixed persistence of mapping of CAN airspeed sensors to instances
 - fixed precision of NMEA serial output function
 - added report of "Engine Running" when using ICE
 - fixed handling of defaults.parm files with lines over 100 chars
 - fixed handling of defaults.parm files with no newline on last line
 - fixed possible divide by zero when changing to GUIDED on quadplanes
 - fixes for VideoTX, fixing buffer overrun and tramp handling
 - fixed spurious error about sending RPM when RPM disabled
 - fixed an EKF3 lane switch issue that can cause incorrect height with dual GPS
 - fixed mission cmd to mission int initialisation error
 - fixed mission jump tracking init on startup
 - fixed OSD view roll/pitch error for tailsitters
 - added SkystarsH7HD-bdshot
 - fixed SkystarsH7HD VTX control
 - reduced memory usage on MatekF405-CAN board
 - disable SLCAN when armed to reduce CPU load
 - enable CAN battery mon on CUAV V6X by default
 - added arming check for Q_M_SPIN_MIN value too high
 - fixed reporting of RPM from harmonic notch
 - improved handling of airspeed errors and airspeed auto disable
 - fixed SERVO_AUTO_TRIM for multiple outputs of same type
 - fixed auto baud rate detection on SBF/GSOF/NOVA GPS
 - increased max board name length for mavlink statustext to 23
 - fixed incorrect disable of notches for non-throttle notch
 - added notch filter slew limit to reduce notch errors
 - added ARMING_OPTIONS to control display of pre-arm errors
 - several OSD fixes for params, font and resolution
 - support PWM type transmission for CAN PWM output
 - support Currawong ECU as EFI backend
 - support lua scripts for EFI backends
 - implement SkyPower and HFE CAN EFI lua scripts
 - improved speed of log download with dataflash block backends
 - disabled all GPS drivers except uBlox and NMEA on Pixhawk1-1M to save flash
 - disabled all GPS drivers except uBlox on MatekF405-bdshot and omnibusf4pro-bdshot
 - fixed FFT indexing bug
 - added USART2 for AIRLink
 - allow reset to default airspeed using mission item DO_CHANGE_SPEED
 - added new boards AtomRCF405, KakuteH7Mini-Nand, SkystarsH7HD
 - added bi-directional dshot for several new boards
 - EK3_GPS_VACC_MAX threshold to control when GPS altitude is used as alt source
 - EKF ring buffer fix for slow sensor updates
 - EKF3 source set change captured in replay logs
 - numerous gimbal support improvements
 - improved RemoteId support
 - SecureBoot support with remote update of secure boot public keys
 - crash_dump.bin file saved to SD Card on startup (includes details re cause of software failures)
 - several new pre-arm checks (AHRS type, scripts, terrain)
 - numerous scripting improvements
 - fixed scripting restart leaking memory
 - Benewake H30 radar support
 - BMI270 IMU performance improvements
 - Logging pause with auxiliary switch
 - TeraRanger Neo rangefinder support
 - support for both AMSL and ellipsoid height in most GPS drivers
 - Custom controller support
 - parameter defaults sent with param FTP and onboard logs
 - Sim on Hardware allows simulator to run on autopilot
 - added Q_LAND_ALTCHG parameter
 - added climb before QRTL for safer QRTL from low altitudes
 - added support for logging pre and post filtered FFT data
 - support triple-notch harmonic notch filter
 - support up to 32 actuators (with SERVO_32_ENABLE parameter)
 - support EFI input over DroneCAN
 - by default only run notch filter on first IMU
 - added ESC_TLM_MAV_OFS parameter for mapping ESCs to MAVLink ESC telemetry
 - added Q_NAVALT_MIN for quadplane takeoff
 - added ICE redline governor
 - added in-flight FFT notch tuning option
 - added Sagetech ADSB support
 - added INS_HNTCH_FM_RAT parameter for handling under-hover throttle
 - improvements to filtering on ICM42xxx IMUs
 - added option parameters to NAV_VTOL_LAND mission item for fixed wing approach

Many thanks to the huge number of developers, testers and documenters
who contributed to the 4.3.0 release!

Special thanks to all the ArduPilot Parters who have worked closely
with us on the 4.3.x development and testing, with many partners
contributing suggestions or supporting particular features.

Release 4.3.0beta3 7th Oct 2022
-------------------------------

This is the third beta of the 4.3.0 stable release. Changes since
beta1 are:

 - fixed BRD_SAFETY_MASK for enabling outputs when safety on
 - fixed persistence of mapping of CAN airspeed sensors to instances
 - fixed precision of NMEA serial output function
 - added report of "Engine Running" when using ICE
 - fixed handling of defaults.parm files with lines over 100 chars
 - fixed handling of defaults.parm files with no newline on last line
 - fixed possible divide by zero when changing to GUIDED on quadplanes

Happy flying!

Release 4.3.0beta2 3rd Oct 2022
-------------------------------

This is the second beta of the 4.3.0 stable release. Changes since
beta1 are:

 - fixes for VideoTX, fixing buffer overrun and tramp handling
 - fixed spurious error about sending RPM when RPM disabled
 - fixed an EKF3 lane switch issue that can cause incorrect height with dual GPS
 - fixed mission cmd to mission int initialisation error
 - fixed mission jump tracking init on startup
 - fixed OSD view roll/pitch error for tailsitters
 - added SkystarsH7HD-bdshot
 - fixed SkystarsH7HD VTX control
 - reduced memory usage on MatekF405-CAN board
 - disable SLCAN when armed to reduce CPU load
 - enable CAN battery mon on CUAV V6X by default
 - added arming check for Q_M_SPIN_MIN value too high
 - fixed reporting of RPM from harmonic notch
 - improved handling of airspeed errors and airspeed auto disable
 - fixed SERVO_AUTO_TRIM for multiple outputs of same type
 - fixed auto baud rate detection on SBF/GSOF/NOVA GPS
 - increased max board name length for mavlink statustext to 23
 - fixed incorrect disable of notches for non-throttle notch
 - added notch filter slew limit to reduce notch errors
 - added ARMING_OPTIONS to control display of pre-arm errors
 - several OSD fixes for params, font and resolution
 - support PWM type transmission for CAN PWM output
 - support Currawong ECU as EFI backend
 - support lua scripts for EFI backends
 - implement SkyPower and HFE CAN EFI lua scripts
 - improved speed of log download with dataflash block backends
 - disabled all GPS drivers except uBlox and NMEA on Pixhawk1-1M to save flash
 - disabled all GPS drivers except uBlox on MatekF405-bdshot and omnibusf4pro-bdshot
 - fixed FFT indexing bug
 - added USART2 for AIRLink
 - allow reset to default airspeed using mission item DO_CHANGE_SPEED

Happy flying!


Release 4.3.0beta1 13th Sep 2022
--------------------------------

This is the first beta of the 4.3.0 stable release. There are a lot of
changes since the 4.2.3 stable release. Key changes are:

- added new boards AtomRCF405, KakuteH7Mini-Nand, SkystarsH7HD
- added bi-directional dshot for several new boards
- EK3_GPS_VACC_MAX threshold to control when GPS altitude is used as alt source
- EKF ring buffer fix for slow sensor updates
- EKF3 source set change captured in replay logs
- numerous gimbal support improvements
- improved RemoteId support
- SecureBoot support with remote update of secure boot public keys
- crash_dump.bin file saved to SD Card on startup (includes details re cause of software failures)
- several new pre-arm checks (AHRS type, scripts, terrain)
- numerous scripting improvements
- fixed scripting restart leaking memory
- Benewake H30 radar support
- BMI270 IMU performance improvements
- Logging pause with auxiliary switch
- TeraRanger Neo rangefinder support
- support for both AMSL and ellipsoid height in most GPS drivers
- Custom controller support
- parameter defaults sent with param FTP and onboard logs
- Sim on Hardware allows simulator to run on autopilot
- added Q_LAND_ALTCHG parameter
- added climb before QRTL for safer QRTL from low altitudes
- added support for logging pre and post filtered FFT data
- support triple-notch harmonic notch filter
- support up to 32 actuators (with SERVO_32_ENABLE parameter)
- support EFI input over DroneCAN
- by default only run notch filter on first IMU
- added ESC_TLM_MAV_OFS parameter for mapping ESCs to MAVLink ESC telemetry
- added Q_NAVALT_MIN for quadplane takeoff
- added ICE redline governor
- added in-flight FFT notch tuning option
- added Sagetech ADSB support
- added INS_HNTCH_FM_RAT parameter for handling under-hover throttle
- improvements to filtering on ICM42xxx IMUs
- added option parameters to NAV_VTOL_LAND mission item for fixed wing approach

Please report flight tests of the 4.3.0beta series on discuss.ardupilot.org

Happy flying!

Release 4.2.3 21st August 2022
------------------------------

This is a minor stable release with a few new features and bug
fixes. The changes from 4.2.0 are:

- OpenDroneID improvements
- added --enable-opendroneid configure option
- added --enable-check-firmware configure option
- enable OSD menus on KakuteH7
- added prearm checks for rangefinder pin conflicts
- added diagnostics for scurve internal error
- allow absolute paths for linux boards in param defaults
- fixed AIRBRAKE rc option warning
- fixed notch filtering ordering issue on loss of RPM source
- fixed Lutan EFI update serial flood
- fixed --upload to work on WSL2
- allow INA2xx battery to init after startup
- fixed healthy check on battery monitors to check all enabled monitors
- added Pixhawk6C and Pixhawk6X support
- fixed alighment of QRTL start in fixed wing circle landing approach
- added Foxeer Reaper F745 support
- added MFE PixSurveyA1 support
- fixed combination of waypoint passby with acceptance distance
- cut throttle on ICE stop when armed
- added ICE option for starting when disarmed
- zero VFWD integrator on ICE override in quadplanes
- don't failsafe when in fixed wing landing sequence with RTL_AUTOLAND
- improved handling of overshoot in VTOL landing
- improved choice of target airspeed in VTOL landing approach
- improved ICM42xxx filter settings
- allow for faster sample rates on ICM42xxx

Release 4.2.3beta3 19th August 2022
-----------------------------------

This is a minor stable release with a few new features and bug
fixes. The changes from beta1 are:

- OpenDroneID improvements
- added --enable-opendroneid configure option
- added --enable-check-firmware configure option
- reverted notch filter changes from beta3 due to issue on some aircraft
- enable OSD menus on KakuteH7
- added prearm checks for rangefinder pin conflicts
- added diagnostics for scurve internal error
- allow absolute paths for linux boards in param defaults
- fixed AIRBRAKE rc option warning


Release 4.2.3beta2 10th August 2022
-----------------------------------

This is a minor stable release with a few new features and bug
fixes. The changes from beta1 are:

- OpenDroneID support
- fixed notch filtering ordering issue on loss of RPM source
- fixed Lutan EFI update serial flood
- fixed --upload to work on WSL2

Release 4.2.3beta1 2nd August 2022
----------------------------------

This is a minor stable release with a few new features and bug
fixes. The changes are:

- allow INA2xx battery to init after startup
- fixed healthy check on battery monitors to check all enabled monitors
- added Pixhawk6C and Pixhawk6X support
- fixed alighment of QRTL start in fixed wing circle landing approach
- added Foxeer Reaper F745 support
- added MFE PixSurveyA1 support
- fixed combination of waypoint passby with acceptance distance
- cut throttle on ICE stop when armed
- added ICE option for starting when disarmed
- zero VFWD integrator on ICE override in quadplanes
- don't failsafe when in fixed wing landing sequence with RTL_AUTOLAND
- improved handling of overshoot in VTOL landing
- improved choice of target airspeed in VTOL landing approach
- improved ICM42xxx filter settings
- allow for faster sample rates on ICM42xxx



Release 4.2.2 24th June 2022
----------------------------

This is a minor stable release with a few new features and bug
fixes. Just one change since beta1. The changes are:

 - support two full harmonic notches using the INS_HNTC2_ parameters
 - adjust neopixel bitwidths for better reliability
 - fixed EKF3 replay bugs that caused poor replay fidelity
 - added BLHeli_S ESC type in SERVO_DSHOT_ESC
 - reduced min lean angle for alt-hold in quadplanes to 5 degrees
 - fixed param ftp run length encoding bug
 - reduced default quadplane rate accelerations and XY position gains
 - improved parameter checks of Q_M_PWM parameters for bad conversions
 - added Q_NAVALT_MIN parameter for min alt to start attitude control in takeoff
 - added CAN_Dn_UC_POOL parameter to control DroneCAN pool size, to allow memory saving on F4 boards
 - saved hardfault crash dumps to microSD if detected
 - fixed PWM rangefinder bug and support SCALING parameter
 - updated OSD flight modes menu for newer modes
 - preserve new rangefinder addresses on VL53L1X
 - protect against hardfault on bad CRSF frames
 - handle reset of CRSF receiver in flight
 - added MambaH743v4 and MambaF405 MK4
 - fixed fault on FFT init with ARMING_REQUIRE=0
 - synced quicktune lua script with latest version
 - learn MAVLink routes on private channels
 - fixed throttle compensation with FWD_BAT_VOLT_MAX at low voltages

Happy flying!

Release 4.2.2beta1 16th June 2022
---------------------------------

This is a minor stable release with a few new features and bug
fixes. The changes are:

 - support two full harmonic notches using the INS_HNTC2_ parameters
 - adjust neopixel bitwidths for better reliability
 - fixed EKF3 replay bugs that caused poor replay fidelity
 - added BLHeli_S ESC type in SERVO_DSHOT_ESC
 - reduced min lean angle for alt-hold in quadplanes to 5 degrees
 - fixed param ftp run length encoding bug
 - reduced default quadplane rate accelerations and XY position gains
 - improved parameter checks of Q_M_PWM parameters for bad conversions
 - added Q_NAVALT_MIN parameter for min alt to start attitude control in takeoff
 - added CAN_Dn_UC_POOL parameter to control DroneCAN pool size, to allow memory saving on F4 boards
 - saved hardfault crash dumps to microSD if detected
 - fixed PWM rangefinder bug and support SCALING parameter
 - updated OSD flight modes menu for newer modes
 - preserve new rangefinder addresses on VL53L1X
 - protect against hardfault on bad CRSF frames
 - handle reset of CRSF receiver in flight
 - added MambaH743v4 and MambaF405 MK4
 - fixed fault on FFT init with ARMING_REQUIRE=0
 - synced quicktune lua script with latest version
 - learn MAVLink routes on private channels

Happy flying!

Release 4.2.1 23rd May 2022
---------------------------

This is a minor release on top of the 4.2 firmware. It has a number of
bug fixes and safety improvements. The changes are:

- fixed a bug in handling wrap in log download when 500 logs are on the microSD card
- added FlyingMoonF407 and FlyingMoonF427 support
- fixed RSSI input on the RCIN pin on CubeBlack and CubeOrange
- fixed update of gyro FFT throttle tracking
- removed unhealthy reporting on airspeed sensors with negative pressure as this commonly happens on VTOL takeoff
- improved handling of large LOITER_TURNS mission items, expanding maximum to 2.5km
- added fuel usage integration on Lutan EFI
- refuse arming in AUTO when in a landing sequence due to a failsafe
- account for sprung throttle (from RC_OPTIONS) in throttle suppression code
- improved safety of in-flight compass learning
- fixed automatic airspeed ratio calibration for 2nd airspeed sensor
- increase safety of GUIDED -> AUTO takeoff sequence used by QGC
- added CAN_Dx_UC_ESC_OF parameter for ESC offset for bandwidth efficiency
- fixed handling of safety state on boards with no safety switch
- fixed bug where a false positive could trigger landing disarm while land repositioning is active

The most important fixes are related to how QGC (which is often used
on modern GCS style transmitters) does automatic takeoff with the
"swipe right for takeoff" system. QGC does a sequence of a change to
GUIDED mode, then arming, then change to AUTO mode. That sequence is
dangerous if the current waypoint is not properly reset to a VTOL
takeoff. To fix this a number of changes were made to ensure that
takeoff is done with a valid VTOL takeoff. The changes include
handling of failsafe events (such as GCS or RC failsafe) part way
through this takeoff sequence. If QRTL or RTL is engaged during this
sequence then the vehicle will switch to QLAND.

Happy flying!


Release 4.2.1beta1 19th May 2022
--------------------------------

This is a minor release on top of the 4.2 firmware. It has a number of
bug fixes and safety improvements. The changes are:

- fixed a bug in handling wrap in log download when 500 logs are on the microSD card
- added FlyingMoonF407 and FlyingMoonF427 support
- fixed RSSI input on the RCIN pin on CubeBlack and CubeOrange
- fixed update of gyro FFT throttle tracking
- removed unhealthy reporting on airspeed sensors with negative pressure as this commonly happens on VTOL takeoff
- improved handling of large LOITER_TURNS mission items, expanding maximum to 2.5km
- added fuel usage integration on Lutan EFI
- refuse arming in AUTO when in a landing sequence due to a failsafe
- account for sprung throttle (from RC_OPTIONS) in throttle suppression code
- improved safety of in-flight compass learning
- fixed automatic airspeed ratio calibration for 2nd airspeed sensor
- increase safety of GUIDED -> AUTO takeoff sequence used by QGC
- added CAN_Dx_UC_ESC_OF parameter for ESC offset for bandwidth efficiency

The most important fixes are related to how QGC (which is often used
on modern GCS style transmitters) does automatic takeoff with the
"swipe right for takeoff" system. QGC does a sequence of a change to
GUIDED mode, then arming, then change to AUTO mode. That sequence is
dangerous if the current waypoint is not properly reset to a VTOL
takeoff. To fix this a number of changes were made to ensure that
takeoff is done with a valid VTOL takeoff. The changes include
handling of failsafe events (such as GCS or RC failsafe) part way
through this takeoff sequence. If QRTL or RTL is engaged during this
sequence then the vehicle will switch to QLAND.

Happy flying!

Release 4.2.0 4th May 2022
--------------------------

This is the first 4.2 stable release of plane. Since 4.2.0beta6 there
have only been 3 changes:

 - added VTOL-quicktune lua script
 - fixed custom compass orientation for DroneCAN compasses
 - fixed a bug in blended Z accel calculation when not using first IMU

Since the last stable 4.1.7 release there have been over 4k changes,
so it is not practical to list them all here. Highlights for plane
users include:

 - smoother quadplane transitions
 - ship landing support
 - CAN over mavlink support
 - major tailsitter improvements
 - much better error reporting and diagnostics
 - numerous lua scripting improvements
 - improved weathervaning support and options
 - support for fixed wing aerobatics
 - many new flight controllers added
 - DroneCAN dual-GPS for yaw support

Happy flying!


Release 4.2.0beta6 28th April 2022
----------------------------------

This is the 6th beta of the 4.2.0 major release

 - fix FIFO overruns on the BMI088 IMU
 - fix plane avoidance behaviour when in AvoidADSB mode
 - disable fatal exceptions on DMA errors on STM32
 - changed quadplane weathervaning to make pitch input optional for nose-in and tail-in
 - increased default scripting heap size on F7/H7 to 100k
 - added ICM42688 option on MatekH743
 - added ICM4xxxx option on QiotekZealotH743
 - rename INS_NOTCH parameters to INS_HNTC2
 - added Q_OPTION for RTL on failsafe in Q modes, for ship operation
 - disabled setting of highest airspeed when disarmed
 - fixed init of file descriptor in logging
 - cope with gaps in log listing, and reduce impact of log listing on EKF and ESCs
 - avoided repeated message on "failed to detach from pin" for RPM
 - improved error messages on GPIO pin configuration conflict
 - use narrower bitwidths for dshot, giving more accurate timing
 - added MatekF765-Wing-bdshot target
 - fixed use of DO_SET_SERVO with SERVOn_FUNCTION=0
 - several improvements to SPRacingH7 support

Happy flying!

Release 4.2.0beta5 5th April 2022
---------------------------------

This is the 5th beta of the 4.2.0 major release

 - fixed a timer bug that could cause boards using flash storage to watchdog
 - added OTG2 USB on QioTekZealotH743
 - increased stack size on log and monitor threads
 - fixed rudder control when ARMING_RUDDER != 2 on quadplanes
 - fixed accel bias when an IMU is disabled in EKF3
 - improved QSPI support for SPro H7 Extreme
 - fixed @SYS file logging
 - fixed buzzer on MatekH743, going back to 16 bit timer
 - fixed H7 flash storage bug that caused re-init on overflow
 - fixed incorrect lock class in UART driver

Happy flying!

Release 4.2.0beta4 28th March 2022
----------------------------------

This is the 4th beta of the 4.2.0 major release.

 - added BATT_OPTIONS option to send resting voltage (corrected for internal resistance) to the ground station
 - fixed a bug when a blended GPS is lost, where the wrong GPS could be used for a short time
 - prevent rapid RTL/AUTO switching with DO_LAND_START and fence breach
 - added RTL_AUTOLAND=3 to prevent arming check about DO_LAND_START with no RTL_AUTOLAND
 - fixed yaw in AUTO mode on the ground on quadplanes when using rudder to disarm
 - fixed failover between IOMCU RC input and a secondary RC input on a uart
 - display source of RC input with protocol
 - fixed DShot reversal bugs with IOMCU based boards
 - fixed battery remaining percentage with sum battery backend
 - added KakuteH7-bdshot
 - added terrain reference adjustment and TERRAIN_OFS_MAX parameter
 - fixed param conversion bug (impacts airspeed enable)
 - changed MatekH743 to use a 32 bit timer

Happy flying!

Release 4.2.0beta3 18th March 2022
----------------------------------

This is the 3rd beta of the 4.2.0 major release. This beta should be
close to the final 4.2.0 release.

 - fixed pitch envelope constraint after AIRBRAKE
 - improvements to POSITION1 quadplane landing controller
 - added arming check for Q_ASSIST_SPEED
 - added warning if arming with ARMING_CHECK=0
 - added arming check for DO_LAND_START with RTL_AUTOLAND=0
 - improved throttle mix for quadplane autoland
 - added fence breach message on GCS
 - constrain indexing on declination tables

Happy flying!

Release 4.2.0beta2 10th March 2022
----------------------------------

This is the 2nd beta of the 4.2.0 major release. This beta should be
close to the final 4.2.0 release.

 - fixed EKF3 constrain bug
 - added quadplane ship landing support
 - added Q_LAND_ALTCHG to adjust landing detection sensitivity
 - fixed a bug in terrain home compensation
 - changed quaplane POSITION1 landing controller to use jerk and accel limiting
 - fixed default gain for weathervaning on quadplanes
 - fixed health check of airspeed sensor for dual sensors
 - fixed arming check on servos for GPIO outputs
 - disable stick mixing when RC input is unavailable
 - fixed stick mixing during GCS failsafe
 - disallow mavlink disarm while flying
 - adjust frequency selection for DShot
 - fixed logging bug for MCU voltage
 - added BeastH7v2 support
 - improvement to AC_WPNav for quadplane waypoint control
 - simplify RC failsafe messages sent to GCS

Happy flying!

Release 4.2.0beta1 2nd March 2022
---------------------------------

This is a major release with a lot of changes. We expect at least 4
weeks of beta testing and several beta updates before 4.2.0 final is
released.

Changes include:

 - EKF startup messages reduced
 - LORD Microstrain CX5/GX5 external AHRS support
 - Auto mode supports up to 100 DO_JUMP commands on high memory boards
 - Auto support for NAV_SCRIPT_TIME commands (Lua within Auto)
 - aerobatic scripting from any mode via RC switches
 - new boards AirLink, BeastF7v2, BeastH7v2, JHEMCU GSF405A, KakuteH7, KakuteH7Mini, MambaF405US-I2C, MatekF405-TE, ModalAI fc-v1, PixC4-Jetson, Pixhawk5X, QioTekZealotH743, RPI-Zero2W, SPRacingH7 Extreme, Swan-K1
 - Parachute option to leave servo in open position (see CHUTE_OPTIONS parameter)
 - Parachute released arming check added
 - Pre-arm check of IMU heater temp
 - Pre-arm check of rangefinder health
 - Pre-arm check of throttle position skipped if PILOT_THR_BHV is "Feedback from mid stick"
 - ADIS16470, ADIS16507 and BMI270 IMU support
 - AK09918 compass support
 - Battery monitor supports voltage offset (see BATTx_VLT_OFFSET)
 - Benewake TFMiniPlus I2C address defaults correctly
 - Buzzer can be connected to any PWM on any board
 - Compass calibration (in-flight) uses GSF for better accuracy
 - CRSFv3 support, CSRF telemetry link reports link quality in RSSI
 - Cybot D1 Lidar support
 - DroneCan (aka UAVCAN) battery monitors support scaling (see BATTx_CURR_MULT)
 - DroneCan (aka UAVCAN) GPS-for-yaw support
 - Electronic Fuel Injection support incl Lutan EFI
 - FETtecOneWire resyncs if EMI causes lost bytes
 - IMU heater params renamed to BRD_HEAT_xxx
 - Landing gear enable parameter added (see LGR_ENABLE)
 - Lightware SF40C ver 0.9 support removed (ver 1.0 and higher still supported)
 - Maxbotix serial sonar driver support RNGFNDx_SCALING parameter to support for varieties of sensor
 - MPPT solar charge controller support
 - MTK GPS driver removed
 - Optical flow in-flight calibration
 - Ping200x ADSB support
 - Proximity sensor min and max range (see PRX_MIN, PRX_MAX)
 - QSPI external flash support
 - uLanding (aka USD1) radar provides average of last few samples
 - Unicore NMEA GPS support for yaw and 3D velocity
 - Board ID sent in AUTOPILOT_VERSION mavlink message
 - DO_SET_CAM_TRIG_DIST supports instantly triggering camera
 - DJI FPV OSD multi screen and stats support
 - GPIO pins configured by setting SERVOx_FUNCTION to -1 (also see SERVO_GPIO_MASK. BRD_PWM_COUNT removed)
 - GPIO pin support on main outputs on boards with IOMCU
 - GyroFlow logging (see LOG_BITMASK's "VideoStabilization" option)
 - Firmware version logged in VER message
 - SD card format via MAVLink
 - Serial port option to disable changes to stream rate (see SERIALx_OPTIONS)
 - VIBE logging units to m/s/s
 - BLHeli passthrough reliability improvements
 - Compass learning (inflight) fixed to ignore unused compasses (e.g. those with COMPASS_USE = 0)
 - EKF optical flow terrain alt reset fixed (large changes in rangefinder alt might never be fused)
 - EKF resets due to bad IMU data occur at most once per second
 - GPIO pin fix on CubeOrange, F4BY, mRoControlZeroF7, R9Pilot
 - MAVlink2 serial ports always send MAVLink2 messages (previously waited until other side sent MAVLink2)
 - Omnibusf4pro bi-directional dshot fix
 - Real-Time-Clock (RTC) oldest possible date updated to Jan-2022

Happy flying!


Release 4.1.7 12th February 2022
--------------------------------

This is a minor release with some important bug fixes and a couple of
new features:

 - avoid all opening of log files in main thread
 - support Benewake CAN Lidars
 - improved VTOL navigation shaping code
 - Compass custom orientation is never overwritten during calibration
 - EKF logging gaps fixed (some messages were occasionally being skipped)
 - IMU logging fix for IREG message (records IMU register changes)
 - SERVOx_FUNCTION protection to avoid memory overwrite if set too high
 - FTP parameter download fix

Happy flying!

Release 4.1.6 3rd January 2022
------------------------------

This is a minor release with some important bug fixes:

 - disable the STLink debug pins after boot by default to prevent ESD
   from triggering CPU changes or a reset
 - support ICM-20608-D, IIM-42652 and ICM-40605 sensors
 - fixed missing covarience reset row in EKF3
 - fixed failure to detect inverted RC input on uarts when transmitter
   is on at boot
 - fixed QAUTOTUNE to obey maximum attitude rate limits
 - fixed a bug in smartaudio support that increased CPU usage
 - added lowpass filter to speed scaling to prevent sudden surface
   movement on VTOL arming
 - fixed hang when BATT_MONITOR is set to 14
 - fixed gaps in EKF3 logging of variances and timing

Happy flying!

Release 4.1.5 13th December 2021
--------------------------------

This is a minor release with some important bug fixes and small number
of new features.

 - fixed switching airspeed sensors based on EKF3 affinity
 - added servo voltage pin on CUAV-X7
 - make 2nd reduction of P gain in AUTOTUNE smaller
 - fixed fence count on upload from old fence protocol
 - fixed reset of ground steering lock for landing
 - reset VTOL takeoff if not armed
 - fixed 16 bit timer bug on MatekH743
 - added FETTec onewire ESC support
 - set slew rate to zero while in QUATOTUNE twitch
 - fixed rate of notch filter to follow configured rate

Happy flying!


Release 4.1.4 25th November 2021
--------------------------------

This is a minor release with some important bug fixes and small number
of new features.

 - fixed RC parachute release to only trigger on RC input channel
   above 1800, fixing an issue with trigger on power on

 - added QRTL flight mode as an RCn_OPTION

 - display VTOL position1 state change when landing approach and
   airbrake logic is disabled using Q_OPTIONS

 - limit Q_VFWD integrator to be below TRIM_THROTTLE to prevent very
   high forward throttle building up in some landing approach
   conditions

 - fixed an issue with high position1 landing approach target speed
   causing the nose to dip when going between VTOL approach and
   position1 states

 - allow for a wider range of Q_A_THR_MIX values to be configured, to
   better support landing quadplanes in high wind

Happy flying!


Release 4.1.3 16th November 2021
--------------------------------

This is a minor release with some bug fixes and small number of new
features.

 - allow for scripting based quadplane motor mixers

 - fixed double application of rangefinder landing offset on go-around

 - avoid doing quadplane approach when close to landing point

 - suppress D gains on fixed wing control surfaces when in ground mode
   to prevent ground oscillation on some aircraft

 - added OBAL Linux board support

 - added CAN_Dn_UC_OPTION parameter to control conflicts in DroneCAN
   DNA database

 - fixed a bug in POSITION1 quadplane landing code that led to too
   sharp pullup in VTOL automatic landing

 - default maximum attitude rate for quadplanes to 75 degrees/second

 - fixed a bug in use of non-zero EK3_PRIMARY value

 - fixed a bug in ADSB vertical velocity reporting

 - fixed an overshoot in quadplane guided takeoff

 - allow for interruption of quadplane guided takeoff with a new
   target location

 - fixed creation of APM/LOGS directory on some boards

 - added RCn_OPTION switch for enabling fixed wing AUTOTUNE in any
   stabilized flight mode (useful for LOITER and AUTO modes)

 - support Durandal with alternative ICM-20602 IMU

 - removed a spurious EKF yaw reset message

Happy flying!


Release 4.1.2 17th October 2021
-------------------------------

This is minor update with a few important fixes and some new features:

 - added EK3_PRIMARY parameter, to allow for selection of other than
   the first IMU as initial EK3 lane

 - added ESC_TELEMETRY_1_to_4 to list of mavlink message selections

 - fixed burst read ftp error causing bad ftp param download

 - fixed H7 FIFO issue with RTSCTS flow control on UARTs

 - apply takeoff throttle slew limit in TAKEOFF mode and forward
   transition in quadplanes

 - reset target mission airspeed on disarm

 - added RC_OPTION bit to enable multi-receiver systems

 - don't apply fixed wing pitch limit to tailsitters when hovering

 - fixed turn rate coordination when inverted in fixed wing flight

 - fixed bug in AUTOTUNE mode that could leave gains from part way
   through the tune

Of these fixes the only one that is not backwards compatible is the
new RC_OPTION bit which needs to be set if you have multiple RC
receivers on your aircraft and want to fail over to a backup receiver
while in flight. You will need to set the new RC_OPTION to enable that
for the 4.1.2 release and later.

Happy flying!

Release 4.1.1 stable 8th October 2021
-------------------------------------

This is the a minor bugfix release in the 4.1.x stable series. Changes
are:

 - fixed IMU orientation on QiotekZealotF427 controller
 - fixed lua scripting bug in logging of strings
 - fixed an initialisation bug in the attitude controller for quadplanes
 - added desired values to PSC quadplane pos control logging
 - prevent entry into CRSF parameter mode while armed
 - added bootloader to build for MatekF765-SE

Thanks to all the users who have been reporting back on flights with
4.1.0, and happy flying!


Release 4.1.0 stable 28th September 2021
----------------------------------------

This is the first stable release of the 4.1.x series for fixed
wing. This is a major release, with a lot of changes over the 4.0.9
release.

The changes since the last beta (4.1.0beta8) are:

 - fixed a wind speed initialiation bug that affects planes with no
   airspeed sensor and no compass
 - added support for icm42605 on MatekH743

There have in total been over 9000 patches applied since the last
major release (the 4.0.9 stable release), so listing all of them is
not practical, but a few of the highlights are:

 - USB log download speeds improved on F7 and H7 based boards
 - Large improvements in EKF stability and support for very long distance flights
 - new boards QiotekZealotF427, MatekF765-SE, MambaF405v2, KakuteF4Mini, FlywooF745, FlywooF745Nano, BeastF7 and BeastH7
 - massive improvements to lua scripting, with greatly expanded API
 - External AHRS support (VectorNav)
 - support for EKF3 affinity for better sensor redundancy
 - big improvements to GPS moving baseline yaw support
 - new fixed wing PID system
 - major fixed wing autotune update giving much better tuning
 - added MAN_EXPO_* parameters for input expos in MANUAL, TRAINING and ACRO modes
 - added ONESHOT_MASK for oneshot output on control surfaces
 - dupport for bi-directional dshot on many boards
 - added PiccoloCAN servo support
 - new quadplane landing approach system, with airbraking
 - greatly improved tiltrotor support with much improved transition stability
 - new unified ESC telemetry system for CAN, DShot and serial ESC telemetry
 - temperature calibration for IMUs, including factory calibration support
 - much improved notch filtering support, including double-notch and realtime FFT based notch filtering
 - much faster parameter download and mission upload/down using MAVFTP
 - switched to new EKF3 as default state estimator with many new features

The 4.1.0 includes contributions from over 240 developers and changes
over 300 thousand lines of code across 2800 files. It has been a
mammoth effort from the whole community to bring this new version of
ArduPilot which pushes the best open flight controller to whole new
levels. A big thank you to everyone who contributed, includding the
developers, testers and documenters. You should all be very proud of
this release!

We are already starting work on 4.2.x, and will of course continue to
do point releases to the 4.1.x series to keep everyone up to date with
the latest fixes.

Happy flying, and thanks for choosing ArduPilot!


Release 4.1.0beta8 15th September 2021
--------------------------------------

This is a very small beta update, with a single patch to fix a serial
flow control issue that came in beta7.

Changes since beta7 are:

 - fixed auto-detection of serial flow control

Happy flying!

Release 4.1.0beta7 11th September 2021
--------------------------------------

This is a small beta update, with a few important fixes. I expect this
to be the final beta and will release 4.1.0 in one week if no
significant issues are found.

Changes since beta6 are:

 - USB log download speeds improved on F765 and F777 based boards (USB buffers increased)
 - Serial port DMA contention heuristics improved (reduces chance of delays writing to serial devices)
 - Declination automatic lookup rounding fix (caused inaccurate declination in some parts of world)
 - DShot (bi-directional) support on Pixhawk4, CUAVv5, CUAVv5-Nano
 - IMU semaphore fix to avoid occasional corruption
 - QioTek Zealot F427 GPIO pin fix
 - Replay/DAL RMGH log message format fix
 - Rangefinder initial buffer size and baudrate fix (affected Aintein US-D1 radar)
 - increase CRSF frame timeout to cope with scheduling delays
 - fixed quadplane Z controller init calculation

Please test this final beta and report on the discuss.ardupilot.org
4.1.0beta topic.

Happy flying!


Release 4.1.0beta6 29th August 2021
-----------------------------------

This is a small beta update, with a few important fixes.

Changes are:

 - Flywoo F745 Goku Nano support
 - Scripting support for getting circle mode radius and setting rate
 - Scripting support for new Guided mode controls including acceleration control
 - ChibiOS scheduling slip workaround to avoid occasional 60ms delays found on MatekH743
 - EKF2 divide-by-zero protection when using optical flow (issue only found in simulator)
 - External AHRS (e.g. VectorNav driver) init fix
 - KakuteF4Mini SBUS fix
 - Pixhawk4 blue and red LEDs swapped
 - Position control fixes to limit initial desired accel (horizontal and vertical) which could lead to an excessive climb or descent
 - fixed airspeed constraint by AHRS_WIND_MAX
 - fixed use of hw serial inversion pins on F4 boards
 - updates for new revisions of QioTekF427Zealot

Happy flying!

Release 4.1.0beta5 16th August 2021
-----------------------------------

This is a small beta update, with a few important fixes.

Changes are:

 - Flywoo F745 supports external I2C compasses
 - GPS-for-yaw arming check added
 - GPS_DRV_OPTIONS allows forcing UBlox GPS to 115200 baud
 - Lua scripts can be placed in ROMFS_custom directory
 - Q_P_VELXY_FILT renamed to _FLTE, Q_P_VELXY_D_FILT renamed to _FLTD
 - CAN threading fix to resolve potential lockup when lua scripts use CAN
 - EKF3 GSF can be invoked multiple times with source switching (no longer limited by EK3_GSF_RST_MAX)
 - EKF3 IMU offset fix (vehicle's reported position was slightly incorrect if INS_POS_XYZ params set)
 - OSD overwrite and nullptr check fix
 - RCOut banner displayed at very end of startup procedure to avoid invalid output
 - allow for VTOL flightmodes after AHRS fallback to DCM
 - fixed a bug in Z accel initialisation (particularly affects tailsitters)
 - added LOG_BITMASK bit for logging attitude and PIDs at full loop rate

Happy flying!

Release 4.1.0beta4 25th July 2021
---------------------------------

This is a large beta update, with significant changes over beta3.

Changes are:

 - major fixed wing autotune update over previous 4.1 beta releases
 - added MAN_EXPO_* parameters for input expos in MANUAL, TRAINING and ACRO modes
 - added ONESHOT_MASK for oneshot output on control surfaces
 - GPS-for-yaw enhancements including using position and yaw from different GPSs
 - Long distance flight supported (thousands of km) including double precision EKF and moving origin
 - major improvements to EKF3 stability
 - BLHeli fix that could cause failure to boot
 - CRSF message spamming and firmware string length fixed
 - Display re-enabled on 1MB boards
 - DShot always sends 0 when disarmed (protects against motors spin while disarmed due to misconfiguration)
 - DShot fix that could cause main loop jitter
 - DShot buzzer tone disabled during motor test to remove bad interation
 - MatekF405-bdshot NeoPixel LEDs re-enabled on PWM5
 - Serial port performance improvements using FIFO on H7 boards

Please report all test results and happy flying!

Release 4.1.0beta3 5th July 2021
--------------------------------

This is a minor update over the 4.0.1beta2 release. It has a few
important fixes for beta testers:

 - sync with copter 4.1.0beta5

 - added QRTL always option in Q_RTL_MODE

 - added more modes to USE_REV_THRUST parameter

 - added QioTekZealonH743 board type

 - added EKF3 protections for variance collapse

 - added PiccoloCAN servo support

 - improved VTOL position control a long distance from home

 - fixed handling of 180 degree longitude date-line crossing

 - fixed logged after forced arm

 - fixed DShot with quadplane motor test

 - adjusted autotune handling of target filter

 - adjusted default roll/pitch PID filters

Please report all test results, and happy flying!

Release 4.1.0beta2 24th June 2021
---------------------------------

This is a minor update over the 4.0.1beta1 release. It has a few
important fixes for beta testers:

 - Fixed a bug in attitude control during the airbrake phase of
   quadplane landing approach

 - skip POSITION1 stage of landing approach for tailsitters

 - fixed handling of corrupted filesystems on arming, where a long
   delay during log file creation could cause a watchdog reset

 - fixed build of VRUBrain-v51 board

 - re-enable soaring on MatekF405-Wing

 - fixed calculation of battery consumption on a commanded reset

Please report all test results, and happy flying!

Release 4.1.0beta1 14th June 2021
---------------------------------

This is a major release, with a lot of changes across almost all
subsystems. Key changes include:

 - major updates to quadplane control algorithms, including new
   landing approach system, new input shaping system for all levels of
   VTOL control and new filtering options.

 - new PID system for fixed wing control, with much more flexible
   filtering and true rate D term support.

 - new fixed wing autotune system which on most aircraft produces a
   much better tune than the previous autotune.

There have also been a lot of changes at the low level system level as
well.

 - new DShot system, with much better timing and new features.

 - improved support for GPS moving baseline yaw

 - new unified ESC telemetry system for CAN, DShot and serial ESC
   telemetry, allowing for ESC RPM driven notch filters on more ESC
   types

 - EKF3 is now the default estimator, with EKF2 also available on
   systems with 2MByte of flash.

 - external AHRS support, allowing for a serial connected AHRS system
   (VectorNav-300 only at the moment)

 - sensor affinity in EKF3, allowing for more robust handling of
   sensor failure, especially for airspeed sensors

 - rate PID slew limiting to to detect and suppress oscillations

 - temperature calibration for IMUs, including factory calibration
   support

 - much improved notch filtering support, including double-notch and
   realtime FFT based notch filtering

 - lots of lua scripting improvements, with greatly expanded API

 - much faster parameter download and mission upload/down using MAVFTP

Many thanks to everyone who contributed to this release. It is our
biggest release yet and has benefited from fantastic feedback from our
user community and partners.

We hope you enjoy this release! Happy flying!

Release 4.0.9, 23rd February 2021
---------------------------------

This is a minor release with just bug fixes:

 - added automatic backup/restore of parameters in case of FRAM
   corruption for F7/H7 boards with 32k FRAM parameter storage

 - fixed a bug in EKF2/EKF3 that could cause memory corruption if
   external naviagtion sources (such as vision based position and
   velocity data) is supplied before the EKF has initialised

 - fixed a problem with low accuracy data from UAVCAN GPS modules when
   GPS blending is enabled

 - fixed an arming check failure with u-blox M9 based GPS modules

 - added mRoControlZeroH7 support

The main reason for this release is the parameter backup/restore. This
has been an ongoing issue for quite some time. The symptom is an
unexpected full parameter reset on boards using FRAM (also known as
RAMTRON) for storage, particularly those using F7 or H7 based
MCUs. The issue has most commonly been seen on the Hex CubeOrange
board, but has been seen on boards from other vendors as well.

The issue has been frustratingly difficult to reproduce. We did narrow
down one cause last year which was a floating CS pin in the bootloader
triggering corruption before ArduPilot starts. An updated bootloader
reduced the occurrance of the issue a lot and we thought it was
solved. Since then the problem has still happened a few times on
boards that have had a bootloader update.

This release avoids the issue by keeping a complete second copy of the
parameters in the 2nd half of 32k FRAM devices. On boot we check the
integrity of the primary parameter storage area via a simple signature
check and if it has been corruptted then we restore from the backup
area. We also raise an internal error named "params_restored". You
will then need to reboot to clear the error, but your parameters will
have been automatically recovered, avoiding the need to reload
parameters and re-calibrate.

If you get this internal error then it would be appreciated if you
could send us the contents of your APM/STRG_BAK directory on the
microSD card so we can analyse the corruption that happened and ensure
that the fix covers all real cases.

Happy flying!

Release 4.0.8, 26th January 2021
--------------------------------

This is a minor release with just bug fixes:

 - fixed a bug where an unhealthy airspeed sensor could be fused in EKF2/EKF3

 - fixed FPort RSSI scaling

 - fixed timestamp on RAW_IMU data to be 64 bit

 - fixed an issue with BLHeli pass-thru on some ESCs

Happy flying!

Release 4.0.7, 3rd November 2020
--------------------------------

This is a minor release over 4.0.6. The changes are:

 - enable telem3 on Durandal

 - fixed SBUS output when scanning for FPort on IOMCU

 - Fixed F32Lightening board IMU fast sampling issue

 - fixed issue with EKF2 and EKF3 with use of high accuracy GPS modules. This is important for anyone flying with a RTK GPS

 - Fixed KakuteF7 DShot glitch issue

 - Fixes issue with checking for valid RC input in ICE disable channel

 - fixed an interrupt flood issue with any sensor that uses interrupt timing measurement, such as RPM sensors.

 - Modified RM3100 driver to problem all 4 possible I2C addresses

 - Fixed UDP RC input on Linux boards to accept up to 16 RC inputs on UDP

The most critical fix in the above list is the ISR flood issue.

ISR Flood Issue
---------------

The ISR flood issue happens when a misbehaving external sensor (such
as an RPM sensor for a petrol motor) starts providing pulses faster
than the flight controller can process them. If this happens then the
CPU can be overloaded in the interrupt handling for the sensor and
stop flying the vehicle. The level of interrupt rate needed to cause
this issue is around 500k interrupts per second, so to be safe we have
added a limit of 100k interrupts per second. If a single interrupt
source produces more than 10k interrupts in a single 0.1 second period
then we disable that interrupt source, print a warning to the user and
raise an internal error.

Happy flying!

Release 4.0.6, 7th September 2020
---------------------------------

The ArduPilot development team is proud to announce the release of
plane 4.0.6 stable. This is a significant release with a lot of
changes, so please read the change summary below carefully.

 - changed LED scripting API to allow more than 32 LEDs on a pin
 - added support for ProfiLED LEDs
 - added u-blox GPS moving baseline u-blox auto-configuration
 - fixed handling of GPS antenna positions on EKF GPS switch
 - changed default USB IDs to new ArduPilot specific IDs
 - fixed bug in handling trim for RC control of camera mounts
 - added LGR_OPTIONS bits to control landing gear behaviour on takeoff/landing
 - improved mavlink streaming output control to better allocate time to each channel
 - fixed send of mavlink PARAM_VALUE on set of a readonly parameter
 - fixed mag variance reporting in EKF_STATUS_REPORT mavlink message
 - fixed time wrap bug in BMP085 barometer driver
 - fixed buffer overflow in ST24 RC input driver
 - fixed EKF usage of WMM tables when user has specified a specific declination
 - fixed bug in AP_Terrain on-disk format
 - added script for offline generation of terrain data
 - severel improvements to smbus battery drivers
 - fixed a race condition in parameter storage on ChibiOS
 - fixed use of zero GNSS timestamp in UAVCAN GPS driver
 - improved GCS messages during bootloader flash
 - fixed CS pin in bootloader that could corrupt FRAM on some boards
 - added GPS yaw to MAVLink GPS_RAW_INT message
 - added Hott telemetry support
 - added FRSky FPort support
 - fixed bug in CAN clock and queue handling on H7 based boards
 - added support for BRD_ALT_CONFIG for alternative hardware configs on several boards
 - added new boards CUAV-Nora, CUAV-X7, MatekH743, R9Pilot, mRoNexus
 - improved reporting of internal errors to GCS
 - fixed recursion bug in tonealarm player
 - fixed flaperon SERVO_AUTO_TRIM behaviour
 - added option to compensate forward throttle for battery voltage
 - added compensation in VTOL gains for pressure altitude
 - switched to new more flexible compass ordering system
 - fixed forcing of safety off on IOMCU reset
 - increased maximum compass scale factor to 1.4
 - added RTL_CLIMB_MIN parameter for initial climb in RTL
 - fixed disable of throttle nudge during a RC failsafe
 - added support for a wider range of DLVR airspeed sensors
 - fixed RC input processing for out of range RC channels
 - added THR_FAILSAFE=2 option for flying BVLOS with only GCS failsafes enabled
 - fixed reordering of compasses on boot based on compass priorities
 - fixed use of minimum accuracy for GPS data in EKF2 and EKF3, fixing an issue with RTK GPS modules that report overly optimistic accuracy values
 - fixed handling of RC_OPTION bit for disabling receiver failsafe handling
 - fixed LOITER_TO_ALT with terrain altitude target
 - fixed an issue with swapping UAVCAN compasses and calibration
 - fixed use of VTOL quadplane missions (where the plane flies as a multi-rotor for auto waypoints)
 - fixed legacy parsing of some lightware i2c lidars when beyond max range
 - fixed RTL_AUTOLAND with MIS_RESTART=1
 - enable more compasses on low flash boards
 - HAL erase storage fix for flash storage boards
 - prevent jump to circle mode in TAKEOFF if flying for less than 10s
 - fixed handling of out of range on LightwareI2C Lidar
 - fixed init of HoTT telemetry
 - added support for mRo Pixracer Pro, Holybro Pix32v5
 - added arming check for terrain data healthy if needed
 - increased monitor thread size to 768
 - fixed IMU fast sampling on F35Lightning board

The key changes to existing behaviour to watch out for in this update
are:

USB IDs
-------

ArduPilot has now switched to it's own USB IDs for all boards. For
most users this won't cause any change, except they may notice the
drop down list of devices in MissionPlanner will have the ArduPilot
flight controllers labelled more usefully as "ArduPilot" instead of
just "STM32". If you hit issues on windows please try reinstalling the
device drivers from this URL:
https://firmware.ardupilot.org/Tools/MissionPlanner/driver.msi

Terrain
-------

A bug fix in the format ArduPilot uses to store terrain data means
that your flight controller will need to re-download the terrain data
onto the sdcard via your GCS. If you fly without a GCS and you use
terrain data then please re-download the terrain data you need by
setting up a mission when you have internet access and allowing your
flight controller to request terrain data from your GCS.

Pressure Altitude Compensation
------------------------------

This release adds in the missing hook for the VTOL motor controller on
a quadplane to compensate the VTOL gains using your pressure
altitude. For most people this will not have a noticible effect, but
some users that have tuned their aircraft for high locations may
notice a tuning change.

Compass Ordering
----------------

The new compass ordering system in this release gives a lot more
flexibility and should preseve existing configurations and
calibrations. If you hit issues then please have a look at the new
compass ordering user interface in recent beta releases of
MissionPlanner.

THR_FAILSAFE=2
--------------

For people flying before visual range (with appropriate
authorisations) there is a new option which allows for safer
operation. By setting THR_FAILSAFE=2 you can setup the R/C failsafe so
that R/C input will stop being used when one of the receiver failsafe
conditions is met, while not triggering any failsafe actions. This is
a significant improvement over setting THR_FAILSAFE=0 as it prevents
the receiver inputs being used for stick mixing and other inputs that
would be inappropriate when in receiver failsafe.

Many thanks to everyone who contributed to and tested this release!

Happy flying!

Release 4.0.6beta5, 25th August 2020
------------------------------------

This should be the final beta for 4.0.6. Several important fixes are
included:

 - fixed disable of throttle nudge during a RC failsafe

 - added support for a wider range of DLVR airspeed sensors

 - fixed RC input processing for out of range RC channels

 - added THR_FAILSAFE=2 option for flying BVLOS with only GCS
   failsafes enabled

 - fixed reordering of compasses on boot based on compass priorities

 - fixed use of minimum accuracy for GPS data in EKF2 and EKF3, fixing
   an issue with RTK GPS modules that report overly optimistic
   accuracy values

 - fixed handling of RC_OPTION bit for disabling receiver failsafe
   handling

 - fixed LOITER_TO_ALT with terrain altitude target

Happy flying!

Release 4.0.6beta4, 27th July 2020
----------------------------------

This is a minor change over beta3

 - fixed an issue with swapping UAVCAN compasses and calibration

 - fixed use of VTOL quadplane missions (where the plane flies as a
   multi-rotor for auto waypoints)

 - fixed legacy parsing of some lightware i2c lidars when beyond max
   range

Happy flying!


Release 4.0.6beta3, 7th July 2020
----------------------------------

This is a minor change over beta2

 - fixed RTL_AUTOLAND with MIS_RESTART=1
 - enable more compasses on low flash boards
 - HAL erase storage fix for flash storage boards

Happy flying!

Release 4.0.6beta2, 23rd June 2020
----------------------------------

This is a minor change over beta1

 - prevent jump to circle mode in TAKEOFF if flying for less than 10s
 - fixed handling of out of range on LightwareI2C Lidar
 - fixed init of HoTT telemetry
 - added support for mRo Pixracer Pro, Holybro Pix32v5
 - added arming check for terrain data healthy if needed
 - fixed compass ordering bug
 - increased monitor thread size to 768

Happy flying!

Release 4.0.6beta1, 23rd May 2020
---------------------------------

This is a major release with a significant number of new features and
bug fixes. You should read the list of changes and the information
below carefully.

 - changed LED scripting API to allow more than 32 LEDs on a pin
 - added support for ProfiLED LEDs
 - added u-blox GPS moving baseline u-blox auto-configuration
 - fixed handling of GPS antenna positions on EKF GPS switch
 - changed default USB IDs to new ArduPilot specific IDs
 - fixed bug in handling trim for RC control of camera mounts
 - added LGR_OPTIONS bits to control landing gear behaviour on takeoff/landing
 - improved mavlink streaming output control to better allocate time to each channel
 - fixed send of mavlink PARAM_VALUE on set of a readonly parameter
 - fixed mag variance reporting in EKF_STATUS_REPORT mavlink message
 - fixed time wrap bug in BMP085 barometer driver
 - fixed buffer overflow in ST24 RC input driver
 - fixed EKF usage of WMM tables when user has specified a specific declination
 - fixed bug in AP_Terrain on-disk format
 - added script for offline generation of terrain data
 - severel improvements to smbus battery drivers
 - fixed a race condition in parameter storage on ChibiOS
 - fixed use of zero GNSS timestamp in UAVCAN GPS driver
 - improved GCS messages during bootloader flash
 - fixed CS pin in bootloader that could corrupt FRAM on some boards
 - added GPS yaw to MAVLink GPS_RAW_INT message
 - added Hott telemetry support
 - added FRSky FPort support
 - fixed bug in CAN clock and queue handling on H7 based boards
 - added support for BRD_ALT_CONFIG for alternative hardware configs on several boards
 - added new boards CUAV-Nora, CUAV-X7, MatekH743, R9Pilot, mRoNexus
 - improved reporting of internal errors to GCS
 - fixed recursion bug in tonealarm player
 - fixed flaperon SERVO_AUTO_TRIM behaviour
 - added option to compensate forward throttle for battery voltage
 - added compensation in VTOL gains for pressure altitude
 - switched to new more flexible compass ordering system
 - fixed forcing of safety off on IOMCU reset
 - increased maximum compass scale factor to 1.4
 - added RTL_CLIMB_MIN parameter for initial climb in RTL

The key changes to existing behaviour to watch out for in this update
are:

USB IDs
-------

ArduPilot has now switched to it's own USB IDs for all boards. For
most users this won't cause any change, except they may notice the
drop down list of devices in MissionPlanner will have the ArduPilot
flight controllers labelled more usefully as "ArduPilot" instead of
just "STM32". If you hit issues on windows please try reinstalling the
device drivers from this URL:
https://firmware.ardupilot.org/Tools/MissionPlanner/driver.msi

Terrain
-------

A bug fix in the format ArduPilot uses to store terrain data means
that your flight controller will need to re-download the terrain data
onto the sdcard via your GCS. If you fly without a GCS and you use
terrain data then please re-download the terrain data you need by
setting up a mission when you have internet access and allowing your
flight controller to request terrain data from your GCS.

Pressure Altitude Compensation
------------------------------

This release adds in the missing hook for the VTOL motor controller on
a quadplane to compensate the VTOL gains using your pressure
altitude. For most people this will not have a noticible effect, but
some users that have tuned their aircraft for high locations may
notice a tuning change.

Compass Ordering
----------------

The new compass ordering system in this release gives a lot more
flexibility and should preseve existing configurations and
calibrations. If you hit issues then please have a look at the new
compass ordering user interface in recent beta releases of
MissionPlanner.

Many thanks to everyone who contributed to and tested this release!

Happy flying!


Release 4.0.5, 4th March 2020
-----------------------------

This release includes a one important bug fix and some minor
enhancements. The changes are:

 - fixed bug handling change of RC input source while MAVLink signing
   is active. This could cause a stack overflow and a crash

 - added display of RC output types to aid in debugging DShot outputs

 - modified clocking on H7 boards to produce more accurate clocks for
   DShot

 - switched to new USB VIDs for dual-CDC boards

 - fixed a bug in handling LOITER_TURNS for quadplanes when
   Q_GUIDED_MODE is enabled

 - added a TECS reset at the end of a VTOL takeoff to handle aircraft
   with TECS climb rates below VTOL climb rates

Happy flying!

Release 4.0.4, 16th February 2020
---------------------------------

This release includes a significant number of changes from 4.0.3. Key
changes are:

 - re-sync the 4.0 release with Copter-4.0, bringing them in line so
   as to maximise cross-vehicle testing

 - fixed a timing issue in IOMCU that could lead to loss of RC input
   frames and increased latency of output to ESCs on the IOMCU

 - fixed a bug in restoring gains in QAUTOTUNE that could cause the
   aircraft to be unstable on leaving QAUTOTUNE mode

 - fixed stack size of ftp thread

The Copter-4.0 re-sync brings in quite a few structural changes. The
main user visible changes are:

 - UAVCAN DNA server no longer needs a microSD card to operate

 - added logging of UAVCAN servos and ESC feedback messages

 - reduced QTUN logging rate to 25Hz

 - reduced memory usage in EKF with multiple lanes

 - Minor OSD improvements

 - added a lot more scripting bindings

 - fixed UAVCAN GPS status when not connected

 - added EK3_MAG_EF_LIM limit for earth frame mag errors

 - added MAVLink FTP support

 - added support for WS2812 LEDs

Due to the significant number of changes with the re-sync I would
particularly appreciate as much flight testing as we can get on this
release.

Happy flying!

Release 4.0.3, 21st January 2020
--------------------------------

This is a minor release with a few bug fixes and enhancements. The
changes since beta1 are:

 - fixed 3 missing semaphore waits
 - fixed checking for bouncebuffer allocation on microSD card IO
 - fixed incorrect param count
 - prevent failsafe action from overriding a VTOL land
 - fixed compass calibration failures with auto-rotation detection
 - fixed errors on STM32H7 I2C (affects CubeOrange and Durandal)
 - fixed a race condition in FrSky passthrough telemetry
 - fixed DSM/Spektrum parsing for 22ms protocols
 - added fixed yaw compass calibration method
 - re-generated magnetic field tables
 - ensure SERIAL0_PROTOCOL is mavlink on boot

The most important fix is for FrSky pass-through telemetry. Pass
through telemetry support had a race condition which could lead to the
flight controller generating a fault and rebooting. Until you are
running a firmware with the fix you should disable FrSky pass-through
telemetry by changing SERIALn_PROTOCOL from 10 to 0 on the where you
have SPort enabled.

Happy flying!

Release 4.0.2, 30th December 2019
---------------------------------

This is a minor release with a few bug fixes and enhancements. Changes
are:

 - fixed voltage scaling on CUAVv5Nano
 - fixed 10Hz NMEA output
 - fixed range check on RC channel selection
 - scale UART RX size with baudrate
 - default fast sampling enabled on first IMU for all capable boards
 - fixed bootloader flash alignment bug
 - fixed PWM 5 and 6 for MatekF765-Wing
 - support RM3100 compass on I2C
 - fixed error on AHRS level trim in preflight calibration
 - fixed handling of SB without BUSY on I2Cv1 devices
 - update bootloaders to be more robust to unexpected data
 - added new COMPASS_SCALE parameters and estimator to fix issue with
   compass in Here2 GPS
 - fixed issue with millis wrap on boards with 16 bit system timer
   (MatekF405, MatekF765, speedybeef4 and KakuteF4)
 - fixed i2c internal masks for several boards
 - fixed scaling of Blheli32 RPM conversion
 - changed to WFQ FrSky telemetry scheduler
 - enable LED pin on MatekF765
 - added params for Durandal battery monitor pins to param docs
 - updated bootloaders to reduce change of line noise stopping boot
 - fixed DMA error causing memory corruption in ChibiOS I2C driver
 - fixed param conversion from plane 3.9.x to plane 4.0.x for rangefinders
 - cope with UAVCAN GPS that doesn't provide AUX messages (Here2 GPS)
 - send temperatures for IMUs on mavlink
 - fixed I2C clock speed error on STM32H7
 - fixed CR/LF output error for NMEA output

Happy flying!

Release 4.0.1, 22nd November 2019
---------------------------------

This is a minor release with a few bug fixes and enhancements. Changes
are:

 - Added Q_ASSIST_ALT parameter which offers a way for VTOL assistance
   at low altitudes

 - fixed channels 5 and 6 on the MatekF765-Wing

 - fixed a bug with sending data on a full UART in mavlink parameter
   download

 - fixed use of UAVCAN primary GPS with UART secondary GPS

 - fixed failover between rangefinders of same orientation

 - added RC option for TAKEOFF mode

 - fixed logging of current on secondary battery monitors

 - fixed register checking on DPS280 for mRoControlZeroF7

 - added clock panel to OSD

 - fixed B/E led brightness on Pixhawk4

 - support RTCM injection to UAVCAN GPS for RTK support

 - fixed an RC failsafe bug that could cause the geofence to disable

 - fixed a bug in the SDP33 airspeed driver

Happy flying!


Release 4.0.1beta1, 17th November 2019
--------------------------------------

This is a minor release with a few bug fixes and enhancements for the
4.0 stable version.

Changes are:

 - Added Q_ASSIST_ALT parameter which offers a way for VTOL assistance
   at low altitudes

 - fixed channels 5 and 6 on the MatekF765-Wing

 - fixed a bug with sending data on a full UART in mavlink parameter
   download

 - added TECS_LAND_PMIN for constraining pitch minimum during landing

 - fixed use of UAVCAN primary GPS with UART secondary GPS

 - fixed failover between rangefinders of same orientation

 - added RC option for TAKEOFF mode

 - fixed logging of current on secondary battery monitors

 - fixed register checking on DPS280 for mRoControlZeroF7

 - added clock panel to OSD

 - fixed B/E led brightness on Pixhawk4

 - support RTCM injection to UAVCAN GPS for RTK support

Happy flying!

Release 4.0.0, 28th October 2019
--------------------------------

The final release of stable 4.0.0 has just one change from beta4,
which is to fix a bug in the new TAKEOFF flight mode.

Many thanks to everyone who has been testing the 4.0 release, and
special thanks to Henry for his fantastic work in bringing the wiki up
to date for this release!

Happy flying!

Release 4.0.0beta4, 19th October 2019
------------------------------------

A small set of improvements over beta3:

 - fixed time race in airspeed driver (thanks to liang)
 - fixed uninitialsed bytes in send_named_float()
 - added TAKEOFF flight mode

Happy flying!

Release 4.0.0beta3, 7th October 2019
------------------------------------

A small set of improvements over beta2:

 - added Pixhawk1-1M build (for Pixhawk1 with 1M flash bug)
 - fixed a bug handling UART corruption for u-blox driver
 - fixed a CAN ISR latency bug for STM32H7 boards
 - fixed FMU channel mask to correctly obey SERVO_RATE
 - fixed use of DMA on Pixracer WiFi UART
 - reduced flash size and memory usage with EKF optimisation changes
 - fixed a BLHeli bug when no motors enabled
 - raise default LOG_FILE_BUFSIZE on boards with more memory
 - fixed units of custom AHRS orientations
 - fixed LOG_FILE_DSRMROT with delayed log stop on disarm
 - fixed block flash logging
 - fixed SLCAN bug on Pixhawk1 and fmuv2
 - added check for airspeed and Z controller active for hover throttle learning
 - enable hover learning by default in quadplanes

Happy flying!

Release 4.0.0beta2, 24th September 2019
---------------------------------------

A small set of improvements over beta1:

 - Added autoranging to current and voltage in OSD
 - fixed issue with motors spinning up in quadplane landing when below min lidar range
 - fixed empty pre-arm warning string in EKF
 - added code to cope with SCHED_LOOP_RATE being above max achievable rate
 - fixed delay on oneshot125 channels which was limiting loop rate
 - fixed use of uninitialised variable in mag fusion in EKF3

Happy flying!

Release 4.0.0beta1, 16th September 2019
---------------------------------------

The 4.0.0 release of ArduPilot plane is a major release. It contains
thousands of changes, some large and some small, developed over the
last year. Key changes include:

 - support STM32H7 MCUs (Durandal and CubeOrange currently)
 - QAUTOTUNE mode
 - in-flight compass calibration
 - support for dynamic node allocation server in UAVCAN
 - support for SLCAN pass-through
 - support for up to 10 battery backends
 - support for MAVLink message intervals
 - initial support for onboard Lua scripting
 - automatic remount of microSD cards
 - support for additional RC input on any UART
 - support for WS2812 LEDs
 - new simulators: Morse, Webots, SilentWings, Scrimmage
 - added AFS_MAX_RANGE for limiting vehicle range
 - added support for @READONLY in embedded parameter lists
 - added SERIALn_OPTIONS for inversion, half-duplex and swap
 - added support for reversible blheli32 ESCs
 - added GPS timing jitter correction
 - added Sum battery backend type
 - added fuel flow battery battery types
 - support landing gear control based on altitude
 - added loiter-to-alt approach for quadplane landing
 - added REVERSE_THROTTLE RCn_OPTION
 - added crow brakes for differential spoilers
 - support newer versions of JSBSim in SITL
 - added RObotis servo support
 - added UAVCAN servo support
 - added failure timer for quadplane forward transitions
 - added KDECAN and ToshibaCAN support
 - added RM3100 compass support
 - support new Invensense IMUs
 - many small improvements to tailsitter support
 - added QACRO mode
 - improved watchdog logging
 - improved EKF compass handling based on WMM data
 - added AP_Perip CAN peripheral firmware system
 - added IBUS R/C input support
 - support yaw from GPS
 - support DLVR I2C airspeed sensors
 - fixed FrSky telem packet loss
 - fixed SUMD for 16 channels via pulse input
 - support UAVCAN buzzers, safety switch, safety LED and airspeed
 - added NTF_BUZZ_VOLUME parameter

There are hundreds of other changes as well. I expect the beta period
for this release to be quite long, and I would greatly appreciate as
many test reports as possible. Please submit test results both for
successful and unsuccessful tests!

Happy flying!


Release 3.9.11, 15th September 2019
-----------------------------------

This release fixes two bugs:

 - fixed an uninitialised byte in UAVCAN compass device IDs that
   caused users to need to re-calibrate UAVCAN compasses

 - fixed an I2C interrupt storm that could potentially cause a
   crash. This crash has never been shown to happen in our stable
   releases, but is being fixed as a preventative measure

Release 3.9.10, 26th August 2019
-------------------------------------

This releases includes an important fix to IOMCU handling that fixes a
problem where motors attached on the AUX channels can stop momentarily
due to an error in the UART handling to the IOMCU.

Release 3.9.9, 4th August 2019
------------------------------

New plane stable release with two fixes over beta4:

 - fixed setting file timestamps with u-blox F9 GPS (thanks to Martin Sollie)
 - fixed return value of LightWare serial rangefinder driver when out of range

Release 3.9.9beta4, 31st July 2019
----------------------------------

Minor update over beta3:

 - fixed an issue with handling baro failure on CubeBlack
 - forced INS_USE3=1 on CubeBlack when first two IMUs are enabled

Release 3.9.9beta3, 28th July 2019
----------------------------------

Minor update over beta2:

 - fixed issue with setting of EKF origin in GPS modes
 - added support for MS4525 on 4th I2C bus
 - added delay on LW20 I2C probe


Release 3.9.9beta2, 11th July 2019
----------------------------------

Minor update over beta1:

 - added mRoControlZeroF7 support
 - enabled IO pass-thru on FMU failure on ChibiOS builds

Release 3.9.9beta1, 8th July 2019
---------------------------------

This is a minor release with some important safety and flight
improvement fixes.

Key changes are:

 - learn EKF biases even for inactive IMUs, making IMU failover much  more robust

 - EKF uses earth magnetic field model to reduce in-flight compass errors

 - EKF switches to first healthy IMU when disarmed

 - IMU fix for dropped samples during high CPU usage

 - added support for Ublox F9 GPS

 - switched to ChibiOS I/O firmware for ChibiOS builds to support Spektrum bind

 - CUAVv5 Nano LED fix

 - fixed watchdog trigger on simple accelcal

 - fixed drift of AMSL estimate when disarmed

 - fixed rate integrator buildup when rate control disabled

Happy flying!

Release 3.9.8, 26th April 2019
------------------------------

This is a minor release with some important safety fixes. The fixes
relate to two issues:

 - implement hardware support to reset the CPU if a software or hardware
   failure causes the main loop to stop running

 - fixed a bug in the handling of a failure of the primary IMU in a
   multi-IMU system

The first fix relates to a flyaway that happened on a RadioLink
mini-pix flight controller. The mini-pix suffered a major hardware
failure that led to the main loop stopping. As the mini-pix does not
have an IO co-processor this resulted in fixed control surface outputs
and fixed motor output, which led to a flyaway. The plane was found,
but we want to ensure that if this ever happens again that the motor
will stop and that the pilot will regain control of the aircraft.

The fix is to enable an option in the STM32 processor called
"Independent Watchdog" (IWDG). The IWDG provides a mechanism to
automatically reset the CPU on software or hardware failure resulting
in the main loop stopping. When this happens the hardware also
provides a mechanism for ArduPilot to know that it is booting after a
watchdog reset, in which case it does the following:

 - if the ChibiOS bootloader has been updated then it skips the normal
   5 second delay in the bootloader

 - it skips baromoter, gyro and airspeed calibration, allowing for
   very fast boot

 - the home position and attitude estimate of the vehicle is restored
   to a point less than 0.3 seconds before the lockup

 - the pilot regains full control, and if the pilot requests arming
   then arming checks are automatically bypassed

We have tested this on an aircraft with a deliberately induced full
CPU lockup. The aircraft recovered and flew normally within 3 seconds
of lockup, with the pilot having full control. This was with a board
with no IOMCU. A board that does have an IOMCU (such as a Hex cube)
would have had full manual control on FMU lockup without the changes
in this new release. For those boards with IOMCU the advantage of the
new release is that the pilot will regain the ability to use
stabilised and auto modes (including RTL) after a full CPU lockup.

The additional protections of the IWDG support only apply to the
ChibiOS builds. The IWDG is not supported in NuttX builds.

The second key fix in this release relates to IMU failure on multi-IMU
systems. If the IMU that is associated with the currently active EKF
lane failed then the fixed wing attitude controller would lose
attitude control and the aircraft will crash unless the pilot takes
manual control. This has been fixed to ensure that IMU failover
operates correctly.

Other changes in this release:

 - added support for the CUAVv5Nano board

 - added retries to flash storage of parameters

 - fixed pullups on some fmuv3 based boards that lack hardware pullups
   on sdcard data lines

 - fixed fallback to microSD for parameter storage if a board with
   FRAM storage has a failed FRAM device

 - added a specific check for hw failure regarding the recent
   CubeBlack safety bulletin

 - fixed the power flags for brick2 on fmuv5

Happy flying!


Release 3.9.7, 1st April 2019
-----------------------------

This is a minor release to fix a single important bug in speed/height
handling. The bug was in the TECS (total energy control system) and
could cause a sustained pitch down due to a transient glitch in
airspeed demand resulting in a large negative pitch integrator.

The log that found the bug was of a quadplane in CRUISE mode, and
resulted in the aircraft forcing nose down for long enough to crash.

The fix includes both protections for the pitch integrator and a fix
for the cause of the transient in airspeed demand affecting the TECS
demanded airspeed.

Happy flying!


Release 3.9.6, 4th March 2019
-----------------------------

This is a minor release with some important fixes for VTOL landings
and EKF handling of compass switching.

 - fixed throttle slew on dual-motor planes

 - handle VTOL landings with incorrect height (or failed rangefinder)

 - fixed EKF alt datum reset on barometer reset

 - probe all I2C buses for rangefinders (fixes I2C lidar on boards
   with more than 2 I2C buses)

 - fixed lightware serial detection for newer lidars such as LW20

 - fixed motor relax code on VTOL landing to prevent motors powering
   up after touchdown

 - fixed forward motor in quadplane landing transition to allow motors
   when landing at a height lower than takeoff height

 - fixed throttle mix handling for VTOL planes on descent

 - use WP altitude for height of ground in VTOL landing, allowing for
   good landings at heights well above or below takeoff height

 - fixed EKF compass switching with 3 compasses

 - fixed mini-pix UART order to match case markings

 - added generated git hash to apj file

 - fixed apj generation to not change based on time of build

 - fixed thread creation to use any heap, fixing a bug on
   MatekF405-Wing with a compass

Happy flying!


Release 3.9.5, 27th January 2019
--------------------------------

This is a minor update over the 3.9.4 release. This release includes
the following changes:

 - fixed orientation of 2nd IMU for OmnibusF7V2

 - fixed LEDs on Pixhawk4 and Pixhawk4-mini

 - fixed safety switch on Pixhawk4-mini

 - improved robustness of microSD support under ChibiOS

 - support insert of a microSD after boot but before logging starts
   under ChibiOS

 - added BRD_SD_SLOWDOWN parameter to allow microSD cards to be run at
   a lower clock to improve reliability

 - fixed VTOL takeoff in quadplanes to ignore latitude/longitude in
   the mission item and always takeoff vertically

 - added CubePurple (also known as PH2Slim) builds

 - added DrotekP3Pro builds

 - RC protocol decoding for SRXL, SUMD and ST24 extended to all boards
   including pixracer and ChibiOS-only boards

 - fixed CAN on Pixhawk4

 - fixed EKF yaw reset in quadplanes

 - fixed bug handling corruption of DSM RC protocols

 - fixed internal amber LED on CubeBlack

Happy flying!


Release 3.9.4, 10th December 2018
---------------------------------

This is a minor update over the 3.9.3 release. Changes are:

 - fixed a critical bug in SBUS handling on the Pixhawk4

 - fixed bugs in benewake rangefinder driver

 - added support for garmin LidarLite-V3HP

 - improved error handling for TeraRanger Lidar

 - fixed a bug in DSM handling for more than 7 channels on some boards

 - fixed default voltage and current scaling for Pixhawk4 and PH4-mini

Happy flying!

Release 3.9.3, 10th November 2018
---------------------------------

The ArduPilot development team are delighted to announce the 3.9.3
stable release of the ArduPilot plane code. This release includes a
number of small but important fixes over 3.9.2:

 - fixed error handling for corrupt RC input that could lead to a
   crash in unusual circumstances

 - fixed a race condition in IOMCU event startup that could lead to
   the safety not being disabled on boot with BRD_SAFETYENABLE=0

 - ensure surface speed scaling covers full range of configured
   airspeeds

 - added builds for new boards F35Lightning, omnibusf4v6, mRoX21-777

 - updated GPIO numbers on AUX pins on all boards to be consistent
   with docs

 - updated KakuteF7 UARTs and buzzer

 - added ESC sensor uart on OmnibusNanoV6

 - fix for Benewake rangefinder at long ranges

 - prevent attempts to erase dataflash logs while armed

Many thanks to the people who tested this release!

Happy flying!


Release 3.9.3beta1, 6th November 2018
-------------------------------------

This is the first beta release for the 3.9.3 firmware. This release
has the following changes over 3.9.2:

 - fixed error handling for corrupt RC input that could lead to a
   crash in unusual circumstances

 - fixed a race condition in IOMCU event startup that could lead to
   the safety not being disabled on boot with BRD_SAFETYENABLE=0

 - ensure surface speed scaling covers full range of configured
   airspeeds

 - added builds for new boards F35Lightning, omnibusf4v6, mRoX21-777

 - updated GPIO numbers on AUX pins on all boards to be consistent
   with docs

 - updated KakuteF7 UARTs and buzzer

 - added ESC sensor uart on OmnibusNanoV6

Happy flying!


Release 3.9.2, 10th October 2018
--------------------------------

The ArduPilot development team are delighted to announce the 3.9.2
stable release of the ArduPilot plane code. This release includes a
number of small but important fixes over 3.9.1.

The changes since the 3.9.2beta3 release are:

 - fixed a DShot send bug that could lead to board lockup

 - fixed RGB LED display on Pixracer under both NuttX and ChibiOS

 - fixed safety switch option handling bug

Thanks for the bug reports and testing by all users for the 3.9.2beta
series!

Happy flying!


>>>>>>> a9d5581378... Plane: updated release notes for 3.9.8-beta1
Release 3.9.2beta3, 20th September 2018
---------------------------------------

The is the third beta release of the plane 3.9.2 stable release. It
contains a small number of fixes:

 - fixed clearing of loiter pilot acceleration targets for quadplanes

 - fixed handling of duplicate rotations for COMPASS_AUTO_ROT

I expect this will be the last beta release before 3.9.2

Happy flying!

Release 3.9.2beta2, 15th September 2018
---------------------------------------

The is the second beta release of the plane 3.9.2 stable release. It
contains a number of important bug fixes.

 - implement failsafe PWM in IOMCU, for AFS failsafe when FMU dies

 - handle reversed channels correctly in AFS failsafe

 - fixed twin motor plane handling in AFS failsafe

 - fixed a bug in Q_ASSIST_SPEED support for tiltrotors that could
   lead to zero throttle when assistance triggers.

 - lower default PTCH2SRV_D to 0.04 after reports of oscillation on
   small flying wings

 - added speed scaler reduction in Q modes when at low airspeed

 - fixed synthetic airspeed estimation to be along +ve X axis

 - fixed relaxing of VTOL attitude controller on transition (thanks to
   Leonard Hall)

 - default COMPASS_AUTO_ROT to 2 on all boards

 - fixed UART speed rounding bug that caused failure at high board
   rates

 - fixed a short glitch in position control on quadplane transition
   (many thanks to Leonard for lots of help with this)

 - fixed RSSI voltage from IOMCU

Thanks to everyone who has contributed, and please report all test
results with this beta.

Happy flying!

Release 3.9.2beta1, 12th September 2018
---------------------------------------

The is the first beta release of the plane 3.9.2 stable release. It
contains a number of important bug fixes.

 - fixed a quadplane bug that could cause large attitude instability
   during takeoff if the aircraft does not have enough power to climb
   to its target height. Thanks to Leonard for the fix.

 - fixed a bug that prevented dead-reckoning working on GPS loss.

 - fixed an ADC bug that prevented some boards from using all of their
   analog input. Thanks to vierfuffzig for reporting!

 - fixed a bug in advanced failsafe support that left RC input to
   throttle active after termination. Thanks to Michael Thomas for
   finding this bug.

 - fixed use of OLED displays on the first I2C bus on systems with two
   I2C buses

Thanks to everyone who has contributed, and please report all test
results with this beta.

Happy flying!


Release 3.9.1, 31st August 2018
-------------------------------

The ArduPilot dev team are delighted to announce a new stable plane
release. This is a minor release, but does include some important
fixes.

The only change over the 3.9.1beta3 release is an increase in the USB
buffer size to give faster log download over USB.

Other important changes from the 3.9.1beta releases include:

 - updated to default values for some tuning parameters

 - fixed a critical safety issue found by Oliver Volkmann, and fixed
   by Michael du Breuil. The problem could lead to quadplane motors
   starting on reboot

 - fixed a bug where RC failsafe could lead rudder input stuck at the
   last rudder input value in some modes. Thanks to Michael du Breuil
   for the fix

 - improved DShot and BLHeli-passthrough support, fixing errors
   reported by BLHeli32 ESCs and making using BLHeliSuite32 much more
   reliable

 - new tonealarm system, bringing the tones produced under ChibiOS and
   Linux HALs in line with tones produced under the HAL_PX4 HAL.

 - added a new LED driver for the NCP5623 LED

 - fixed delay caused by parameter writes in AP_Stats

 - fixed default RSSI input pin on some boards

 - fixed the incorrect display of "no io thread heartbeat" message

 - fixed microSD support on some boards due to running out of DMA
   capable memory

Many thanks to everyone who has been testing the beta releases.

Happy flying!


Release 3.9.1beta3, 23rd August 2018
------------------------------------

This is the third beta for the 3.9.1 release, which is a minor
update over 3.9.0. This release includes the following changes:

 - fixed a critical safety issue found by Oliver Volkmann, and fixed
   by Michael du Breuil. The problem could lead to quadplane motors
   starting on reboot

 - fixed a bug where RC failsafe could lead rudder input stuck at the
   last rudder input value in some modes. Thanks to Michael du Breuil
   for the fix

 - raised OSD thread stack size to 1024 (needed for new OSD items)

Thanks to everyone who is testing the 3.9.1beta series!

Release 3.9.1beta2, 17th August 2018
------------------------------------

This is a the second beta for the 3.9.1 release, which is a minor
update over 3.9.0. This release includes the following changes:

 - increased default buffer sizes in dataflash for some boards

 - fixed a bug with small dataflash buffers

 - updated OSD with new items: hdop, temperature, waypoint, xtrack,
   flight distance, flighttime, efficiency (thanks to Hwurzburg,
   vierfuffzig and sh83).

 - reduced memory usage in serial buffers

 - adjusted some default parameters to give a better first flight
   experience for more users.

The default parameter changes are:

 - RLL2SRV_P from 0.6 to 1.0
 - RLL2SRV_I from 0.1 to 0.3
 - RLL2SRV_D from 0.02 to 0.08
 - PTCH2SRV_P from 0.6 to 1.0
 - PTCH2SRV_I from 0.1 to 0.3
 - PTCH2SRV_D from 0.02 to 0.08
 - TECS_PITCH_MAX from 0 to 15
 - NAVL1_PERIOD from 20 to 17

The PID parameter changes won't affect anyone who has done some
tuning, but will make the first flight experience for a new user be a
lot better for nearly all aircraft. Paul and I decided on the values
based on experience with users flight logs, plus the experience users
have had with AUTOTUNE mode.

Please report testing results!

Happy flying

Release 3.9.1beta1, 15th August 2018
------------------------------------

This is a the first beta for the 3.9.1 release, which is a minor
update over 3.9.0. This release includes the following changes:

 - improved DShot and BLHeli-passthrough support, fixing errors
   reported by BLHeli32 ESCs and making using BLHeliSuite32 much more
   reliable

 - new tonealarm system, bringing the tones produced under ChibiOS and
   Linux HALs in line with tones produced under the HAL_PX4 HAL.

 - added a new LED driver for the NCP5623 LED

 - fixed delay caused by parameter writes in AP_Stats

 - fixed default RSSI input pin on some boards

 - fixed the incorrect display of "no io thread heartbeat" message

 - fixed microSD support on some boards due to running out of DMA
   capable memory

Given the changes are quite small, I hope to make this a fairly short
beta cycle. Please test it and report both success and failure.

Happy flying!

Release 3.9.0, 6th August 2018
------------------------------

The ArduPilot development team is delighted to announce a new stable
release of plane, version 3.9.0.

For those of you who have been testing the 3.9.0beta releases then you
won't see any surprises here. That are no changes since
3.9.0beta6. Also, many thanks for testing the betas!

For those of you coming from the last stable 3.8.5 release there are a
lot of changes. For a start, this is the first release to support both
the ChibiOS RTOS on STM32 boards and the NuttX RTOS. Previously stable
releases only used the NuttX RTOS. The ArduPilot project is moving to
ChibiOS for future releases, and we expect to drop support for the
NuttX builds for the next major stable release (which will probably be
called 3.10.0).

When you install the 3.9.0 release on board such as a Pixhawk1, Cube
or Pixracer you have a choice of which build to use. Choosing ChibiOS
will give you better performance and some very nice new
features. Choosing the NuttX (also known as "PX4" builds) will get you
the same base that we have been using for years.

There are a few features that are in the NuttX build but not yet in
the ChibiOS build, in particular the ChibiOS build does not yet
support PWM based rangefinders. That will be fixed in a future
release.

This release has a lot of new features. Some of the most important
ones are:

 - DShot support for controlling ESCs
 - BLHeli pass-thru support for ESC configuration
 - automatic compass orientation on calibrating compasses
 - improved VTOL flight code, with improved transition support and loiter
 - support for Devo telemetry output
 - new battery monitoring system, with more flexible failsafes
 - built-in OSD support for boards with a MAX7456 OSD device

In addition, this release supports a lot of new flight boards,
including:

 - AirbotF4
 - F4BY
 - KakuteF4
 - KakuteF7
 - MatekF405
 - MatekF405-Wing
 - mindpx-v2
 - mini-pix
 - Omnibusf4pro
 - Omnibusf7V2
 - Pixhawk4 and Pixhawk4-mini
 - CUAVv5
 - revo and revo-mini
 - sparky2

We also now have custom ChibiOS based builds for some existing boards,
including:

 - CubeBlack
 - mRoX21
 - Pixhawk1
 - Pixracer

This means you now have a lot more choice in selecting a flight board
to use with ArduPilot.

This release was made possible thanks to contributions from dozens of
volunteer developers in the dev team. In total there are over 5
thousand changes since the 3.8.5 release. Special thanks to:

 - Siddharth Purohit
 - Tom Pittenger
 - Randy Mackay
 - Michael du Breuil
 - Peter Barker
 - Mark Whitehorn
 - Paul Riseborough
 - Francisco Ferreira
 - Jonathan Challinger
 - Leonard Hall
 - Alexander Malishev
 - Nathan E
 - Marco Robustini
 - Luis Vale Gonçalves
 - night-ghost
 - Patrick José Pereira
 - Lucas De Marchi
 - Eugene Shamaev
 - Philip Rowse
 - Amilcar Lucas
 - Kelly Foster
 - Fnoop
 - Pierre Kancir
 - Stephen Dade
 - Jaime Machuca
 - vierfuffzig
 - Henry Wwurzburg
 - Malcolm Churn
 - Holger Steinhaus

We hope you enjoy flying this release as much as we enjoyed making
it. Happy flying!

Release 3.9.0beta6, 30th July 2018
----------------------------------

This is the sixth in a series of beta releases for plane 3.9.0. I aims
to be the final beta.

Changes in this release are:

 - added Q_TAILSIT_THSCMX parameter for tailsitters (thanks to IamPete)
 - added KakuteF7 bootloader
 - fixed arming error with trim on unusued RC channels
 - fixed microSD support on KakureF7 and OmnibusF7V2 (thanks sh83)
 - fixed VL53L0X rangefinder driver
 - added Pixhawk1 ChibiOS build
 - fixed update rate for servos in tailsitters
 - set COMPASS_AUTO_ROT to 2 for most boards
 - improved OmnibusF7V2 support
 - support all external compasses on mini-pix
 - clear COMPASS_DEV_ID for unused compasses

Happy flying!

Release 3.9.0beta5, 19th July 2018
----------------------------------

This is the fifth in a series of beta releases for plane 3.9.0. It
fixes two important bugs over 3.9.0beta4

 - fixed RC failsafe on IOMCU when a SBUS receiver is set to "hold" mode
 - allowed for RC override failsafe to support holding override value
   on timeout

Thanks to Jon and Michael for the failsafe fixes!

It also includes two smaller changes:

 - updated F4BY board id, and added F4BY to autobuild list
 - fixed UARTD for px4-v1 build

Release 3.9.0beta4, 18th July 2018
----------------------------------

This is the fourth in a series of beta releases for plane 3.9.0. It is
a major release, with a lot of changes.

 - fixed compass detection on pixracer
 - greatly improved OSD support
 - fixed ARMING_REQUIRE=2 support (thanks Marco!)
 - added automatic compass orientation support
 - added probe for all external compasses on new ChibiOS boards
 - fixed arming with compass disabled
 - fixed flow control on F7 based boards
 - fixed prealloc of DMA bouncebuffers
 - added board specific builds for mRoX21 and Pixracer
 - fixed mRoX21 IMU detection
 
Many thanks to all who contributed to this release, and our beta testers!

Release 3.9.0beta3, 11th July 2018
----------------------------------

This is the third in a series of beta releases for plane 3.9.0. It is
a minor update to beta2

Changes are:

- fixed nul termination of system ID on ChibiOS
- added PH4-mini support
- fixed compass orientation on fmuv5
- matched BRD_PWM_COUNT between px4-v3 and fmuv3
- fixed MatekF405-Wing compass orientation
- backup storage to microSD if possible


Release 3.9.0beta2, 6th July 2018
---------------------------------

This is the second in a series of beta releases for plane 3.9.0. It is
a major release, with a lot of changes.

 - Builtin OSD support on boards with MAX7456 OSD device
 - fixed throttle display on twin-engine planes
 - improved timing in mavlink remote sensors
 - added many more board types to autobuild
 - fixed airspeed reporting for unhealthy sensors
 - fixed log timestamps on ChibiOS
 - added force arm option
 - added fmuv5 support
 - added CubeBlack build
 - added ESC telemetry virtual battery
 - added SERIAL6 on fmuv4 and fmuv5
 - added MatekF405-Wing support
 - added AP_Bootloader
 - support bootloader update over MAVLink
 - enable sdcard on several F4 boards
 - fixed serial number logging on ChibiOS boards
 - fixed revo-mini compass orientation

Many thanks to all who contributed to this release, and our beta testers!

Release 3.9.0beta1, 21st May 2018
---------------------------------

This is the first in a series of beta releases for plane 3.9.0. It is
a major release, with a lot of changes.

The main changes are for supporting two new HALs (hardware abstraction
layers). One is the port to ChibiOS and the second is the F4Light HAL.

These ports bring a host of new flight board options, and a lot of new
features. Some highlights include:

 - support for many inexpensive STM32F4 based flight boards

 - support for DShot output for BLHeli ESCs, including ESC telemetry
   feedback

 - new battery monitoring system with more flexible failsafe options

 - support for Devo telemetry output

 - a new position hold library for multicopters which improves
   quadplane VTOL support

This release is fully compatible with previous releases, and users
should not notice a significant difference in flight. The focus of the
beta testing will be on ensuring that the new flight board support is
stable.

Happy flying!

Release 3.8.5, 25th April 2018
------------------------------

This release includes small bug fixes and one safety fix. The changes
are:

 - fixed an issue where the external safety button can activate in
   flight on some boards, causing them to crash. A new parameter
   BRD_SAFETYOPTION is added which controls the behaviour of the
   safety button. The default is to de-activate the safety button when
   armed.

 - fixed default orientation of ICM-20948 compass for Here GPS

 - added support for dual airspeed sensors

 - added support for the SDP33 airspeed sensor. This is still
   considered experimental. There are reports of it underestimating
   the aircrafts speed at higher altitudes.

 - add support for the MS5525 airspeed sensor on multiple I2C
   addresses. Two new values of the ARSDP_TYPE are introduced (4 and
   5) for specific I2C addresses. This allows you to deconflict the
   MS5525 from a MS5611 barometer on the same bus.

Release 3.8.4, 9th January 2018
-------------------------------

This is a minor release with just two small changes.

The first change is to fix a timing bug in the MS5525 airspeed sensor
driver. That bug was causing unreliable airspeed sensing. Thanks to
David Ingraham for providing logs that helped find the issue.

The second change is to fix transitions in AUTO mode in
tailsitter. The transition for tailsitters was causing instability on
transition from vertical takeoff to forward flight.


Release 3.8.3, 13th November 2017
---------------------------------

This is a minor release with a number of important bug fixes and some
small feature additions.

The most important general fixes are for an I2C device detection bug
and fixing the implementation of MANUAL_RCMASK.

There are quite a number of small fixes in lots of areas, plus big
improvements to the handling of tailsitter transitions.

Another significant change is to the handling of transitions from RTL
to QRTL when Q_RTL_MODE=1. We now transition to QRTL at a distance
which is the maximum of RTL_RADIUS and a distance calculated based on
a new Q_TRANS_DECEL "transition deceleration" parameter. That allows
you to tune the amount of deceleration you want in the transition
according to how much drag your quadplane has. This also means it
automatically accounts for wind speed and approach speed in
calculating the transition point.

We have also added a new Q_OPTIONS parameter which is a bitmask of
option flags to control quadplane behaviour. We have three options so
far. The first is to allow for keeping the wings within
LEVEL_ROLL_LIMIT degrees during transitions, for users that prefer
transitions to be kept level. The 2nd and 3rd options are to treat
NAV_TAKEOFF as NAV_VTOL_TAKEOFF and NAV_LAND as NAV_VTOL_LAND in
quadplane missions, so that you can use GCS software that doesn't know
about the VTOL takeoff and land commands.

 - allow vertical takeoffs in GUIDED mode with Q_GUIDED_MODE=0

 - fixed a bug in implementation of MANUAL_RCMASK

 - eliminate airspeed positive bias after offset zero

 - prevent quadplane controller windup on the ground

 - added Q_MAV_TYPE for setup of VTOL vehicle type for QGC

 - improved performance of FlightAxis SITL

 - support LOITER_TO_ALT in quadplanes

 - fixed a bug in TECS related to descending quadplane transitions

 - added Q_OPTIONS for controlling roll during transitions and
   allowing for fixed wing takeoff and landing

 - fixed a race condition in IMU logging

 - fixed a race in i2c device probing

 - log critical messages while disarmed

 - added IST8310 compass on FMUv3 I2C

 - probe for QMC5883 by default

 - avoid double detection of AK09916 compass

 - smooth out tailsitter transitions in both directions

 - fixed use of RTL_RADIUS for QRTL threshold and added Q_TRANS_DECEL

 - fixed course unlocking with rudder in CRUISE mode

We hope you have as much fun flying this release as we had producing
it. Happy flying!

Release 3.8.2, 11th September 2017
----------------------------------

This is a minor release, with a few small fixes and one important
crash bug fix.

The crash bug is in the Septentrio (SBF) GPS driver. Anyone flying
with an SBF GPS should update to this release. The driver had a bug
that could cause a in-flight crash under some circumstances.

Other changes in this release are:

 - support takeoff in GUIDED mode for quadplanes

 - support changing target altitude while hovering in GUIDED mode for
   quadplanes

 - improved descent rate control in QRTL and QLAND modes. This fixes
   an issue with higher than configured descent rates, and smoothes
   out the change in descent rate for the change to the final landing
   stage

 - smooth out the transition to QLOITER or QLAND when transitioning
   from fixed wing mode. This prevents the sudden nose up when
   changing to QLOITER or QLAND mode while flying at high speed.

 - added MANUAL_CONTROL support, allowing for joystick control from QGroundControl

Happy flying!

Release 3.8.1, 3rd September 2017
---------------------------------

This is a bugfix release for 3.8.0, with just a few small changes, and
only one new feature.

The bug fixes are:

 - removed TRIM_RC_AT_START option, as this was causing significant
   issues

 - fixed bug in TRIM_AUTO support with differential spoilers

 - fixed bug with RCn_TRIM == RCn_MIN in MANUAL mode

 - fixed combination of TKOFF_THR_MINACC and TKOFF_THR_DELAY

 - fixed a bug in sending STATUSTEXT messages on all MAVLink channels

The new feature is that you can set the protocol for the debug UART on
FMUv2 or FMUv3 board (such as a Pixhawk or Cube) to allow allow UART5
to be used for arbitrary protocols. To use this fetaure just set
SERIAL5_PROTOCOL to the desired serial protocol. This should be useful
to users who have multiple serial devices (such as rangefinders) they
want to connect and have run out of UARTs.

Happy flying!


Release 3.8.0, 5th August 2017
------------------------------

The ArduPilot development team is very proud to announce the release
of APM:Plane 3.8.0. This is a major release, with a lot of new
features and improvements. The release has been a long time coming,
and the dev team would like to thank everyone who has contributed,
in terms of code and documentation as well as the many testers of beta
versions of the code.

To help with migration from the 3.7.1 release to 3.8.0 there is a
migration guide here:

  http://ardupilot.org/plane/docs/plane-3-7-to-3-8-migration.html

All users should read the migration guide carefully. While the
firmware will try to auto-migrate most settings, please do very
careful ground tests before your first flight with 3.8.

In total there have been over 4 thousand changes in the ArduPilot git
tree since the 3.7.1 release, with over four hundred of those specific
to fixed wing aircraft. Listing all of them in these release notes is
not practical, but the following list will give some of the larger
changes:

 - addition of EKF3 support (disabled by default)
 - new SERVO parameter system for servo output configuration
 - support for MS5525 airspeed sensor
 - support for a wide range of tiltrotor quadplanes
 - support for tailsitter aircraft (including vectored tailsitters)
 - support for twin-motor aircraft, with differential thrust
 - new system for elevon, vtail, flaperon and differential spoiler setup
 - support for deep stall landings
 - support for dual-gps blending
 - support for masking compass types for driver loading
 - completely new DMA based and high sample rate IMU drivers
 - support for px4pro board from Drotek
 - several new compass drivers, new IMU drivers and new GPS drivers
 - new MANUAL_RCMASK system for fine-grained MANUAL flight setup
 - support for log rotation on disarm
 - numerous quadplane improvements
 - fixed loiter behaviour with Q_GUIDED_MODE=1
 - allow rudder arming in CRUISE and FBWB modes
 - fixed bug in transmitter tuning support
 - fixed bug in attitude integrator zero on mode change
 - added SYSID_ENFORCE parameter
 - fixed support for vtail rudder-only planes
 - greatly improved automatic landing accuracy
 - support for high update rates on all servos with SERVO_RATE parameter
 - greatly improved UAVCAN support, with parameters now in CAN_ parameter space
 - support for prop-hang on 3D aircraft
 - improved quadplane PID logging
 - smoother takeoffs for quadplanes
 - logging of side-slip and angle-of-attack estimates
 - added AETR, pre-mixer logging
 - automatic thermalling support

Release 3.8.0beta5, 1st May 2017
--------------------------------

The ArduPilot development team is proud to announce the release of
version 3.8.0beta of APM:Plane. This is a major release. All users
should carefully check their setup when upgrading.

Major changes include:

 - new SRV_Channel system for setting up of output servos separately
   from input RC channels
 - new elevon and vtail support system
 - new EKF3 state estimator (not enabled by default yet)
 - support for tailsitter aircraft
 - greatly improved support for tilt-rotor quadplanes
 - greatly improved quadplane transition code
 - support for deepstall landing
 - large improvements in airspeed noise handling
 - much improved RTK GPS support
 - support automatic servo trimming
 - greatly improved automatic landing accuracy
 - significant improvements in altitude handling
 - significant improvement in sensor drivers, with higher sample rates
   and improved vibration resistance

On top of that there have been hundreds of smaller improvements and
bug fixes.

From the users point of view the most significant change in setup is
moving of RCn_FUNCTION parameters into new SERVOn_FUNCTION parameters,
and the creation of separate output MIN/MAX/TRIM values for all servo
outputs. This allows for much more flxible output setup that is
independent of RC input setup.

Release 3.7.1, 21st October 2016
--------------------------------

The ArduPilot development team is proud to announce the release of
version 3.7.1 of APM:Plane. This is a minor bug fix release. A major
release of 3.8.0 is also being prepared now and will be in beta soon.

Changes in this release:

 - fixed arming check error on FMUv4 (pixracer)
 - fixed pilot throttle based motor test for quadplane
 - fixed use of multicopter mix_max in quadplane
 - fixed AFS termination bugs for quadplanes
 - added automatic ICE engine cut in QLAND
 - fixed loiter mode with Q_GUIDED_MODE=1
 - fixed navigation at lattitude of exactly zero
 - fixed quadplane transition without airspeed sensor
 - fixed rudder arming in CRUISE and FBWB modes
 - fixed throttle slew rate in MANUAL mode for quadplanes
 - fixed multiple bugs in transmitter tuning for quadplane rates
 - fixed mavlink system ID in early startup on USB
 - slow down update of home position
 - update Disco battery lookup table
 - improved compass calibration code for larger offsets

Many thanks to everyone who contributed to this release!

Release 3.7.0, 9th September 2016
---------------------------------

The ArduPilot development team is proud to announce the release of
version 3.7.0 of APM:Plane. This is a major update so please read the
notes carefully.

The biggest changes in this release are:

 - more reliable recovery from inverted flight
 - automatic IC engine support
 - Q_ASSIST_ANGLE for stall recovery on quadplanes
 - Pixhawk2 IMU heater support
 - PH2SLIM support
 - AP_Module support
 - Parrot Disco support
 - major VRBrain support merge
 - much faster boot time on Pixhawk

I'll give a bit of detail on each of these changes before giving the
more detailed list of changes.

More reliable recovery from inverted flight

Marc Merlin discovered that on some types of gliders that ArduPilot
would not reliably recover from inverted flight. The problem turned
out to be the use of the elevator at high bank angles preventing the
ailerons from fully recovering attitude. The fix in this release
prevent excessive elevator use when the aircraft is beyond
LIM_ROLL_CD. This should help a lot for people using ArduPilot as a
recovery system for manual FPV flight.

Automatic IC engine support

ArduPilot has supported internal combustion engines for a long time,
but until now the pilot has had to control the ignition and starter
manually using transmitter pass throughs. A new "ICE" module in
ArduPilot now allows for fully automatic internal combustion engine
support.

Coupled with an RPM sensor you can setup your aircraft to
automatically control the ignition and starter motor, allowing for one
touch start of the motor on the ground and automatic restart of the
motor in flight if needed.

The IC engine support is also integrated into the quadplane code,
allowing for automatic engine start at a specified altitude above the
ground. This is useful for tractor engine quadplanes where the
propeller could strike the ground on takeoff. The engine can also be
automatically stopped in the final stage of a quadplane landing.

Q_ASSIST_ANGLE for stall recovery

Another new quadplane feature is automatic recovery from fixed wing
stall. Previously the VTOL motors would only provide assistance in
fixed wing modes when the aircraft airspeed dropped below
Q_ASSIST_SPEED. Some stalls can occur with higher airspeed however,
and this can result in the aircraft losing attitude control without
triggering a Q_ASSIST_SPEED recovery. A new parameter Q_ASSIST_ANGLE
allows for automatic assistance when attitude control is lost,
triggering when the attitude goes outside the defined roll and pitch
limits and is more than Q_ASSIST_ANGLE degrees from the desired
attitude. Many thanks to Iskess for the suggestion and good discussion
around this feature.

Pixhawk2 heated IMU support

This release adds support for the IMU heater in the upcoming Pixhawk2,
allowing for more stable IMU temperatures. The Pixhawk2 is
automatically detected and the heater enabled at boot, with the target
IMU temperature controllable via BRD_IMU_TARGTEMP.

Using an IMU heater should improve IMU stability in environments with
significant temperature changes.

PH2SLIM Support

This release adds support for the PH2SLIM variant of the Pixhawk2,
which is a Pixhawk2 cube without the isolated sensor top board. This
makes for a very compact autopilot for small aircraft. To enable
PH2SLIM support set the BRD_TYPE parameter to 6 using a GCS connected
on USB.

AP_Module Support

This is the first release of ArduPilot with loadable module support
for Linux based boards. The AP_Module system allows for externally
compiled modules to access sensor data from ArduPilot controlled
sensors. The initial AP_Module support is aimed at vendors integrating
high-rate digital image stabilisation using IMU data, but it is
expected this will be expanded to other use cases in future releases.

Parrot Disco Support

This release adds support for the Parrot C.H.U.C.K autopilot in the
new Disco airframe. The Disco is a very lightweight flying wing with
a nicely integrated Linux based autopilot. The Disco flies very nicely
with ArduPilot, bringing the full set of mission capabilities of
ArduPilot to this airframe.

Major VRBrain Support Update

This release includes a major merge of support for the VRBrain family
of autopilots. Many thanks to the great work by Luke Mike in putting
together this merge!

Much Faster Boot Time

Boot times on Pixhawk are now much faster due to a restructuring of
the driver startup code, with slow starting drivers not started unless
they are enabled with the appropriate parameters. The restructuring
also allows for support of a wide variety of board types, including
the PH2SLIM above.

This release includes many other updates right across the flight
stack, including several new features. Some of the changes include:

 - improved quadplane auto-landing
 - limit roll and pitch by Q_ANGLE_MAX in Q modes
 - improved ADSB avoidance and MAVLink streaming
 - smoother throttle control on fixed-wing to VTOL transition
 - removed "demo servos" movement on boot
 - fixed a problem with spurious throttle output during boot (thanks
   to Marco for finding this)
 - support MAVLink SET_ATTITUDE_TARGET message
 - log all rally points on startup
 - fixed use of stick mixing for rudder with STICK_MIXING=0
 - fixed incorrect tuning warnings when vtol not active
 - support MAVLink based external GPS device
 - support LED_CONTROL MAVLink message
 - prevent baro update while disarmed for large height change
 - support PLAY_TUNE MAVLink message
 - added AP_Button support for remote button input reporting
 - support Ping2020 ADSB transceiver
 - fixed disarm by rudder in quadplanes
 - support 16 channel SERVO_OUTPUT_RAW in MAVLink2
 - added automatic internal combustion engine support
 - support DO_ENGINE_CONTROL MAVLink message
 - added ground throttle suppression for quadplanes
 - added MAVLink reporting of logging subsystem health
 - prevent motor startup on reboot in quadplanes
 - added quadplane support for Advanced Failsafe
 - added support for a 2nd throttle channel
 - fixed bug in crash detection during auto-land flare
 - lowered is_flying groundspeed threshold to 1.5m/s
 - added support for new FrSky telemetry protocol variant
 - added support for fence auto-enable on takeoff in quadplanes
 - added Q_ASSIST_ANGLE for using quadplane to catch stalls in fixed wing flight
 - added BRD_SAFETY_MASK to allow for channel movement for selected channels with safety on
 - numerous improvements to multicopter stability control for quadplanes
 - support X-Plane10 as SITL backend
 - lots of HAL_Linux improvements to bus and thread handling
 - fixed problem with elevator use at high roll angles that could
   prevent attitude recovery from inverted flight
 - improved yaw handling in EKF2 near ground
 - added IMU heater support on Pixhawk2
 - allow for faster accel bias learning in EKF2
 - fixed in-flight yaw reset bug in EKF2
 - added AP_Module support for loadable modules
 - support Disco airframe from Parrot
 - use full throttle in initial takeoff in TECS
 - added NTF_LED_OVERRIDE support
 - added terrain based simulation in SITL
 - merged support for wide range of VRBrain boards
 - added support for PH2SLIM and PHMINI boards with BRD_TYPE
 - greatly reduced boot time on Pixhawk and similar boards
 - fixed magic check for signing key in MAVLink2
 - fixed averaging of gyros for EKF2 gyro bias estimate
 

Release 3.6.0, 6th June 2016
----------------------------

The ArduPilot development team is proud to announce the release of
version 3.6.0 of APM:Plane. This is a major update so please read the
notes carefully.

The biggest changes in this release are:

 - major update to PX4Firmware code
 - major update to QuadPlane code
 - addition of MAVLink2 support

The updated PX4Firmware tree greatly improves support for the new
Pixracer boards as well as improving scheduling performance and UAVCAN
support.

The QuadPlane changes are very extensive in this release. A lot of new
features have been added, including:

 - improved automatic weathervaning
 - greatly improved support for mixed fixed wing and VTOL missions
 - automatic RTL with VTOL land
 - VTOL GUIDED mode support
 - greatly improved transition code
 - new tuning system for VTOL motors
 - extensive upgrade to logging system for much better flight analysis
 
The new QuadPlane features are documented at:

  http://ardupilot.org/plane/docs/quadplane-support.html

There is also a prototype implementation supporting tiltrotors and
tiltwings, but so far it has only been flown in simulations and it
should be considered very experimental.

Detailed changes include:

 - added motortest for all quad motors in sequence
 - merge upstream PX4Firmware changes
 - new AC_AttitudeControl library from copter for quadplane
 - modified default gains for quadplanes
 - new velocity controller for initial quadplane landing
 - smooth out final descent for VTOL landing
 - changed default loop rate for quadplanes to 300Hz
 - support up to 16 output channels (two via SBUS output only)
 - fixed bug with landing flare for high values of LAND_FLARE_SEC
 - improved crash detection logic
 - added in-flight transmitter tuning
 - fix handling of SET_HOME_POSITION
 - added Q_VFWD_GAIN for forward motor in VTOL modes
 - added Q_WVANE_GAIN for active weathervaning
 - log the number of lost log messages
 - Move position update to 50hz loop rather then the 10hz
 - Suppress throttle when parachute release initiated, not after release.
 - support Y6 frame class in quadplane
 - log L1 xtrack error integrator and remove extra yaw logging
 - limit roll before calculating load factor
 - simplify landing flare logic
 - smooth-out the end of takeoff pitch by reducing takeoff pitch min via TKOFF_PLIM_SEC
 - added support for DO_VTOL_TRANSITION as a mission item
 - fixed is_flying() for VTOL flight
 - added Q_ENABLE=2 for starting AUTO in VTOL
 - reload airspeed after VTOL landing
 - lower default VTOL ANGLE_MAX to 30 degrees
 - Change mode to RTL on end of mission rather then staying in auto
 - implemented QRTL for quadplane RTL
 - added Q_RTL_MODE parameter for QRTL after RTL approach
 - reduced the rate of EKF and attitude logging to 25Hz
 - added CHUTE_DELAY_MS parameter
 - allow remapping of any input channel to any output channel
 - numerous waf build improvements
 - support fast timer capture for camera trigger feedback
 - numerous improvements for Pixracer support
 - added more general tiltrotor support to SITL
 - only save learned compass offsets when disarmed
 - support MISSION_ITEM_INT for more accurate waypoint positions
 - change parachute deployment altitude to above ground not home
 - added AP_Tuning system for QuadPlane tuning
 - added initial support for tiltrotors and tiltwings
 - added LOG_REPLAY and LOG_DISARMED parameters
 - added Q_GUIDED_MODE parameter
 - major update to QuadPlane documentation
 - added MAVLink2 support
 - fixed origin vs home altitude discrepancy
 - improved Lidar based landing glide slope
 - fixed throttle failsafe with THR_PASS_STAB=1
 - prevent EKF blocking during baro and airspeed cal
 - allow for ground testing of parachutes with CHUTE_MINALT=0
 - fixed elevator stick mixing for above 50% input
 - added QuadPlane ESC calibration
 


Release 3.6.0beta1, 30th April 2016
-----------------------------------

The ArduPilot development team is proud to announce the release of
version 3.6.0beta1 of APM:Plane. This is the first beta version of a
major release.

The biggest changes in this release is the major update of the
PX4Firmware tree which greatly improves support for the Pixracer
board, and a lot of QuadPlane improvements and new features.

Detailed changes include:

 - added motortest for all quad motors in sequence
 - merge upstream PX4Firmware changes
 - new AC_AttitudeControl library from copter for quadplane
 - modified default gains for quadplanes
 - new velocity controller for initial quadplane landing
 - smooth out final descent for VTOL landing
 - changed default loop rate for quadplanes to 300Hz
 - support up to 16 output channels (two via SBUS output only)
 - fixed bug with landing flare for high values of LAND_FLARE_SEC
 - improved crash detection logic
 - added in-flight transmitter tuning
 - fix handling of SET_HOME_POSITION
 - added Q_VFWD_GAIN for forward motor in VTOL modes
 - added Q_WVANE_GAIN for active weathervaning
 - log the number of lost log messages
 - Move position update to 50hz loop rather then the 10hz
 - Suppress throttle when parachute release initiated, not after release.
 - support Y6 frame class in quadplane
 - log L1 xtrack error integrator and remove extra yaw logging
 - limit roll before calculating load factor
 - simplify landing flare logic
 - smooth-out the end of takeoff pitch by reducing takeoff pitch min via TKOFF_PLIM_SEC
 - added support for DO_VTOL_TRANSITION as a mission item
 - fixed is_flying() for VTOL flight
 - added Q_ENABLE=2 for starting AUTO in VTOL
 - reload airspeed after VTOL landing
 - lower default VTOL ANGLE_MAX to 30 degrees
 - Change mode to RTL on end of mission rather then staying in auto
 - implemented QRTL for quadplane RTL
 - added Q_RTL_MODE parameter for QRTL after RTL approach
 - reduced the rate of EKF and attitude logging to 25Hz
 - added CHUTE_DELAY_MS parameter
 - allow remapping of any input channel to any output channel
 - numerous waf build improvements
 - support fast timer capture for camera trigger feedback
 - numerous improvements for Pixracer support
 - added more general tiltrotor support to SITL


Release 3.5.3, 30th April 2016
------------------------------

The ArduPilot development team is proud to announce the release of
version 3.5.3 of APM:Plane. This is a minor release with only small
bugfix changes.

The main motivation for the release is a problem with flying without a
compass enabled. If you fly 3.5.2 with MAG_ENABLE=0 or no compass
attached then there is a risk that the EKF2 attitude estimator may
become unstable before takeoff. This can cause the aircraft to crash.

The other changes in this release are:

 - fixed loiter radius for counter-clockwise loiter
 - fixed the loiter radius when doing a RTL at the end of a mission
 - provide reasons to the GCS when a uBlox GPS fails to properly configure
 - support a wider variety of NMEA GPS receivers
 - use EKF1 by default if no compass is enabled

Happy flying!

Release 3.5.2, 26th March 2016
------------------------------

The ArduPilot development team is proud to announce the release of
version 3.5.2 of APM:Plane. This is a minor release with small
changes.

The main reason for this release over 3.5.1 is a fix for a bug where
the px4io co-processor on a Pixhawk can run out of memory while
booting. This causes the board to be unresponsive on boot. It only
happens if you have a more complex servo setup and is caused by too
much memory used by the IO failsafe mixer.

The second motivation for this release is to fix an issue where during
a geofence altitude failsafe that happens at low speed an aircraft may
dive much further than it should to gain speed. This only happened if
the thrust line of the aircraft combined with low pitch integrator
gain led to the aircraft not compensating sufficiently with elevator
at full throttle in a TECS underspeed state. To fix this two changes
have been made:

 - a minimum level of integrator in the pitch controller has been
   added. This level has a sufficiently small time constant to avoid
   the problem with the TECS controller in an underspeed state.

 - the underspeed state in TECS has been modified so that underspeed
   can end before the full target altitude has been reached, as long
   as the airspeed has risen sufficiently past the minimum airspeed
   for a sufficient period of time (by 15% above minimum airspeed for
   3 seconds).

The default P gains for both roll and pitch have also been raised from
0.4 to 0.6. This is to help for users that fly with the default
parameters. A value of 0.6 is safe for all aircraft that I have
analysed logs for.

The default gains and filter frequencies of the QuadPlane code have
also been adjusted to better reflect the types of aircraft users have
been building.

Other changes include:

 - improved QuadPlane logging for better analysis and tuning (adding
   RATE and QTUN messages)
 - fixed a bug introduced in 3.5.1 in rangefinder landing
 - added TECS logging of speed_weight and flags
 - improvements to the lsm303d driver for Linux
 - improvements to the waf build system


Release 3.5.1, 21st March 2016
------------------------------

The ArduPilot development team is proud to announce the release of
version 3.5.1 of APM:Plane. This is a minor release with primarily
small changes.

The changes in this release are:

 - update uavcan to new protocol
 - always exit loiter in AUTO towards next waypoint
 - support more multicopter types in quadplane
 - added support for reverse thrust landings
 - added LAND_THR_SLEW parameter
 - added LAND_THEN_NEUTRL parameter
 - fixed reporting of armed state with safety switch
 - added optional arming check for minimum voltage
 - support motor test for quadplanes
 - added QLAND flight mode (quadplane land mode)
 - added TECS_LAND_SRC (land sink rate change)
 - use throttle slew in quadplane transition
 - added PID tuning for quadplane
 - improved text message queueing to ground stations
 - added LAND_THR_SLEW parameter
 - re-organisation of HAL_Linux bus API
 - improved NMEA parsing in GPS driver
 - changed TECS_LAND_SPDWGT default to -1
 - improved autoconfig of uBlox GPS driver
 - support a wider range of Lightware serial Lidars
 - improved non-GPS performance of EKF2
 - allow for indoor flight of quadplanes
 - improved compass fusion in EKF2
 - improved support for Pixracer board
 - improved NavIO2 support
 - added BATT_WATT_MAX parameter

The reverse thrust landing is particularly exciting as that adds a
whole new range of possibilities for landing in restricted areas. Many
thanks to Tom for the great work on getting this done.

The uavcan change to the new protocol has been a long time coming, and
I'd like to thank Holger for both his great work on this and his
patience given how long it has taken to be in a release. This adds
support for automatic canbus node assignment which makes setup much
easier, and also supports the latest versions of the Zubax canbus GPS.

My apologies if your favourite feature didn't make it into this
release! There are a lot more changes pending but we needed to call a
halt for the release eventually. This release has had a lot of flight
testing and I'm confident it will be a great release.

Happy flying!


Release 3.5.0, 30th January 2016
--------------------------------

The ArduPilot development team is proud to announce the release of
version 3.5.0 of APM:Plane. This is a major release with a lot of
changes so please read the notes carefully!

The biggest changes in this release are:

 - switch to new EKF2 kalman filter for attitude and position estimation
 - added support for parachutes
 - added support for QuadPlanes
 - support for 4 new flight boards, the QualComm Flight, the BHAT,
   the PXFmini and the Pixracer
 - support for arming on moving platforms
 - support for better camera trigger logging

New Kalman Filter

The 3.4 release series was the first where APM:Plane used a Kalman
Filter by default for attitude and position estimation. It works very
well, but Paul Riseborough has been working hard recently on a new
EKF variant which fixes many issues seen with the old estimator. The
key improvements are:

  - support for separate filters on each IMU for multi-IMU boards
    (such as the Pixhawk), giving a high degree of redundency
  - much better handling of gyro drift estimation, especially on
    startup
  - much faster recovery from attitude estimation errors

After extensive testing of the new EKF code we decided to make it the
default for this release. You can still use the old EKF if you want to
by setting AHRS_EKF_TYPE to 1, although it is recommended that the new
EKF be used for all aircraft.

Parachute Support

This is the first release with support for parachute landings on
plane. The configuration and use of a parachute is the same as the
existing copter parachute support. See
http://copter.ardupilot.org/wiki/parachute/

Note that parachute support is considered experimental in planes.

QuadPlane Support

This release includes support for hybrid plane/multi-rotors called
QuadPlanes. More details are available in this blog post:
http://diydrones.com/profiles/blogs/quadplane-support-in-apm-plane-3-5-0

Support for 4 new Flight Boards

The porting of ArduPilot to more flight boards continues, with support
for 3 new flight boards in this release. They are:

 - the BHAT board
 - the PXFmini
 - the QualComm Flight
 - the Pixracer

More information about the list of supported boards is available here:
https://ardupilot.org/copter/docs/common-autopilots.html

Startup on a moving platform

One of the benefits of the new EKF2 estimator is that it allows for
rapid estimation of gyro offset without doing a gyro calibration on
startup. This makes it possible to startup and arm on a moving
platform by setting the INS_GYR_CAL parameter to zero (to disable gyro
calibration on boot). This should be a big help when flying off boats.

Improved Camera Trigger Logging

This release adds new CAM_FEEDBACK_PIN and CAM_FEEDBACK_POL
parameters. These add support for separate CAM and TRIG log messages,
where TRIG is logged when the camera is triggered and the CAM message
is logged when an external pin indicates the camera has actually
fired. This pin is typically based on the flash hotshoe of a camera
and provides a way to log the exact time of camera triggering more
accurately. Many thanks to Dario Andres and Jaime Machuca for their
work on this feature.

That is just a taste of all of the improvements in this release. In
total the release includes over 1500 patches. Some of the other more
significant changes include:

- RPM logging
- new waf build system
- new async accel calibrator
- SITL support for quadplanes
- improved land approach logic
- better rangefinder power control
- ADSB adapter support
- dataflash over mavlink support
- settable main loop rate
- hideable parameters
- improved crash detection logic
- added optional smooth speed weighting for landing
- improved logging for dual-GPS setups
- improvements to multiple RTK GPS drivers
- numerous HAL_Linux improvements
- improved logging of CAM messages
- added support for IMU heaters in HAL_Linux
- support for RCInput over UDP in HAL_Linux
- improved EKF startup checks for GPS accuracy
- added raw IMU logging for all platforms
- added BRD_CAN_ENABLE parameter
- support FlightGear visualisation in SITL
- configurable RGB LED brightness
- added RTL_RADIUS parameter
- improvements to the OVERRIDE_CHAN handling, fixing a race condition
- added OVERRIDE_SAFETY parameter

Many thanks to everyone who contributed to this release! The
development team is growing at a fast pace, with 57 people
contributing changes over this release cycle.

I'd like to make special mention of Tom Pittenger and Michael du
Breuil who have been doing extensive testing of the plane development
code, and also contributing a great deal of their own
improvements. Thanks!


Release 3.4.0, 24th September 2015
----------------------------------

The ArduPilot development team is proud to announce the release of
version 3.4.0 of APM:Plane. This is a major release with a lot of
changes so please read the notes carefully!

First release with EKF by default

This is the also the first release that enables the EKF (Extended
Kalman Filter) for attitude and position estimation by default. This
has been in development for a long time, and significantly improves
flight performance. You can still disable the EKF if you want to using
the AHRS_EKF_USE parameter, but it is strongly recommended that you
use the EKF. Note that if an issue is discovered with the EKF in
flight it will automatically be disabled and the older DCM system will
be used instead. That should be very rare.

In order to use the EKF we need to be a bit more careful about the
setup of the aircraft. That is why in the last release we enabled
arming and pre-arm checks by default. Please don't disable the arming
checks, they are there for very good reasons.

Last release with APM1/APM2 support

This will be the last major release that supports the old APM1/APM2
AVR based boards. We have finally run out of flash space and
memory. In the last few releases we spent quite a bit of time trying
to squeeze more and more into the small flash space of the APM1/APM2,
but it had to end someday if ArduPilot is to continue to develop. I am
open to the idea of someone else volunteering to keep doing
development of APM1/APM2 so if you have the skills and inclination do
please get in touch. Otherwise I will only do small point release
changes for major bugs.

Even to get this release onto the APM1/APM2 we had to make sacrifices
in terms of functionality. The APM1/APM2 release is missing quite a
few features that are on the Pixhawk and other boards. For example:

  - no rangefinder support for landing
  - no terrain following
  - no EKF support
  - no camera control
  - no CLI support
  - no advanced failsafe support
  - no HIL support (sorry!)
  - support for far fewer GPS types

that is just the most obvious major features that are missing on
APM1/APM2. There are also numerous other smaller things where we need
to take shortcuts on the APM1/APM2. Some of these features were
available on older APM1/APM2 releases but needed to be removed to
allow us to squeeze the new release onto the board. So if you are
happy with a previous release on your APM2 and want a feature that is
in that older release and not in this one then perhaps you shouldn't
upgrade.

PID Tuning

While most people are happy with autotune to tune the PIDs for their
planes, it is nice also to be able to do fine tuning by hand. This
release includes new dataflash and mavlink messages to help with that
tuning. You can now see the individual contributions of the P, I and D
components of each PID in the logs, allowing you to get a much better
picture of the performance.

A simple application of this new tuning is you can easily see if your
trim is off. If the Pitch I term is constantly contributing a
signifcant positive factor then you know that ArduPilot is having to
constantly apply up elevator, which means your plane is nose
heavy. The same goes for roll, and can also be used to help tune your
ground steering.

Vibration Logging

This release includes a lot more options for diagnosing vibration
issues. You will notice new VIBRATION messages in MAVLink and VIBE
messages in the dataflash logs. Those give you a good idea of your
(unfiltered) vibration levels. For really detailed analysis you can
setup your LOG_BITMASK to include raw logging, which gives you every
accel and gyro sample on your Pixhawk. You can then do a FFT on the
result and plot the distribution of vibration level with
frequency. That is great for finding the cause of vibration
issues. Note that you need a very fast microSD card for that to work!

Rudder Disarm

This is the first release that allows you to disarm using the rudder
if you want to. It isn't enabled by default (due to the slight risk of
accidentially disarming while doing aerobatics). You can enable it
with the ARMING_RUDDER parameter by setting it to 2. It will only
allow you to disarm if the autopilot thinks you are not flying at the
time (thanks to the "is_flying" heuristics from Tom Pittenger).

More Sensors

This release includes support for a bunch more sensors. It now supports
3 different interfaces for the LightWare range of Lidars (serial, I2C
and analog), and also supports the very nice Septentrio RTK
dual-frequency GPS (the first dual-frequency GPS we have support
for). It also supports the new "blue label" Lidar from Pulsed Light
(both on I2C and PWM).

For the uBlox GPS, we now have a lot more configurability of the
driver, with the ability to set the GNSS mode for different
constellations. Also in the uBlox driver we support logging of the raw
carrier phase and pseudo range data, which allows for post-flight RTK
analysis with raw-capable receivers for really accurate photo
missions.

Better Linux support

This release includes a lot of improvements to the Linux based
autopilot boards, including the NavIO+, the PXF and ERLE boards and
the BBBMini and the new RasPilot board. If you like the idea of flying
with Linux then please try it out!

On-board compass calibrator

We also have a new on-board compass calibrator, which also adds calibration
for soft iron effects, allowing for much more accurate compass
calibration. Support for starting the compass calibration in the
various ground stations is still under development, but it looks like
this will be a big improvement to compass calibration.

Lots of other changes!

The above list is just a taste of the changes that have gone into this
release. Thousands of small changes have gone into this release with
dozens of people contributing. Many thanks to everyone who helped!

Other key changes include:

  - fixed return point on geofence breach
  - enable messages for MAVLink gimbal support
  - use 64 bit timestamps in dataflash logs
  - added realtime PID tuning messages and PID logging
  - fixed a failure case for the px4 failsafe mixer
  - added DSM binding support on Pixhawk
  - added ALTITUDE_WAIT mission command
  - added vibration level logging
  - ignore low voltage failsafe while disarmed
  - added delta velocity and delta angle logging
  - fix LOITER_TO_ALT to verify headings towards waypoints within the loiter radius
  - allow rudder disarm based on ARMING_RUDDER parameter
  - fix default behaviour of flaps
  - prevent mode switch changes changing WP tracking
  - make TRAINING mode obey stall prevention roll limits
  - disable TRIM_RC_AT_START by default
  - fixed parameter documentation spelling errors
  - send MISSION_ITEM_REACHED messages on waypoint completion
  - fixed airspeed handling in SITL simulators
  - enable EKF by default on plane
  - Improve gyro bias learning rate for plane and rover
  - Allow switching primary GPS instance with 1 sat difference
  - added NSH over MAVLink support
  - added support for mpu9250 on pixhawk and pixhawk2
  - Add support for logging ublox RXM-RAWX messages
  - lots of updates to improve support for Linux based boards
  - added ORGN message in dataflash
  - added support for new "blue label" Lidar
  - switched to real hdop in uBlox driver
  - improved auto-config of uBlox
  - raise accel discrepancy arming threshold to 0.75
  - improved support for tcp and udp connections on Linux
  - switched to delta-velocity and delta-angles in DCM
  - improved detection of which accel to use in EKF
  - improved auto-detections of flow control on pixhawk UARTs
  - Failsafe actions are not executed if already on final approach or land.
  - Option to trigger GCS failsafe only in AUTO mode.
  - added climb/descend parameter to CONTINUE_AND_CHANGE_ALT
  - added HDOP to uavcan GPS driver
  - improved sending of autopilot version
  - prevent motor startup with bad throttle trim on reboot
  - log zero rangefinder distance when unhealthy
  - added PRU firmware files for BeagleBoneBlack port
  - fix for recent STORM32 gimbal support
  - changed sending of STATUSTEXT severity to use correct values
  - added new RSSI library with PWM input support
  - fixed MAVLink heading report for UAVCAN GPS
  - support LightWare I2C rangefinder on Linux
  - improved staging of parameters and formats on startup to dataflash
  - added new on-board compass calibrator
  - improved RCOutput code for NavIO port
  - added support for Septentrio GPS receiver
  - support DO_MOUNT_CONTROl via command-long interface
  - added CAM_RELAY_ON parameter
  - moved SKIP_GYRO_CAL functionality to INS_GYR_CAL
  - added detection of bad lidar settings for landing

Note that the documentation hasn't yet caught up with all the changes
in this release. We are still working on that, but meanwhile if you
see a feature that interests you and it isn't documented yet then
please ask.



Release 3.3.0, 20th May 2015
----------------------------

The ardupilot development team is proud to announce the release of
version 3.3.0 of APM:Plane. This is a major release with a lot of
changes. Please read the release notes carefully!

The last stable release was 3 months ago, and since that time we have
applied over 1200 changes to the code. It has been a period of very
rapid development for ArduPilot. Explaining all of the changes that
have been made would take far too long, so I've chosen some key
changes to explain in detail, and listed the most important secondary
changes in a short form. Please ask for details if there is a change
you see listed that you want some more information on.

Arming Changes
--------------

This is the first release of APM:Plane where ARMING_CHECK and
ARMING_REQUIRE both default to enabled. That means when you upgrade if
you didn't previously have arming enabled you will need to learn about
arming your plane.

Please see this page for more information on arming:

  http://plane.ardupilot.com/wiki/arming-your-plane/

I know many users will be tempted to disable the arming checks, but
please don't do that without careful thought. The arming checks are an
important part of ensuring the aircraft is ready to fly, and a common
cause of flight problems is to takeoff before ArduPilot is ready.

Re-do Accelerometer Calibration
-------------------------------

Due to a change in the maximum accelerometer range on the Pixhawk all
users must re-do their accelerometer calibration for this release. If
you don't then your plane will fail to arm with a message saying that
you have not calibrated the accelerometers.

Only 3D accel calibration
-------------------------

The old "1D" accelerometer calibration method has now been removed, so
you must use the 3D accelerometer calibration method. The old method
was removed because a significant number of users had poor flights due
to scaling and offset errors on their accelerometers when they used
the 1D method. My apologies for people with very large aircraft who
find the 3D method difficult.

Note that you can do the accelerometer calibration with the autopilot
outside the aircraft which can make things easier for large aircraft.

Auto-disarm
-----------

After an auto-landing the autopilot will now by default disarm after
LAND_DISARMDELAY seconds (with a default of 20 seconds). This feature
is to prevent the motor from spinning up unexpectedly on the ground
after a landing.

HIL_MODE parameter
------------------

It is now possible to configure your autopilot for hardware in the
loop simulation without loading a special firmware. Just set the
parameter HIL_MODE to 1 and this will enable HIL for any
autopilot. This is designed to make it easier for users to try HIL
without having to find a HIL firmware.

SITL on Windows
---------------

The SITL software in the loop simulation system has been completely
rewritten for this release. A major change is to make it possible to
run SITL on native windows without needing a Linux virtual
machine. There should be a release of MissionPlanner for Windows soon
which will make it easy to launch a SITL instance.

The SITL changes also include new backends, including the CRRCSim
flight simulator. This gives us a much wider range of aircraft we can
use for SITL. See http://dev.ardupilot.com/wiki/simulation-2/ for more
information.

Throttle control on takeoff
---------------------------

A number of users had problems with pitch control on auto-takeoff, and
with the aircraft exceeding its target speed during takeoff. The
auto-takeoff code has now been changed to use the normal TECS throttle
control which should solve this problem.

Rudder only support
-------------------

There is a new RUDDER_ONLY parameter for aircraft without ailerons,
where roll is controlled by the rudder. Please see the documentation
for more information on flying with a rudder only aircraft:

  http://plane.ardupilot.com/wiki/arduplane-parameters/#rudder_only_aircraft_arduplanerudder_only

APM1/APM2 Support
-----------------

We have managed to keep support for the APM1 and APM2 in this release,
but in order to fit it in the limited flash space we had to disable
some more features when building for those boards. For this release
the AP_Mount code for controlling camera mounts is disabled on
APM1/APM2.

At some point soon it will become impractical to keep supporting the
APM1/APM2 for planes. Please consider moving to a 32 bit autopilot
soon if you are still using an APM1 or APM2.

New INS code
------------

There have been a lot of changes to the gyro and accelerometer
handling for this release. The accelerometer range on the Pixhawk has
been changed to 16g from 8g to prevent clipping on high vibration
aircraft, and the sampling rate on the lsm303d has been increased to
1600Hz. 

An important bug has also been fixed which caused aliasing in the
sampling process from the accelerometers. That bug could cause 
attitude errors in high vibration environments.

Numerous Landing Changes
------------------------

Once again there have been a lot of improvements to the automatic
landing support. Perhaps most important is the introduction of a
smooth transition from landing approach to the flare, which reduces
the tendency to pitch up too much on flare. 

There is also a new parameter TECS_LAND_PMAX which controls the
maximum pitch during landing. This defaults to 10 degrees, but for
many aircraft a smaller value may be appropriate. Reduce it to 5
degrees if you find you still get too much pitch up during the flare.

Other secondary changes in this release include:

 - a new SerialManager library which gives much more flexible management of serial port assignment
 - changed the default FS_LONG_TIMEOUT to 5 seconds
 - raised default IMAX for roll/pitch to 3000
 - lowered default L1 navigation period to 20
 - new BRD_SBUS_OUT parameter to enable SBUS output on Pixhawk
 - large improvements to the internals of PX4Firmware/PX4NuttX for better performance
 - auto-formatting of microSD cards if they can't be mounted on boot (PX4/Pixhawk only)
 - a new PWM based driver for the PulsedLight Lidar to avoid issues with the I2C interface
 - fixed throttle forcing to zero when disarmed
 - only reset mission on disarm if not in AUTO mode
 - much better handling of steep landings
 - added smooth transition in landing flare
 - added HIL_MODE parameter for HIL without a special firmware
 - lowered default FS_LONG_TIMEOUT to 5 seconds
 - mark old ELEVON_MIXING mode as deprecated
 - fixed 50Hz MAVLink support
 - support DO_SET_HOME MAVLink command
 - fixed larger values of TKOFF_THR_DELAY
 - allow PulsedLight Lidar to be disabled at a given height
 - fixed bungee launch (long throttle delay)
 - fixed a bug handling entering AUTO mode before we have GPS lock
 - added CLI_ENABLED parameter
 - removed 1D accel calibration
 - added EKF_STATUS_REPORT MAVLink message
 - added INITIAL_MODE parameter
 - added TRIM_RC_AT_START parameter
 - added auto-disarm after landing (LAND_DISARMDELAY)
 - added LOCAL_POSITION_NED MAVLink message
 - avoid triggering a fence breach in final stage of landing
 - rebuild glide slope if we are above it and climbing
 - use TECS to control throttle on takeoff
 - added RUDDER_ONLY parameter to better support planes with no ailerons
 - updated Piksi RTK GPS driver
 - improved support for GPS data injection (for Piksi RTK GPS)
 - added NAV_LOITER_TO_ALT mission item
 - fixed landing approach without an airspeed sensor
 - support RTL_AUTOLAND=2 for landing without coming to home first
 - disabled camera mount support on APM1/APM2
 - added support for SToRM32 and Alexmos camera gimbals
 - added support for Jaimes mavlink enabled gimbal
 - improved EKF default tuning for planes
 - updated support for NavIO and NavIO+ boards
 - updated support for VRBrain boards
 - fixes for realtime threads on Linux
 - added simulated sensor lag for baro and mag in SITL
 - made it possible to build SITL for native Windows
 - switched to faster accel sampling on Pixhawk
 - added coning corrections on Pixhawk
 - set ARMING_CHECK to 1 by default
 - disable NMEA and SiRF GPS on APM1/APM2
 - support MPU9255 IMU on Linux
 - updates to BBBMINI port for Linux
 - added TECS_LAND_PMAX parameter
 - switched to synthetic clock in SITL
 - support CRRCSim FDM backend in SITL
 - new general purpose replay parsing code
 - switched to 16g accel range in Pixhawk
 - added FENCE_AUTOENABLE=2 for disabling just fence floor
 - added POS dataflash log message
 - changed GUIDED behaviour to match copter
 - added support for a 4th MAVLink channel
 - support setting AHRS_TRIM in preflight calibration
 - fixed a PX4 mixer out of range error

Best wishes to all APM:Plane users from the dev team, and happy
flying!


Release 3.2.2, February 10th 2015
---------------------------------

The ardupilot development team has released version 3.2.2 of
APM:Plane. This is a bugfix release for some important bugs found by
users of the 3.2.1 release.

The changes in this release are:

  - fixed a bug that could cause short term loss of RC control with
    some receiver systems and configurations

  - allowed for shorter sync pulse widths for PPM-SUM receivers on
    APM1 and APM2

  - fixed HIL mode altitude

The most important bug fix is the one for short term loss of RC
control. This is a very long standing bug which didn't have a
noticeable impact for most people, but could cause loss of RC control
for around 1 or 2 seconds for some people in certain circumstances.

The bug was in the the AP_HAL RCInput API. Each HAL backend has a flag
that says whether there is a new RC input frame available. That flag
was cleared by the read() method (typically hal.rcin->read()). Callers
would check for new input by checking the boolean
hal.rcin->new_input() function.

The problem was that read() was called from multiple places. Normally
this is fine as reads from other than the main radio input loop happen
before the other reads, but if the timing of the new radio frame
exactly matched the loop frequency then a read from another place
could clear the new_input flag and we would not see the new RC input
frame. If that happened enough times we would go into a short term RC
failsafe and ignore RC inputs, even in manual mode.

The fix was very simple - it is the new_input() function itself that
should clear the flag, not read(). 

Many thanks to MarkM for helping us track down this bug by providing
us with sufficient detail on how to reproduce it. In Marks case his
OpenLRSng configuration happened to produce exactly the worst case
timing needed to reproduce the issue. Once I copied his OpenLRS
settings to my TX/RX I was able to reproduce the problem and it was
easy to find and fix.

A number of users have reported occasional glitches in manual control
where servos pause for short periods in past releases. It is likely
that some of those issues were caused by this bug. The dev team would
like to apologise for taking so long to track down this bug!

The other main change was also related to RC input. Some receivers use
a PPM-SUM sync pulse width shorter than what the APM1/APM2 code was
setup to handle. The OpenLRSng default sync pulse width is 3000
microseconds, but the APM1/APM2 code was written for a mininum sync
pulse width of 4000 microseconds. For this release I have changed the
APM1/APM2 driver to accept a sync pulse width down to 2700
microseconds.


Release 3.2.1, February 5th 2015
--------------------------------

The ardupilot development team is proud to announce the release of
version 3.2.1 of APM:Plane. This is primarily a bugfix release, but
does have some new features.

The major changes in this release are:

  - fixed a mission handling bug that could cause a crash if jump
    commands form an infinite loop (thanks to Dellarb for reporting
    this bug)
  - improved support for in-kernel SPI handling on Linux (thanks to John Williams)
  - support UAVCAN based ESCs and GPS modules on Pixhawk (thanks to
    Pavel, Holger and and PX4 dev team)
  - Multiple updates for the NavIO+ cape on RaspberryPi (thanks to
    Emlid)
  - multiple automatic landing fixes, including improvements in flare
    detection, glide slope calculation and lag handling
  - fixed a bug that could cause a change altitude MAVLink command
    from causing a sudden descent
  - re-enable CLI on non-APM1/APM2 boards
  - Lots of EKF changes, including reducing impact of ground magnetic
    interference, reducing the impact of a GPS outage and integrating
    optical flow support
  - added initial support for the PX4 optical flow sensor. Just
    logging for this release.
  - added support for MAVLink packet routing
  - added detection and recovery from faulty gyro and accel sensors
  - improved arming checks code to detect a lot more error conditions,
    and change the ARMING_CHECK default value to check all error
    conditions.
  - added support for BBBMini Linux port
  - increased number of AVR input channels from 8 to 11
  - auto-set system clock based on GPS in Linux ports
  - added SBUS FrSky telemetry support (thanks to Mathias)

Release 3.2.0, November 25th 2014
---------------------------------

The ardupilot development team is proud to announce the release of
version 3.2.0 of APM:Plane. This is a major release with a lot of 
new features.

The changes span a lot of different areas of the code, but arguably
the most important changes are:

  - automatic stall prevention code
  - PX4IO based RC override code on FMU failure
  - I2C crash bugfix
  - new autoland code from Michael Day
  - compass independent auto takeoff

I'll go into each of these changes in a bit more detail.

Automatic Stall Prevention
--------------------------

The automatic stall prevention code is code that uses the aerodynamic
load factor (calculated from demanded bank angle) to adjust both the
maximum roll angle and the minimum airspeed. You can enable/disable
this code with the STALL_PREVENTION parameter which defaults to
enabled. 

When in stabilised manual throttle modes this option has the effect of
limiting how much bank angle you can demand when close to the
configured minimum airspeed (from AIRSPEED_MIN). That means when in
FBWA mode if you try to turn hard while close to AIRSPEED_MIN it will
limit the bank angle to an amount that will keep the speed above
AIRSPEED_MIN times the aerodynamic load factor. It will always allow
you at bank at least 25 degrees however, to ensure you keep some
maneuverability if the airspeed estimate is incorrect.

When in auto-throttle modes (such as AUTO, RTL, CRUISE etc) it will
additionally raise the minimum airspeed in proportion to the
aerodynamic load factor. That means if a mission demands a sharp turn
at low speed then initially the turn will be less sharp, and the TECS
controller will add power to bring the airspeed up to a level that can
handle the demanded turn. After the turn is complete the minimum
airspeed will drop back to the normal level.

This change won't completely eliminate stalls of course, but it should
make them less likely if you properly configure AIRSPEED_MIN for your
aircraft.

PX4IO based RC override code
----------------------------

This releases adds support for PX4IO based RC override. This is a
safety feature where the stm32 IO co-processor on the PX4 and Pixhawk
will give the pilot manual control if the main ArduPilot
micro-controller fails (or the autopilot code crashes). This is
particularly useful when testing new code that may not be stable.

As part of this new RC override support we also have a new
OVERRIDE_CHAN parameter, which allows you to specify a RC input
channel which can be used to test the RC override support. See the
documentation on OVERRIDE_CHAN for details.

I2C bugfix
----------

This release fixes another I2C bug in NuttX which could cause the
Pixhawk to lock up under high I2C load with noise on I2C cables. This
bug has caused at least two aircraft to crash, so it is an important
fix. I hope this will be the last I2C crash bug we find in NuttX! An
audit of the code was done to try to confirm that no more bugs of this
type are present.

New Autoland code
-----------------

This release incorporates some new autoland capabilities contributed
by Michael Day. The key new feature is the ability to trigger an
automatic landing when a RTL completes, which for the first time
allows a user to setup their aircraft to land using only transmitter
control. 

The way it works is there is a new parameter RTL_AUTOLAND. If that is
set to 1 and the aircraft reaches its target location in an RTL it
will look for DO_LAND_START mission item in the mission. If that is
found then the aircraft will switch to AUTO starting at that section
of the mission. The user sets up their land mission commands starting
with a DO_LAND_START mission item.

There is more to do in this autoland support. We have been discussing
more advanced go-around capabilities and also better path planning for
landing. The code in this release is an important first step though,
and will be a good basis for future work.

Compass independent takeoff code
--------------------------------

The auto-takeoff code has been changed to make it more independent of
compass settings, allowing for reliable takeoff down a runway with
poor compass offsets. The new takeoff code uses the gyroscope as the
primary heading control for the first part of the takeoff, until the
aircraft gains enough speed for a GPS heading to be reliable.

Many thanks to all the contributors, especially:

  - Paul and Jon for EKF and TECS updates
  - Bret and Grant for stall prevention testing
  - Michael for all his autoland work
  - all the work on NavIO, PXF and Zynq by John, Victor, George and Siddarth
  - The PX4 team for all the PX4 updates

More complete list of changes:

 - allow GCS to enable/disable PX4 safety switch
 - make auto-takeoff independent of compass errors
 - report gyro unhealthy if calibration failed
 - added support for MAV_CMD_DO_LAND_START
 - added RTL_AUTOLAND parameter
 - disable CLI by default in build
 - new InertialSensor implementation
 - added landing go around support
 - enable PX4 failsafe RC override
 - added OVERRIDE_CHAN parameter
 - changed default AUTOTUNE level to 6
 - changed default I value for roll/pitch controllers
 - added CAMERA_FEEDBACK mavlink messages
 - use airspeed temperature for baro calibration if possible
 - added STALL_PREVENTION parameter 
 - fixed handling of TKOFF_THR_MAX parameter
 - added ARSPD_SKIP_CAL parameter
 - fixed flaperon trim handling (WARNING: may need to retrim flaperons)
 - EKF robustness improvements, especially for MAG handling
 - lots of HAL_Linux updates
 - support wider range of I2C Lidars
 - fixed fallback to DCM in AHRS
 - fixed I2C crash bug in NuttX
 - TECS prevent throttle undershoot after a climb
 - AP_Mount: added lead filter to improve servo gimbals
 - Zynq and NavIO updates
 - fixed preflight calibration to prevent losing 3D accel cal
 - perform a gyro calibration when doing 3D accel cal
 - added DO_CONTINUE_AND_CHANGE_ALT mission command
 - added support for DO_FENCE_ENABLE mission command
 - allow gyro calibration to take up to 30 seconds
 - improved health checks in the EKF for DCM fallback

Note: If you use flaperons you may need to re-trim them before you
fly due to the change in flaperon trim handling.

I hope that everyone enjoys flying this new APM:Plane release as much
as we enjoyed producing it! It is a major milestone in the development
of the fixed wing code for APM, and I think puts us in a great
position for future development.

Happy flying!



Release 3.1.1, September 12th 2014
----------------------------------

The ardupilot development team is proud to announce the release of
version 3.1.1 of APM:Plane. This is primarily a bugfix release with a
small number of new features.

The main bug fixed in this release is a bug that could affect saving
parameters and mission items in the FRAM/eeprom storage of
PX4v1/Pixhawk/VRBrain. The bug has been in the code since January 2013
and did not cause problems very often (which is why it hasn't been
noticed till now), but when it does happen it causes changes to
parameters or mission items not to be saved on a reboot.

Other changes in this release:

  - support for using a Lidar for landing for terrain altitude (see
    the RNGFND_LANDING parameter)

  - improvements in the landing approach code, especially the glide
    slope calculation

  - added LAND_FLAP_PERCENT and TKOFF_FLAP_PCNT parameters, to control
    the amount of flaps to use on takeoff and landing

  - the default WP_RADIUS has been raised from 30 to 90. Note that the
    L1 controller may choose to turn after the WP_RADIUS is
    reached. The WP_RADIUS only controls the earliest point at which
    the turn can happen, so a larger WP_RADIUS usually leads to better
    flight paths, especially for faster aircraft.

  - send gyro and accel status separately to the GCS (thanks to Randy)

  - support setting the acceptance radius in mission waypoints (in
    parameter 2), which allows for better control of waypoints where
    actions such as servo release will happen

  - fixed GPS time offset in HIL

  - added RELAY_DEFAULT parameter, allowing control of relay state on
    boot

  - fixed sdcard logging on PX4v1

  - added GPS_SBAS_MODE and GPS_MIN_ELEV parameters for better control
    of the use of SBAS and the GPS elevation mask for advanced users

Happy flying!


Release 3.1.0, August 26th 2014
-------------------------------

The ardupilot development team is proud to announce the release of
version 3.1.0 of APM:Plane. This is a major release with a lot of new
features and bug fixes.

The biggest change in this release is the addition of automatic
terrain following. Terrain following allows the autopilot to guide the
aircraft over varying terrain at a constant height above the ground
using an on-board terrain database.

Changes in this release:

  - added terrain following support. See
    http://plane.ardupilot.com/wiki/common-terrain-following/

  - added support for higher baudrates on telemetry ports, to make it
    easier to use high rate telemetry to companion boards. Rates of up
    to 1.5MBit are now supported to companion boards.

  - added new takeoff code, including new parameters:
      TKOFF_TDRAG_ELEV, TKOFF_TDRAG_SPD1, TKOFF_ROTATE_SPD,
      TKOFF_THR_SLEW and TKOFF_THR_MAX. 
    This gives fine grained control of auto takeoff for tail dragger aircraft.

  - overhauled glide slope code to fix glide slope handling in many
    situations. This makes transitions between different altitudes
    much smoother.

  - prevent early waypoint completion for straight ahead
    waypoints. This makes for more accurate servo release at specific
    locations, for applications such as dropping water bottles.

  - added MAV_CMD_DO_INVERTED_FLIGHT command in missions, to change
    from normal to inverted flight in AUTO (thanks to Philip Rowse for
    testing of this feature).

  - new Rangefinder code with support for a wider range of rangefinder
    types including a range of Lidars (thanks to Allyson Kreft)

  - added support for FrSky telemetry via SERIAL2_PROTOCOL parameter
    (thanks to Matthias Badaire)

  - added new STAB_PITCH_DOWN parameter to improve low throttle
    behaviour in FBWA mode, making a stall less likely in FBWA mode
    (thanks to Jack Pittar for the idea).

  - added GLIDE_SLOPE_MIN parameter for better handling of small
    altitude deviations in AUTO. This makes for more accurate altitude
    tracking in AUTO.

  - added support for Linux based autopilots, initially with the PXF
    BeagleBoneBlack cape and the Erle robotics board. Support for more
    boards is expected in future releases. Thanks to Victor, Sid and
    Anuj for their great work on the Linux port. See
    http://diydrones.com/profiles/blogs/first-flight-of-ardupilot-on-linux
    for details.

  - prevent cross-tracking on some waypoint types, such as when
    initially entering AUTO or when the user commands a change of
    target waypoint.

  - fixed servo demo on startup (thanks to Klrill-ka)

  - added AFS (Advanced Failsafe) support on 32 bit boards by
    default. See
    http://plane.ardupilot.com/wiki/advanced-failsafe-configuration/

  - added support for monitoring voltage of a 2nd battery via BATTERY2
    MAVLink message

  - added airspeed sensor support in HIL

  - fixed HIL on APM2. HIL should now work again on all boards.

  - added StorageManager library, which expands available FRAM storage
    on Pixhawk to 16 kByte. This allows for 724 waypoints, 50 rally
    points and 84 fence points on Pixhawk.

  - improved steering on landing, so the plane is actively steered
    right through the landing.

  - improved reporting of magnetometer and barometer errors to the GCS

  - added FBWA_TDRAG_CHAN parameter, for easier FBWA takeoffs of tail
    draggers, and better testing of steering tuning for auto takeoff.

  - fixed failsafe pass through with no RC input (thanks to Klrill-ka)

  - fixed a bug in automatic flow control detection for serial ports
    in Pixhawk

  - fixed use of FMU servo pins as digital inputs on Pixhawk

  - imported latest updates for VRBrain boards (thanks to Emile
    Castelnuovo and Luca Micheletti)

  - updates to the Piksi GPS support (thanks to Niels Joubert)

  - improved gyro estimate in DCM (thanks to Jon Challinger)

  - improved position projection in DCM in wind (thanks to Przemek
    Lekston)

  - several updates to AP_NavEKF for more robust handling of errors
    (thanks to Paul Riseborough)

  - improved simulation of rangefinders in SITL

  - lots of small code cleanups thanks to Daniel Frenzel

  - initial support for NavIO board from Mikhail Avkhimenia

  - fixed logging of RCOU for up to 12 channels (thanks to Emile
    Castelnuovo)

  - code cleanups from Silvia Nunezrivero

  - improved parameter download speed on radio links with no flow
    control

Many thanks to everyone who contributed to this release, especially
our beta testers Marco, Paul, Philip and Iam.

Happy flying!


Release 3.0.3, May 19th 2014
----------------------------

The ardupilot development team is proud to announce the release of
version 3.0.3 of APM:Plane. This release contains some important bug
fixes for all supported boards.

The key bug fixes in this release are:

 - fixed handling of filter divergance in the EKF filter
 - fixed a glide slope calculation bug when entering AUTO mode

The EKF fixes are the main focus of this release. During testing of
APM:Plane with the AHRS_EKF_USE enabled it was found that under some
circumstances the EKF could diverge, resulting in loss of attitude
estimate. Unless the pilot quickly took control in MANUAL this could
result in the aircraft crashing.

The fix for this problem was in several parts. The main fix was to
prevent the divergance, but as a precuation against future bugs of
this type additional numerical checks were added to allow the EKF to
automatically reset in flight when the internal state shows
large gyro bias changes, which are the first sign of something going
wrong in the filter. If this happens again the EKF will automatically
disable itself for 10 seconds, allowing APM:Plane to fall back to the
old DCM code. The EKF will then reset itself using initial state based
on the DCM state. The aircraft will report the failure using the AHRS
health bit in the SYS_STATUS MAVLink message.

The default EKF tuning parameters were also updated based on a number
of user supplied flight logs to increase the robustness of the filter.

The second bug fixed in this release relates to the glide slope
calculation when the aircraft enters AUTO mode for the first time when
at an altitude above the altitude of the first waypoint in the
mission. The starting point for the glide slope was incorrectly
calculated using the home altitude, which resulted in the aircraft
descending below the first waypoint altitude before climbing again. In
some circumstances this could lead to a crash due to local terrain.

Many thanks to everyone who tested this release. Special thanks to
Dellarb for reporting the glide slope bug and to Paul Riseborough for
all his work on the EKF code over the last few weeks.

Happy flying!


Release 3.0.2, May 4th 2014
---------------------------

The ardupilot development team is proud to announce the release of
version 3.0.2 of APM:Plane. This release combines some important bug
fixes with some new features.

I2C bug fix
-----------

The most important change for this release is a bug fix for an I2C bug
in the NuttX I2C driver code that could (under some rare
circumstances) cause a Pixhawk to crash. This bug fix is the primary
reason for doing a new release now.

This bug was able to be reproduced by creating a 1.3m GPS cable
carrying both the I2C signals for a magnetometer and the UART signals
for the GPS. Interference between these two signals could cause the
I2C controller to give spurious data to the I2C driver. The I2C driver
did not have sufficient protection against these errors and could
crash the board.

While we have not been able to reproduce this error with the normal
cables that come with a Pixhawk we cannot rule out the bug triggering
with shorter cables, so we are doing a fast release to fix the bug.

Autotune
--------

This release also includes an important new feature - automatic
roll/pitch tuning. While this feature is still considered experimental
we have had very positive feedback from beta testers and have decided
to include it in the release.

Full documentation for how to use automatic tuning is available here:

  http://plane.ardupilot.com/wiki/automatic-tuning-with-autotune/

we hope that the automatic tuning will help users who have had
difficulty with the standard APM:Plane manual tuning procedure. We
plan on extending autotune to other aspects of fixed wing tuning in
future releases.

Other changes
-------------

 - fixed a glide slope calculation error when very close to waypoints
 - fixed a bug when swithing to another auto-throttle mode during auto
   takeoff (thanks to Marco for finding this bug!)
 - added MIS_AUTORESET parameter (thanks to Andrew Chapman)
 - support compassmot calibration by supplying current measurments to the
   compass driver (thanks to Jon Challinger)
 - fixed a GPS driver bug that could cause GPS lag in case of lost GPS
   samples (thanks to Jon Challinger)
 - fixed a LOITER_TURNS bug in missions for counter-clockwise loiter
   (thanks to Iskess for finding this bug)
 - added support for OBC termination requirements to PX4IO
 - added support for pressure altitude termination to OBC module
 - fixed EKF wind estimation with no airspeed sensor (thanks to Paul
   Riseborough)
 - improved tuning of EKF for fixed wing aircraft (thanks to Paul
   Riseborough)
 - Converted rally point code to library AP_Rally (thanks to Andrew
   Chapman)
 - added SITL checking for numerical errors

Thanks to testers!

Many thanks to everyone who tested the beta versions of this release!
Special thanks to Marco, Paul, Jon, Iam, JNJO, sonicdahousecat and
Keeyen for providing testing of both existing features and the new
autotune code.


Release 3.0.1, April 9th 2014
-----------------------------

I've just released APM:Plane 3.0.1, a bug fix release for the 3.0.0 release.
This release fixes two bugs:

    throttle failsafe for aircraft using PWM level for failsafe detection
    wind reporting with EKF enabled and no airspeed sensor

The throttle failsafe fix is a critical bugfix, which is why I am
doing a new release so soon. The bug was found by Sam Tabor, and he
posted the bug report before the 3.0.0 release, but I didn't notice it
in the release preparations.  The bug only affects systems using PWM
value as the sole method of detecting loss of RC control. I hadn't
noticed it myself as my planes all use receivers which stop sending
value PWM frames when the RC link is lost. In that case failsafe
worked correctly. Receivers that keep sending PWM frames but with low
throttle levels are common though, so this is a very important fix.
Many thanks to Sam for reporting the bug, and my apologies for not
noticing it in time for the 3.0.0 release.


Release 3.0.0, April 8th 2014
-----------------------------

The ardupilot development team is proud to announce the release of
version 3.0.0 of APM:Plane. This is a major release with a lot of new
features.

For each release I try to highlight the two or 3 key new features that
have gone in since the last release. That is a more difficult task
this time around because there are just so many new things. Still, I
think the most important ones are the new Extended Kalman Filter (EKF)
for attitude/position estimation, the extensive dual sensors support
and the new AP_Mission library.

We have also managed to still keep support for the APM1 and APM2,
although quite a few of the new features are not available on those
boards. We don't yet know for how long we'll be able to keep going on
supporting these old boards, so if you are thinking of getting a new
board then you should get a Pixhawk, and if you want the best
performance from the APM:Plane code then you should swap to a
Pixhawk now. It really is a big improvement.

New Extended Kalman Filter
--------------------------

The biggest change for the 3.0.0 release (and in fact the major reason
why we are calling it 3.0.0) is the new Extended Kalman Filter from
Paul Riseborough. Using an EKF for attitude and position estimation
was never an option on the APM2 as it didn't have the CPU power or
memory to handle it. The Pixhawk does have plenty of floating point
performance, and Paul has done a fantastic job of taking full
advantage of the faster board.

As this is the first stable release with the EKF code we have decided
to not enable it by default. It does however run all the time in
parallel with the existing DCM code, and both attitude/position
solutions are logged both to the on-board SD card and over
MAVLink. You can enable the EKF code using the parameter
AHRS_EKF_USE=1, which can be set and unset while flying, allowing you
to experiment with using the EKF either by examining your logs with
the EKF disabled to see how it would have done or by enabling it while
flying.

The main thing you will notice with the EKF enabled is more accurate
attitude estimation and better handling of sensor glitches. A Kalman
filter has an internal estimate of the reliability of each of its
sensor inputs, and is able to weight them accordingly. This means that
if your accelerometers start giving data that is inconsistent with
your other sensors then it can cope in a much more graceful way than
our old DCM code.

The result is more accurate flying, particularly in turns. It also
makes it possible to use higher tuning gains, as the increased
accuracy of the attitude estimation means that you can push the
airframe harder without it becoming unstable. You may find you can use
a smaller value for NAVL1_PERIOD, giving tighter turns, and higher
gains on your roll and pitch attitude controllers.

Paul has written up a more technical description of the new EKF code
here:

  http://plane.ardupilot.com/wiki/common-apm-navigation-extended-kalman-filter-overview/


Dual Sensors
------------

The second really big change for this release is support for
dual-sensors. We now take full advantage of the dual accelerometers
and dual gyros in the Pixhawk, and can use dual-GPS for GPS
failover. We already had dual compass support, so the only main
sensors we don't support two of now are the barometer and the airspeed
sensor. I fully expect we will support dual baro and dual airspeed in
a future release.

You might wonder why dual sensors is useful, so let me give you an
example. I fly a lot of nitro and petrol planes, and one of my planes
(a BigStik 60) had a strange problem where it would be flying
perfectly in AUTO mode, then when the throttle reached a very specific
level the pitch solution would go crazy (sometimes off by 90
degrees). I managed to recover in MANUAL each time, but it certainly
was exciting!

A careful analysis of the logs showed that the culprit was
accelerometer aliasing. At a very specific throttle level the Z
accelerometer got a DC offset of 11 m/s/s. So when the plane was
flying along nice and level the Z accelerometer would change from -10
m/s/s to +1 m/s/s. That resulted in massive errors in the attitude
solution.

This sort of error happens because of the way the accelerometer is
sampled. In the APM code the MPU6000 (used on both the APM2 and
Pixhawk) samples the acceleration at 1kHz. So if you have a strong
vibrational mode that is right on 1kHz then you are sampling the "top
of the sine wave", and get a DC offset.

The normal way to fix this issue is to improve the physical
anti-vibration mounting in the aircraft, but I don't like to fix
problems like this by making changes to my aircraft, as if I fix my
aircraft it does nothing for the thousands of other people running the
same code. As the lead APM developer I instead like to fix things in
software, so that everyone benefits.

The solution was to take advantage of the fact that the Pixhawk has
two accelerometers, one is a MPU6000, and the 2nd is a LSM303D. The
LSM303D is sampled at 800Hz, whereas the MPU6000 is sampled at
1kHz. It would be extremely unusual to have a vibration mode with
aliasing at both frequencies at once, which means that all we needed
to do was work out which accelerometer is accurate at any point in
time. For the DCM code that involved matching each accelerometer at
each time step to the combination of the GPS velocity vector and
current attitude, and for the EKF it was a matter of producing a
weighting for the two accelerometers based on the covariance matrix.

The result is that the plane flew perfectly with the new dual
accelerometer code, automatically switching between accelerometers as
aliasing occurred.

Since adding that code I have been on the lookout for signs of
aliasing in other logs that people send me, and it looks like it is
more common than we expected. It is rarely so dramatic as seen on my
BigStik, but often results in some pitch error in turns. I am hopeful
that with a Pixhawk and the 3.0 release of APM:Plane that these types
of problems will now be greatly reduced.

For the dual gyro support we went with a much simpler solution and
just average the two gyros when both are healthy. That reduces noise,
and works well, but doesn't produce the dramatic improvements that the
dual accelerometer code resulted in.

Dual GPS was also quite a large development effort. We now support
connecting a 2nd GPS to the serial4/5 port on the Pixhawk. This allows
you to protect against GPS glitches, and has also allowed us to get a
lot of logs showing that even with two identical GPS modules it is
quite common for one of the GPS modules to get a significant error
during a flight. The new code currently switches between the two GPS
modules based on the lock status and number of satellites, but we are
working on a more sophisticated switching mechanism.

Supporting dual GPS has also made it easier to test new GPS
modules. This has enabled us to do more direct comparisons between the
Lea6 and the Neo7 for example, and found the Neo7 performs very
well. It also helps with developing completely new GPS drivers, such
as the Piksi driver (see notes below).

New AP_Mission library
----------------------

Many months ago Brandon Jones re-worked our mission handling code to
be a library, making it much cleaner and fixing a number of long term
annoyances with the behaviour. For this release Randy built upon the
work that Brandon did and created the new AP_Mission library.

The main feature of this library from the point of view of the
developers is that it has a much cleaner interface, but it also has
some new user-visible features. The one that many users will be glad
to hear is that it no longer needs a "dummy waypoint" after a
jump. That was always an annoyance when creating complex missions.

The real advantage of AP_Mission will come in future releases though,
as it has the ability to look ahead in the mission to see what is
coming, allowing for more sophisticated navigation. The copter code
already takes advantage of this with the new spline waypoint feature,
and we expect to take similar advantage of this in APM:Plane in future
releases.

New Piksi GPS driver
--------------------

One of the most exciting things to happen in the world of GPS modules
in the last couple of years is the announcement by SwiftNav that they
would be producing a RTK capable GPS module called the Piksi at a
price that (while certainly expensive!) is within reach of more
dedicated hobbyists. It offers the possibility of decimeter and
possibly even centimetre level relative positioning, which has a lot
of potential for small aircraft, particularly for landing control and
more precise aerial mapping.

This release of APM:Plane has the first driver for the Piksi. The new
driver is written by Niels Joubert, and he has done a great job. It is
only a start though, as this is a single point positioning driver. It
will allow you to use your new Piksi if you were part of the
kickstarter, but it doesn't yet let you use it in RTK mode. Niels and
the SwiftNav team are working on a full RTK driver which we hope will
be in the next release.

Support for more RC channels
----------------------------

This release is the first to allow use of more than 8 RC input
channels. We now support up to 18 input channels on SBus on Pixhawk,
with up to 14 of them able to be assigned to functions using the
RCn_FUNCTION settings. For my own flying I now use a FrSky Taranis
with X8R and X6R receivers and they work very nicely. Many thanks to
the PX4 team, and especially to Holger and Lorenz for their great work
on improving the SBus code.

Flaperon Support
----------------

This release is the first to have integrated flaperon support, and
also includes much improved flaps support in general. You can now set
a FLAP_IN_CHANNEL parameter to give an RC channel for manual flap
control, and setup a FLAPERON_OUTPUT to allow you to setup your
ailerons for both manual and automatic flaperon control.

We don't yet have a full wiki page on setting up flaperons, but you
can read about the parameters here:

  http://plane.ardupilot.com/wiki/arduplane-parameters/#Flap_input_channel_ArduPlaneFLAP_IN_CHANNEL

Geofence improvements
---------------------

Michael Day has made an number of significant improvements to the
geo-fencing support for this release. It is now possible to
enable/disable the geofence via MAVLink, allowing ground stations to
control the fence. 

There are also three new fence control parameters. One is
FENCE_RET_RALLY which when enabled tells APM to fly back to the
closest rally point on a fence breach, instead of flying to the center
of the fence area. That can be very useful for more precise control of
fence breach handling.

The second new parameter is FENCE_AUTOENABLE, which allows you to
automatically enable a geofence on takeoff, and disable when doing an
automatic landing. That is very useful for fully automated missions.

The third new geofence parameter is FENCE_RETALT, which allows you to
specify a return altitude on fence breach. This can be used to
override the default (half way between min and max fence altitude).

Automatic Landing improvements
------------------------------

Michael has also been busy on the automatic landing code, with
improvements to the TECS speed/height control when landing and new
TECS_LAND_ARSPD and TECS_LAND_THR parameters to control airspeed and
throttle when landing. This is much simpler to setup than
DO_CHANGE_SPEED commands in a mission.

Michael is also working on automatic path planning for landing, based
on the rally points code. We hope that will get into a release soon.

Detailed Pixhawk Power Logging
------------------------------

One of the most common causes of issues with autopilots is power
handling, with poor power supplies leading to brownouts or sensor
malfunction. For this release we have enabled detailed logging of the
information available from the on-board power management system of the
Pixhawk, allowing us to log the status of 3 different power sources
(brick input, servo rail and USB) and log the voltage level of the
servo rail separately from the 5v peripheral rail on the FMU.

This new logging should make it much easier for us to diagnose power
issues that users may run into.

New SERIAL_CONTROL protocol
---------------------------

This release adds a new SERIAL_CONTROL MAVLink message which makes it
possible to remotely control a serial port on a Pixhawk from a ground
station. This makes it possible to do things like upgrade the firmware
on a 3DR radio without removing it from an aircraft, and will also
make it possible to attach to and control a GPS without removing it
from the plane.

There is still work to be done in the ground station code to take full
advantage of this new feature and we hope to provide documentation
soon on how to use u-Blox uCenter to talk to and configure a GPS in an
aircraft and to offer an easy 3DR radio upgrade button via the Pixhawk
USB port.

Lots of other changes!
----------------------

There have been a lot of other improvements in the code, but to stop
this turning into a book instead of a set of release notes I'll stop
the detailed description there. Instead here is a list of the more
important changes not mentioned above:

  - added LOG_WHEN_DISARMED flag in LOG_BITMASK
  - raised default LIM_PITCH_MAX to 20 degrees
  - support a separate steering channel from the rudder channel
  - faster mission upload on USB
  - new mavlink API for reduced memory usage
  - fixes for the APM_OBC Outback Challenge module
  - fixed accelerometer launch detection with no airspeed sensor
  - greatly improved UART flow control on Pixhawk
  - added BRD_SAFETYENABLE option to auto-enable the safety
    switch on PX4 and Pixhawk on boot
  - fixed pitot tube ordering bug and added ARSPD_TUBE_ORDER parameter
  - fixed log corruption bug on PX4 and Pixhawk
  - fixed repeated log download bug on PX4 and Pixhawk
  - new Replay tool for detailed log replay and analysis
  - flymaple updates from Mike McCauley
  - fixed zero logs display in MAVLink log download
  - fixed norm_input for cruise mode attitude control
  - added RADIO_STATUS logging in aircraft logs
  - added UBX monitor messages for detailed hardware logging of u-Blox status
  - added MS4525 I2C airspeed sensor voltage compensation


I hope that everyone enjoys flying this new APM:Plane release as much
as we enjoyed producing it! It is a major milestone in the development
of the fixed wing code for APM, and I think puts us in a great
position for future development.


Happy flying!
