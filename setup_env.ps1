# Sanal ortam klasör adini belirle
$VENV_DIR = "venv"

<#
# Python yüklü mü kontrol et
try {
    python --version > $null 2>&1
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Python yüklü degil veya PATH'e ekli degil."
        Pause
        exit 1
    }
} catch {
    Write-Host "Python yüklü degil veya PATH'e ekli degil."
    Pause
    exit 1
}
#>



# Python 3.9'u kontrol et, py launcher ile
if ($python39Path -eq "") {
    try {
        $python39VersionOutput = py -3.9 --version 2>&1
        if ($LASTEXITCODE -eq 0) {
            $python39Path = "py -3.9"
            Write-Host "Python 3.9 bulundu (py launcher ile): $python39VersionOutput"
        }
    }
    catch {
        # py -3.9 da bulunamadı
    }
}

# Eğer Python 3.9 bulunamadıysa hata ver
if ($python39Path -eq "") {
    Write-Host "HATA: Python 3.9 bulunamadi!"
    Write-Host "Lütfen Python 3.9 sürümün<PERSON> yükleyin:"
    Write-Host "https://www.python.org/downloads/release/python-3918/"
    Write-Host ""
    Write-Host "Veya şu komutlardan birini deneyin:"
    Write-Host "- python3.9 --version"
    Write-Host "- py -3.9 --version"
    Pause
    exit 1
}


# Sanal ortam olustur (Python 3.9 ile)
if (-not (Test-Path $VENV_DIR)) {
    Write-Host "Python 3.9 ile sanal ortam olusturuluyor..."
    if ($python39Path -eq "py -3.9") {
        py -3.9 -m venv $VENV_DIR
    } else {
        & $python39Path -m venv $VENV_DIR
    }
} else {
    Write-Host "Sanal ortam zaten var: $VENV_DIR"
}

# Ortami aktive et
$activateScript = Join-Path $VENV_DIR "Scripts\Activate.ps1"
if (Test-Path $activateScript) {
    & $activateScript
    Write-Host "Sanal ortam aktif edildi."
} else {
    Write-Host "Aktivasyon dosyasi bulunamadi: $activateScript"
    Pause
    exit 1
}

# Ana bagimliliklari yükle
if (Test-Path "requirements.txt") {
    Write-Host "requirements.txt yükleniyor..."
    pip install -r requirements.txt
} else {
    Write-Host "requirements.txt bulunamadi."
}

# Gelistirme bagimliliklari istege bagli
if (Test-Path "requirements-dev.txt") {
    $DEVINSTALL = Read-Host "Gelistirme bagimliliklari yüklensin mi? (E/H)"
    if ($DEVINSTALL -ieq "E") {
        pip install -r requirements-dev.txt
    } else {
        Write-Host "Gelistirme bagimliliklari atlandi."
    }
} else {
    Write-Host "requirements-dev.txt bulunamadi."
}

Write-Host ""
Write-Host "Ortam hazir! Sanal ortam aktif."
Write-Host ""
Write-Host "Komut satirinda isiniz bitince 'deactivate' yazabilirsiniz."
Pause