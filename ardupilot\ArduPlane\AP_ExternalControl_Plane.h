
/*
  external control library for plane
 */
#pragma once

#include <AP_ExternalControl/AP_ExternalControl.h>

#if AP_EXTERNAL_CONTROL_ENABLED

#include <AP_Common/Location.h>

class AP_ExternalControl_Plane : public AP_ExternalControl {
public:
    /*
        Sets the target global position for a loiter point.
    */
    bool set_global_position(const Location& loc) override WARN_IF_UNUSED;

    /*
        Sets the target airspeed.
    */
    bool set_airspeed(const float airspeed) override WARN_IF_UNUSED;

};

#endif // AP_EXTERNAL_CONTROL_ENABLED
