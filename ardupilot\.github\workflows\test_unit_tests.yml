name: test unit tests and sitl building

on:
  push:
    paths-ignore:
      # Remove markdown files as irrelevant
      - '**.md'
      # Remove dotfile at root directory
      - './.dir-locals.el'
      - './.dockerignore'
      - './.editorconfig'
      - './.gitattributes'
      - './.github'
      - './.gitignore'
      - './.valgrind-suppressions'
      - './.valgrindrc'
      - 'Dockerfile'
      - 'Vagrantfile'
      - 'Makefile'
      # Remove some directories check
      - '.vscode/**'
      - '.github/ISSUE_TEMPLATE/**'
      # Remove generic tools
      - 'Tools/CHDK-Script/**'
      - 'Tools/CodeStyle/**'
      - 'Tools/completion/**'
      - 'Tools/CPUInfo/**'
      - 'Tools/debug/**'
      - 'Tools/environment_install/**'
      - 'Tools/FilterTestTool/**'
      - 'Tools/geotag/**'
      - 'Tools/GIT_Test/**'
      - 'Tools/gittools/**'
      - 'Tools/Hello/**'
      - 'Tools/Linux_HAL_Essentials/**'
      - 'Tools/mavproxy_modules/**'
      - 'Tools/Pozyx/**'
      - 'Tools/PrintVersion.py'
      - 'Tools/simulink/**'
      - 'Tools/UDP_Proxy/**'
      - 'Tools/vagrant/**'
      - 'Tools/Vicon/**'
      # remove non SITL HAL
      - 'libraries/AP_HAL_ChibiOS/**'
      - 'libraries/AP_HAL_ESP32/**'
      - 'libraries/AP_HAL_QURT/**'
      # Remove change on other workflows
      - '.github/workflows/test_environment.yml'
  pull_request:
    paths-ignore:
      # Remove markdown files as irrelevant
      - '**.md'
      # Remove dotfile at root directory
      - './.dir-locals.el'
      - './.dockerignore'
      - './.editorconfig'
      - './.gitattributes'
      - './.github'
      - './.gitignore'
      - './.valgrind-suppressions'
      - './.valgrindrc'
      - 'Dockerfile'
      - 'Vagrantfile'
      - 'Makefile'
      # Remove some directories check
      - '.vscode/**'
      - '.github/ISSUE_TEMPLATE/**'
      # Remove generic tools
      - 'Tools/CHDK-Script/**'
      - 'Tools/CPUInfo/**'
      - 'Tools/CodeStyle/**'
      - 'Tools/FilterTestTool/**'
      - 'Tools/GIT_Test/**'
      - 'Tools/Hello/**'
      - 'Tools/Linux_HAL_Essentials/**'
      - 'Tools/LogAnalyzer/**'
      - 'Tools/Pozyx/**'
      - 'Tools/PrintVersion.py'
      - 'Tools/completion/**'
      - 'Tools/debug/**'
      - 'Tools/environment_install/**'
      - 'Tools/geotag/**'
      - 'Tools/gittools/**'
      - 'Tools/mavproxy_modules/**'
      - 'Tools/simulink/**'
      - 'Tools/vagrant/**'
      - 'Tools/UDP_Proxy/**'
      - 'Tools/Vicon/**'
      # remove non SITL HAL
      - 'libraries/AP_HAL_ChibiOS/**'
      - 'libraries/AP_HAL_ESP32/**'
      - 'libraries/AP_HAL_QURT/**'
      # Remove change on other workflows
      - '.github/workflows/test_environment.yml'
  workflow_dispatch:

concurrency:
  group: ci-${{github.workflow}}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  build:
    runs-on: ubuntu-22.04
    container:
      image: ardupilot/ardupilot-dev-${{ matrix.toolchain }}:v0.1.3
      options: --user 1001
    strategy:
      fail-fast: false  # don't cancel if a job from the matrix fails
      matrix:
        toolchain: [
            base,  # GCC
            clang,
        ]
        config: [
            unit-tests,
            examples,
            sitl
        ]
    steps:
      # git checkout the PR
      - uses: actions/checkout@v4
        with:
          submodules: 'recursive'
      # Put ccache into github cache for faster build
      - name: Prepare ccache timestamp
        id: ccache_cache_timestamp
        run: |
          NOW=$(date -u +"%F-%T")
          echo "timestamp=${NOW}" >> $GITHUB_OUTPUT
      - name: ccache cache files
        uses: actions/cache@v4
        with:
          path: ~/.ccache
          key: ${{github.workflow}}-ccache-${{ matrix.toolchain }}-${{steps.ccache_cache_timestamp.outputs.timestamp}}
          restore-keys: ${{github.workflow}}-ccache-${{ matrix.toolchain }}-  # restore ccache from either previous build on this branch or on master
      - name: setup ccache
        run: |
          . .github/workflows/ccache.env
      - name: test ${{matrix.config}} ${{ matrix.toolchain }}
        env:
          CI_BUILD_TARGET: ${{matrix.config}}
        shell: 'script -q -e -c "bash {0}"'
        run: |
          git config --global --add safe.directory ${GITHUB_WORKSPACE}
          if [[ ${{ matrix.toolchain }} == "clang" ]]; then
            export CC=clang
            export CXX=clang++
          fi
          PATH="/github/home/<USER>/bin:$PATH"
          Tools/scripts/build_ci.sh

      - name: Unittest extract_param_defaults
        env:
          PYTHONPATH: "${{ github.workspace }}/Tools/scripts:${{ github.workspace }}/modules/mavlink/pymavlink"
        if: matrix.config == 'unit-tests'
        run: |
          Tools/autotest/unittest/extract_param_defaults_unittest.py

      - name: Unittest annotate_params
        env:
          PYTHONPATH: "${{ github.workspace }}/Tools/scripts"
        if: matrix.config == 'unit-tests'
        run: |
          python3 -m pip install --upgrade pip
          python3 -m pip install requests mock
          Tools/autotest/unittest/annotate_params_unittest.py

      - name: Archive buildlog artifacts
        uses: actions/upload-artifact@v4
        if: failure()
        with:
           name: fail-${{ matrix.toolchain }}-${{matrix.config}}
           path: /tmp/buildlogs
           retention-days: 14
