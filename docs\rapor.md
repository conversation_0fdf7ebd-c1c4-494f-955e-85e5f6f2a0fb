# Teknofest İHA Projesi - Teknik Rapor

## 1. Proje Genel Bakış

Bu proje, Teknofest yarışması için geliştirilmiş bir İnsansız Hava Aracı (İHA) sistemidir. Proje, simülasyon tabanlı geliştirme yaklaşımı benimser ve gerçek dünya uygulamaları için hazırlanmış kapsamlı bir yazılım mimarisi sunar.

### 1.1 Proje <PERSON>
```
IHA/
├── AirSim/                # Microsoft AirSim simülasyon platformu
├── ardupilot/             # ArduPilot açık kaynak otopilot yazılımı
├── docs/                  # Proje belgeleri ve kurulum rehberleri
├── logs/                  # Uçuş ve simülasyon günlükleri
├── missions/              # Görev planları ve waypoint dosyaları
├── models/                # Simülasyon modelleri
├── scripts/               # Python kontrol betikleri
├── requirements.txt       # Ana bağımlılıklar
├── requirements-dev.txt   # Geliştirici bağımlılıkları
└── setup_env.ps1          # Ortam kurulum scripti
```

## 2. Sistem Mimarisi

### 2.1 Genel Mimari
Sistem, katmanlı bir mimari yaklaşımı benimser:

1. **Simülasyon Katmanı**: AirSim (Unreal Engine tabanlı)
2. **Otopilot Katmanı**: ArduPilot/ArduCopter
3. **Kontrol Katmanı**: Python tabanlı kontrol scriptleri
4. **İletişim Katmanı**: MAVLink protokolü
5. **Yer İstasyonu Katmanı**: Mission Planner/QGroundControl

### 2.2 Bileşenler Arası İletişim
- **AirSim ↔ ArduPilot**: UDP soketleri üzerinden SITL (Software In The Loop)
- **ArduPilot ↔ Kontrol Scriptleri**: DroneKit ve PyMAVLink kütüphaneleri
- **Yer İstasyonu ↔ ArduPilot**: MAVLink protokolü (UDP/Serial)

## 3. Görüntü İşleme Algoritmaları

### 3.1 AirSim Görüntü API'si
Sistem, AirSim'in güçlü görüntü işleme yeteneklerini kullanır:

```python
# Görüntü alma örneği (scripts/airsimwithopencv.py)
responses = client.simGetImages([
    airsim.ImageRequest("0", airsim.ImageType.Scene, False, False)
])
img1d = np.frombuffer(responses[0].image_data_uint8, dtype=np.uint8)
img_rgb = img1d.reshape(responses[0].height, responses[0].width, 3)
```

### 3.2 Desteklenen Görüntü Türleri
- **Scene**: Normal RGB kamera görüntüsü
- **DepthPlanar**: Derinlik haritası
- **DepthPerspective**: Perspektif derinlik
- **DepthVis**: Görselleştirilmiş derinlik
- **DisparityNormalized**: Normalleştirilmiş disparite
- **Segmentation**: Nesne segmentasyonu
- **SurfaceNormals**: Yüzey normalleri
- **Infrared**: Kızılötesi görüntü

### 3.3 OpenCV Entegrasyonu
Proje, OpenCV kütüphanesi ile görüntü işleme algoritmaları geliştirir:
- Gerçek zamanlı görüntü işleme
- Nesne tanıma ve takip
- Görsel navigasyon
- Hedef tespit algoritmaları

## 4. İHA Kontrol Yazılımı

### 4.1 ArduCopter Uçuş Kontrol Sistemi
ArduCopter, projenin ana uçuş kontrol yazılımıdır ve şu özellikleri sunar:

#### 4.1.1 Uçuş Modları
- **GUIDED**: Programatik kontrol modu
- **AUTO**: Otonom görev yürütme
- **LOITER**: Sabit pozisyonda bekletme
- **RTL**: Güvenli dönüş
- **LAND**: Güvenli iniş

#### 4.1.2 Pozisyon Kontrol Sistemi
```cpp
// ArduCopter/mode_guided.cpp'den örnek
pos_control->input_pos_NEU_cm(guided_pos_target_cm, terr_offset, pos_offset_z_buffer);
pos_control->update_NE_controller();
pos_control->update_U_controller();
attitude_control->input_thrust_vector_heading_cd(pos_control->get_thrust_vector(), auto_yaw.get_heading());
```

#### 4.1.3 Sensör Füzyon
- **EKF (Extended Kalman Filter)**: Durum tahmini
- **IMU**: İvme ve açısal hız sensörleri
- **GPS**: Konum bilgisi
- **Barometrik Sensör**: Yükseklik ölçümü
- **Manyetometre**: Yön bilgisi

### 4.2 Python Kontrol Scriptleri

#### 4.2.1 DroneKit Entegrasyonu
```python
# scripts/takeoff_test.py'den örnek
from dronekit import connect, VehicleMode
vehicle = connect('udp:127.0.0.1:14550', wait_ready=True)
vehicle.mode = VehicleMode("GUIDED")
vehicle.armed = True
vehicle.simple_takeoff(10)
```

#### 4.2.2 Temel Kontrol Fonksiyonları
- Kalkış ve iniş kontrolü
- Waypoint navigasyonu
- Hız ve yükseklik kontrolü
- Güvenlik kontrolleri

## 5. Yer İstasyonu Yazılımı

### 5.1 MAVLink Protokolü
Sistem, MAVLink protokolü üzerinden yer istasyonu ile iletişim kurar:
- **Telemetri**: Gerçek zamanlı durum bilgisi
- **Komut Gönderimi**: Uzaktan kontrol komutları
- **Görev Planlaması**: Waypoint yükleme/indirme
- **Parametre Yönetimi**: Sistem parametrelerinin ayarlanması

### 5.2 Desteklenen Yer İstasyonu Yazılımları
- **Mission Planner**: Windows tabanlı tam özellikli yer istasyonu
- **QGroundControl**: Çapraz platform yer istasyonu
- **MAVProxy**: Komut satırı tabanlı proxy

### 5.3 Görev Planlaması
Görev dosyaları QGC WPL formatında saklanır:
```
QGC WPL 110
0	1	0	16	0	0	0	0	-35.3632621	149.1652374	584.090000	1
1	0	3	16	0.00000000	0.00000000	0.00000000	0.00000000	-35.36217610	149.16403770	100.000000	1
```

## 6. Sensör Entegrasyonu ve İşleyişi

### 6.1 SITL (Software In The Loop) Simülasyonu
Sistem, SITL simülasyonu kullanarak gerçek sensör verilerini taklit eder:

#### 6.1.1 AirSim Sensör Simülasyonu
```cpp
// ardupilot/libraries/SITL/SIM_AirSim.cpp'den
void AirSim::recv_fdm(const sitl_input& input) {
    // Sensör verilerini al ve işle
    update_position(state.position);
    update_velocity(velocity_ef);
    update_attitude(state.attitude);
    update_mag_field_bf();
}
```

#### 6.1.2 Simüle Edilen Sensörler
- **IMU**: 3-eksen ivmeölçer ve jiroskop
- **GPS**: Konum, hız ve yön bilgisi
- **Barometrik Sensör**: Basınç ve yükseklik
- **Manyetometre**: Manyetik alan ölçümü
- **Kamera**: RGB, derinlik ve segmentasyon
- **LiDAR**: Mesafe ölçümü (opsiyonel)

### 6.2 Sensör Füzyon Algoritmaları
ArduPilot'un gelişmiş EKF sistemi:
- **AP_NavEKF2**: İkinci nesil genişletilmiş Kalman filtresi
- **AP_NavEKF3**: Üçüncü nesil EKF (daha gelişmiş)
- **AHRS**: Tutum ve yön referans sistemi

### 6.3 Güvenlik Sistemleri
- **Failsafe**: Bağlantı kaybı durumunda güvenli mod
- **Geofence**: Coğrafi sınırlar
- **Battery Failsafe**: Düşük batarya koruması
- **GPS Failsafe**: GPS kaybı koruması

## 7. Geliştirme Ortamı ve Araçlar

### 7.1 Bağımlılıklar
#### Ana Bağımlılıklar (requirements.txt):
- **dronekit**: Python DroneKit API
- **pymavlink**: MAVLink protokol kütüphanesi
- **numpy**: Sayısal hesaplamalar
- **opencv**: Görüntü işleme
- **mavproxy**: MAVLink proxy

#### Geliştirici Bağımlılıkları (requirements-dev.txt):
- **pytest**: Test framework
- **opencv-python**: OpenCV Python bağlamaları
- **pygame**: Joystick/gamepad desteği
- **msgpack-rpc-python**: AirSim RPC iletişimi

### 7.2 Kurulum ve Çalıştırma
1. **Ortam Hazırlığı**: `setup_env.ps1` scripti ile Python sanal ortamı
2. **ArduPilot Derleme**: WAF build sistemi ile SITL derleme
3. **AirSim Kurulumu**: Visual Studio ile C++ derleme
4. **Simülasyon Başlatma**: ArduCopter SITL + AirSim Unreal ortamı

### 7.3 Test ve Doğrulama
- **SITL Testleri**: Yazılım döngüsü testleri
- **Birim Testleri**: Bileşen bazlı testler
- **Entegrasyon Testleri**: Sistem bütünlük testleri
- **Uçuş Testleri**: Simülasyon ortamında görev testleri

## 8. Sonuç ve Öneriler

Bu İHA projesi, modern yazılım geliştirme yaklaşımlarını benimseyen, ölçeklenebilir ve güvenilir bir sistem mimarisi sunar. Simülasyon tabanlı geliştirme yaklaşımı, gerçek dünya testleri öncesinde kapsamlı doğrulama imkanı sağlar.

### 8.1 Güçlü Yönler
- Modüler ve ölçeklenebilir mimari
- Endüstri standardı protokoller (MAVLink)
- Kapsamlı simülasyon ortamı
- Açık kaynak ekosistem

### 8.2 Geliştirme Önerileri
- Makine öğrenmesi algoritmalarının entegrasyonu
- Gerçek zamanlı görüntü işleme optimizasyonu
- Çoklu İHA koordinasyonu
- Gelişmiş güvenlik protokolleri
