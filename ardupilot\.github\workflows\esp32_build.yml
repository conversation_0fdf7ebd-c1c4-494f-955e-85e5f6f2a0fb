name: ESP32 Build

on:
  push:
    paths-ignore:
      # remove non copter and plane vehicles
      - 'AntennaTracker/**'
      - 'ArduSub/**'
      - 'Blimp/**'
      - 'Rover/**'
      # remove non esp32 HAL
      - 'libraries/AP_HAL_ChibiOS/**'
      - 'libraries/AP_HAL_SITL/**'
      # remove non SITL directories
      - 'Tools/AP_Bootloader/**'
      - 'Tools/AP_Periph/**'
      - 'Tools/bootloaders/**'
      - 'Tools/CHDK-Script/**'
      - 'Tools/CodeStyle/**'
      - 'Tools/completion/**'
      - 'Tools/CPUInfo/**'
      - 'Tools/debug/**'
      - 'Tools/environment_install/**'
      - 'Tools/FilterTestTool/**'
      - 'Tools/Frame_params/**'
      - 'Tools/geotag/**'
      - 'Tools/GIT_Test/**'
      - 'Tools/gittools/**'
      - 'Tools/Hello/**'
      - 'Tools/IO_Firmware/**'
      - 'Tools/Linux_HAL_Essentials/**'
      - 'Tools/mavproxy_modules/**'
      - 'Tools/Pozyx/**'
      - 'Tools/PrintVersion.py'
      - 'Tools/Replay/**'
      - 'Tools/simulink/**'
      - 'Tools/UDP_Proxy/**'
      - 'Tools/vagrant/**'
      - 'Tools/Vicon/**'
      # Discard python file from Tools/scripts as not used
      - 'Tools/scripts/**.py'
      - 'Tools/scripts/build_sizes/**'
      - 'Tools/scripts/build_tests/**'
      - 'Tools/scripts/CAN/**'
      - 'Tools/scripts/signing/**'
      # Remove autotest
      - 'Tools/autotest/**'
      # Remove markdown files as irrelevant
      - '**.md'
      # Remove dotfile at root directory
      - './.dir-locals.el'
      - './.dockerignore'
      - './.editorconfig'
      - './.flake8'
      - './.gitattributes'
      - './.github'
      - './.gitignore'
      - './.pre-commit-config.yaml'
      - './.pydevproject'
      - './.valgrind-suppressions'
      - './.valgrindrc'
      - 'Dockerfile'
      - 'Vagrantfile'
      - 'Makefile'
      # Remove some directories check
      - '.vscode/**'
      - '.github/ISSUE_TEMPLATE/**'
      # Remove change on other workflows
      - '.github/workflows/test_environment.yml'

  pull_request:
    paths-ignore:
      # remove non copter and plane vehicles
      - 'AntennaTracker/**'
      - 'ArduSub/**'
      - 'Blimp/**'
      - 'Rover/**'
      # remove non esp32 HAL
      - 'libraries/AP_HAL_ChibiOS/**'
      - 'libraries/AP_HAL_SITL/**'
      # remove non SITL directories
      - 'Tools/AP_Bootloader/**'
      - 'Tools/AP_Periph/**'
      - 'Tools/bootloaders/**'
      - 'Tools/CHDK-Script/**'
      - 'Tools/CodeStyle/**'
      - 'Tools/completion/**'
      - 'Tools/CPUInfo/**'
      - 'Tools/debug/**'
      - 'Tools/environment_install/**'
      - 'Tools/FilterTestTool/**'
      - 'Tools/Frame_params/**'
      - 'Tools/geotag/**'
      - 'Tools/GIT_Test/**'
      - 'Tools/gittools/**'
      - 'Tools/Hello/**'
      - 'Tools/IO_Firmware/**'
      - 'Tools/Linux_HAL_Essentials/**'
      - 'Tools/LogAnalyzer/**'
      - 'Tools/mavproxy_modules/**'
      - 'Tools/Pozyx/**'
      - 'Tools/PrintVersion.py'
      - 'Tools/Replay/**'
      - 'Tools/simulink/**'
      - 'Tools/UDP_Proxy/**'
      - 'Tools/vagrant/**'
      - 'Tools/Vicon/**'
      # Discard python file from Tools/scripts as not used
      - 'Tools/scripts/**.py'
      - 'Tools/scripts/build_sizes/**'
      - 'Tools/scripts/build_tests/**'
      - 'Tools/scripts/CAN/**'
      - 'Tools/scripts/signing/**'
      # Remove autotest
      - 'Tools/autotest/**'
      # Remove markdown files as irrelevant
      - '**.md'
      # Remove dotfile at root directory
      - './.dir-locals.el'
      - './.dockerignore'
      - './.editorconfig'
      - './.flake8'
      - './.gitattributes'
      - './.github'
      - './.gitignore'
      - './.pre-commit-config.yaml'
      - './.pydevproject'
      - './.valgrind-suppressions'
      - './.valgrindrc'
      - 'Dockerfile'
      - 'Vagrantfile'
      - 'Makefile'
      # Remove some directories check
      - '.vscode/**'
      - '.github/ISSUE_TEMPLATE/**'
      # Remove change on other workflows
      - '.github/workflows/test_environment.yml'

  workflow_dispatch:

concurrency:
  group: ci-${{github.workflow}}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  build:
    runs-on: ubuntu-22.04
    strategy:
      fail-fast: false  # don't cancel if a job from the matrix fails
      matrix:
        config: [
            esp32buzz,
            esp32s3empty,
        ]
        gcc: [10]

    steps:
      - uses: actions/checkout@v4
        with:
          submodules: 'recursive'

      - name: Register gcc problem matcher
        run: echo "::add-matcher::.github/problem-matchers/gcc.json"

      - name: Register python problem matcher
        run: echo "::add-matcher::.github/problem-matchers/python.json"

      - name: Register autotest warn matcher
        run: echo "::add-matcher::.github/problem-matchers/autotestwarn.json"

      - name: Register autotest fail matcher
        run: echo "::add-matcher::.github/problem-matchers/autotestfail.json"

      - name: Install Prerequisites
        shell: bash
        run: |
          sudo apt-get update
          sudo apt-get install git wget libncurses-dev flex bison gperf python3 python3-pip python3-venv python3-setuptools python3-serial python3-gevent python3-cryptography python3-future python3-pyparsing python3-pyelftools cmake ninja-build ccache libffi-dev libssl-dev dfu-util libusb-1.0-0 cmake
          python3 --version
          python3 -m pip install gevent

          # we actualy want 3.11 .. but the above only gave us 3.10, not ok with esp32 builds.
          sudo add-apt-repository ppa:deadsnakes/ppa
          sudo apt-get update
          sudo apt-get install python3.11 python3.11-venv python3.11-distutils  -y
          sudo apt-get install python3 python3-pip python3-venv python3-setuptools python3-serial python3-cryptography python3-future python3-pyparsing python3-pyelftools
          python3 -m pip install gevent
          python3 --version
          python3.11 --version
          which python3.11

          git submodule update --init --recursive --depth=1
          ./Tools/scripts/esp32_get_idf.sh

          sudo ln -s /usr/bin/ninja /usr/bin/ninja-build

          cd modules/esp_idf
          echo "Installing ESP-IDF tools...."
          ./install.sh esp32,esp32s3 
          cd ../..
          
          
      - name: build ${{matrix.config}}
        env:
          CI_BUILD_TARGET: ${{matrix.config}}
        shell: bash
        run: |
          source ~/.bash_profile
          PATH="/github/home/<USER>/bin:$PATH"
          echo $PATH
          echo "### Configure ${{matrix.config}} :rocket:" >> $GITHUB_STEP_SUMMARY
          cd modules/esp_idf
          ./install.sh
          source ./export.sh
          cd ../..
          python3 -m pip install --progress-bar off future lxml pymavlink MAVProxy pexpect flake8 geocoder empy==3.3.4 dronecan
          which cmake
          ./waf configure --board ${{matrix.config}} 
          echo './waf configure --board ${{matrix.config}}' >> $GITHUB_STEP_SUMMARY  
          echo "### Build Plane ${{matrix.config}} :rocket:" >> $GITHUB_STEP_SUMMARY
          echo './waf plane' >> $GITHUB_STEP_SUMMARY  
          ./waf plane | tee >( grep -vE 'Compiling|Generat|includes.list|Parsing|Validation|Building|Linking|Detecting|linker|\*\*\*\*\*\*\*|Partition|SubType|Checking|esp-idf' --color=never --line-buffered > summary.log)  
          cat summary.log >> $GITHUB_STEP_SUMMARY 

          cp build/${{matrix.config}}/esp-idf_build/ardupilot.bin ./ArduPlane.${{matrix.config}}.bin
          cp build/${{matrix.config}}/esp-idf_build/ardupilot.elf ./ArduPlane.${{matrix.config}}.elf
          echo "### Build Copter ${{matrix.config}} :rocket:" >> $GITHUB_STEP_SUMMARY
          echo './waf copter' >> $GITHUB_STEP_SUMMARY  
          ./waf copter | tee >( grep -vE 'Compiling|Generat|includes.list|Parsing|Validation|Building|Linking|Detecting|linker|\*\*\*\*\*\*\*|Partition|SubType|Checking|esp-idf' --color=never --line-buffered > summary2.log)  

          cat summary2.log >> $GITHUB_STEP_SUMMARY  

          cp build/${{matrix.config}}/esp-idf_build/ardupilot.bin ./ArduCopter.${{matrix.config}}.bin
          cp build/${{matrix.config}}/esp-idf_build/ardupilot.elf ./ArduCopter.${{matrix.config}}.elf

          cp build/${{matrix.config}}/esp-idf_build/bootloader/bootloader.bin ./bootloader.${{matrix.config}}.bin
          cp build/${{matrix.config}}/esp-idf_build/partition_table/partition-table.bin ./partition-table.${{matrix.config}}.bin

          echo "### Assets avail in artifact:" >> $GITHUB_STEP_SUMMARY
          ls bootloader* partition* Ardu*.elf Ardu*.bin >> $GITHUB_STEP_SUMMARY

      - name: Archive artifacts
        uses: actions/upload-artifact@v4
        with:
           name: esp32-binaries -${{matrix.config}}
           path: |
              /home/<USER>/work/ardupilot/ardupilot/ArduPlane.${{matrix.config}}.bin
              /home/<USER>/work/ardupilot/ardupilot/ArduPlane.${{matrix.config}}.elf
              /home/<USER>/work/ardupilot/ardupilot/ArduCopter.${{matrix.config}}.bin
              /home/<USER>/work/ardupilot/ardupilot/ArduCopter.${{matrix.config}}.elf
              /home/<USER>/work/ardupilot/ardupilot/bootloader.${{matrix.config}}.bin
              /home/<USER>/work/ardupilot/ardupilot/partition-table.${{matrix.config}}.bin


           retention-days: 14
           
