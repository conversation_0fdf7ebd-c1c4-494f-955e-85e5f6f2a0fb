# How to contribute to the ArduPilot project?

If you are reading this page, you are possibly interested in contributing to our project.  We have a very active (and friendly) developer group and would love to have the help!  Possible ways you can help:

* Testing the code
* Filing issues on github, when you see a problem (or adding detail to existing issues that effect you)
* Fixing issues
* Adding new features
* Reviewing existing pull requests, and notifying the maintainer if it passes your code review.

# How to make a good bug report...

* Make sure your bug is not a support issue. Support issues should go to [the support forums](http://discuss.ardupilot.org) and include a .bin log file if possible. If you're not sure you have a bug, you should seek support first.
* Search for your bug, make sure it is not already reported. If it is already reported, make a comment on that issue.
* Only report one bug per issue report.
* Write a clear and concise summary. Be specific about what component of the software you are writing about, and follow the convention: "Copter: blah blah blah"
* Write a clear and concise description, with **particularly clear steps** to reproduce the problem. Include logs that display the bug. **Try to report only facts in your issue report, keeping your assumptions out of it.** 
* The majority of issues open now are good or acceptable by these guidelines. Please refer to them for examples.

# Submitting patches

Please see our [wiki article](https://ardupilot.org/dev/docs/submitting-patches-back-to-master.html).

# Development Team

The ArduPilot project is open source and [maintained](https://github.com/ArduPilot/ardupilot#maintainers) by a team of volunteers.

To contribute, you can send a pull request on GitHub.

New developers are recommended to join the `#general` channel on
[Discord](https://ardupilot.org/discord).

You can also join the
[development discussion on Discourse](https://discuss.ardupilot.org/c/development-team),
or [Gitter](https://gitter.im/ArduPilot/ardupilot).

Note that these are NOT for user tech support, and are moderated
for new users to prevent off-topic discussion.
