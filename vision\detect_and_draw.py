from ultralytics import YOLO
import cv2
import numpy as np
import time

# Model yolunu kendi modelinize göre ayarlayın
yolo_model_path = 'vision/weights/best.pt'

# Modeli yükle
model = YOLO(yolo_model_path)

# Sınıf isimlerini modelden otomatik al
CLASS_NAMES = model.names
print('Modeldeki sınıf isimleri:', CLASS_NAMES)

# Kamera başlat
cap = cv2.VideoCapture(0)

first_frame = True
prev_time = time.time()
fps = 0

while True:
    ret, frame = cap.read()
    if not ret:
        print('Kamera görüntüsü alınamadı!')
        break

    # FPS hesapla
    curr_time = time.time()
    fps = 1 / (curr_time - prev_time)
    prev_time = curr_time

    img_height, img_width = frame.shape[:2]
    img_center = (img_width // 2, img_height // 2)

    # YOLO ile tahmin (konsol çıktısı kapalı)
    results = model(frame, verbose=False)[0]
    boxes = results.boxes.xyxy.cpu().numpy() if results.boxes.xyxy is not None else []
    classes = results.boxes.cls.cpu().numpy() if results.boxes.cls is not None else []

    if first_frame:
        print('Tespit edilen sınıf indeksleri:', classes)
        first_frame = False

    for i, box in enumerate(boxes):
        x1, y1, x2, y2 = map(int, box)
        cls = int(classes[i]) if i < len(classes) else 0
        label = CLASS_NAMES[cls] if cls < len(CLASS_NAMES) else str(cls)

        # ROI'yi al
        roi = frame[y1:y2, x1:x2]
        if roi.size == 0:
            continue

        # ROI'yi griye çevir ve threshold uygula
        gray = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY)
        _, thresh = cv2.threshold(gray, 60, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)

        # Kontur bul
        contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        if not contours:
            continue

        # En büyük konturu al
        cnt = max(contours, key=cv2.contourArea)
        area = cv2.contourArea(cnt)
        if area < 0.2 * roi.shape[0] * roi.shape[1]:  # Alan çok küçükse atla
            continue

        # Konturu yaklaşıkla
        approx = cv2.approxPolyDP(cnt, 0.04 * cv2.arcLength(cnt, True), True)
        if len(approx) == 4 and cv2.isContourConvex(approx):
            # Dört köşe ve konveks ise kare olabilir
            # Kenar uzunluklarını kontrol et
            edges = [np.linalg.norm(approx[i][0] - approx[(i+1)%4][0]) for i in range(4)]
            min_edge = min(edges)
            max_edge = max(edges)
            if min_edge / max_edge > 0.8:
                # Gerçekten kare!
                if label == 'kirmizi_kare':
                    color = (0, 0, 255)
                elif label == 'mavi_kare':
                    color = (255, 0, 0)
                else:
                    color = (0, 255, 255)
                # Orijinal frame'e çiz
                cv2.rectangle(frame, (x1, y1), (x2, y2), color, 2)
                cv2.putText(frame, label, (x1, y1 - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)
                box_center = ((x1 + x2) // 2, (y1 + y2) // 2)
                # Koordinat sistemini merkez (0,0) olacak şekilde ayarla (yukarı +, aşağı -)
                rel_box_center = (box_center[0] - img_center[0], -(box_center[1] - img_center[1]))
                rel_img_center = (0, 0)
                # Doğruyu çiz
                cv2.line(frame, box_center, img_center, (0, 255, 0), 2)
                # Merkez noktalarını işaretle
                cv2.circle(frame, box_center, 5, color, -1)
                cv2.circle(frame, img_center, 5, (0, 255, 0), -1)
                # Koordinatları ve uzaklığı yaz
                dist = int(np.linalg.norm(np.array(rel_box_center)))
                cv2.putText(frame, f"{rel_box_center}", (box_center[0]+10, box_center[1]), cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)
                cv2.putText(frame, f"{rel_img_center}", (img_center[0]+10, img_center[1]), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0,255,0), 2)
                mid_point = ((box_center[0] + img_center[0]) // 2, (box_center[1] + img_center[1]) // 2)
                cv2.putText(frame, f"{dist}px", mid_point, cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0,255,0), 2)

    # FPS'i sol üst köşeye yaz
    cv2.putText(frame, f"FPS: {fps:.1f}", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (0,255,0), 2)

    cv2.imshow('YOLO Gerçek Kare Tespiti', frame)
    if cv2.waitKey(1) & 0xFF == ord('q'):
        break

cap.release()
cv2.destroyAllWindows() 