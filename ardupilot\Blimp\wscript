#!/usr/bin/env python
# encoding: utf-8

def build(bld):
    vehicle = bld.path.name
    bld.ap_stlib(
        name=vehicle + '_libs',
        ap_vehicle=vehicle,
        ap_libraries=bld.ap_common_vehicle_libraries() + [
            'AC_InputManager',
            'AP_InertialNav',
            'AP_Avoidance',
            'AP_LTM_Telem',
            'AP_Devo_Telem',
            'AP_KDECAN',
            'AP_AdvancedFailsafe', # TODO for some reason compiling GCS_Common.cpp (in libraries) fails if this isn't included.
            'AC_AttitudeControl',  # for PSCx logging
        ],
    )

    bld.ap_program(
        program_name='blimp',
        program_groups=['bin', 'blimp'],
        use=vehicle + '_libs',
        )
