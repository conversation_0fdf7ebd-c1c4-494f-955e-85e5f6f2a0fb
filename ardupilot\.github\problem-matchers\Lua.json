{"problemMatcher": [{"owner": "<PERSON><PERSON><PERSON>-problem-matcher", "fileLocation": ["relative", "${GITHUB_WORKSPACE}"], "pattern": [{"regexp": "^( *)(.+.lua):(\\d+):(\\d+): (.*)$", "file": 2, "line": 3, "column": 4, "message": 5}]}, {"owner": "Lua-language-server-problem-matcher", "fileLocation": ["relative", "${GITHUB_WORKSPACE}"], "pattern": [{"regexp": "^(.+\\.lua):(\\d+):(\\d+)(.*)$", "file": 1, "line": 2, "column": 3, "message": 4}]}]}