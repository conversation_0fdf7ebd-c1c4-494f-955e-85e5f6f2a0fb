ArduPilot Copter Release Notes:
------------------------------------------------------------------
Release 4.6.0 21-May-2025

Changes from 4.6.0-beta6

1) Bug Fixes

- Plane landing fix to handle AGL or AMSL locations
- TBS bootloaders updated to avoid firmware erase on ECC error
------------------------------------------------------------------
Release 4.6.0-beta6 28-Apr-2025

Changes from 4.6.0-beta5

1) Board specific changes

- ARK FPV, <PERSON>menierLuxF765, MicroAir743, StellarF4v2, X-MAV-AP-H743v2 OSD Type defaults fixed
- BETAFPV-F405-I2C UART6 fix
- BrotherHobby F405v3 and H743 support
- FlywooF405HD VTX power pin fixed
- NarinFC-H7 support
- TBS Lucid H7 Wing support
- TBS Lucid H7 support for both I2C ports

2) Driver bug fixes and enhancements

- DroneCAN semaphore bug fixed which affected MPPT battery and Serial-over-CAN
- GPS logs altitude above ellipsoid instead of undulation
- GSOF GPS protects against port misconfiguration
- UBlox GPS configuration sped up
- Lua script battery monitor failures that could cause hardfaults fixed
- Nova GPS undulation fix
- Proximity backends increased to 5
- SCHA63T IMU temperature reporting bug fixed

3) Plane specific fixes and enhancements

- Circle mode terrain alt handling fixed
- Fence re-enable after fence breach fixed
- Prevent rotation pitch calculations from running post-rotation
- Takeoff leveloff timeout check added
- Terrain guided target intermediate alt handling fixed

4) Copter SCurve navigation fix to avoid excessive vertical speed when passing through waypoints

5) Rover circle mode and QuickTune support smaller vehicles

6) Bug Fixes and minor enhancements

- AHRS initial orientation fixed when autopilot mounted in non-default orientation
- AIRSPEED mavlink message health flags fixed
- CAMERA_FOV_STATUS message always sent (once requested), attitude is earth-frame and FOV can be decimal numbers
- DDS / ROS2 fix to provide clock_gettime only on ChibiOS boards
- Lightware I2C and HC SR04 fixed timestamp for last reading (should only affect AP_Periph devices)
- "No ap_message for mavlink id" warning removed
- Power brick2 valid flag fixed on some boards
- Pre-arm check that gyro read rate is at least the main loop rate
------------------------------------------------------------------
Release 4.6.0-beta5 31-Mar-2025

Changes from 4.6.0-beta4

1) Board specfic changes

- MFT-SEMA100 compass orientation fix
- SpeedyBee F405 AIO support

2) Driver bug fixes and enhancements

- Bdshot locking issue fix for slow boards that could cause the flight controller to crash
- BMP280 barometer error handling during init improved
- CADDX gimbal RC update rate fixed
- Hexsoon 77G and 24G radar support
- IE FuelCell generator data rate increased
- IIS2MDC compass support
- LD19/LD06 proximity sensor data length sanity check fix
- RC output cork/push fix to avoid potential motor knocking
- SBF MosaicX5 packets with invalid length ignored
- SPL06 barometer accuracy improvements
- Ublox M10 configuration fix

3) Plane specific fixes and enhancements

- Tiltrotor motor check fixed used in throttle control handover from VTOL to forward flight
- Improved QAssist and VTOL mode recovery from bad attitudes
- Fixed rudder output in VTOL modes
- Added fix for indecision in the roll controller close to inverted
- Ensure tiltrotors use consistent fixed wing and VTOL rates
- Clear inverted control flag on Q-mode entry
- Auto-deploy landing gear on all fixed wing landings when enabled
- Prevent learning bad ARSP_RATIO values when not flying

4) Rover specific fixes and enhancements

- Lua bindings for Follow mode
- WATER_DEPTH mavlink message rate fix

5) Parameter description improvements

- ADSB_LIST_ALT param desc improved
- Alphabetised type params for CAN, Mount, OptFlow, Proximity, Rangefinder, Serial Manager
- Copter's battery failsafe action gets "Brake or Land"
- LOG_FILE_BUFSIZE units fixed
- MOT_THST_HOVER range fixed
- SERIALx_BAUD value added for PPP networking

6) Bug Fixes and minor enhancements

- Arming check for EKF3 velocity innovations
- Copter's LOG_BITMASK includes PIDs by default
- DO_SET_ROI_LOCATION mission command support
- MCU min/max voltage logging fix
- MIS_OPTION parameter handling fixed
------------------------------------------------------------------
Release 4.6.0-beta4 14-Feb-2025

Changes from 4.6.0-beta3

1) Board specfic changes

- BETAFPV F405 supports DPS310 baro
- BETAFPV F405 board variants added

2) Driver enhancements

- CADDX camera gimbal support
- UltraMotion CAN servo support

3) Copter specific fixes and minor enhancements

- SysId mode uninitialised variables fixed
- ARMING_OPTIONS gets "Require position for arming" (was in FLIGHT_OPTIONS)

4) Bug Fixes and minor enhancements

- AM32 ESC passthrough fixed
- BMP581 baro initialisation fix
- DDS/ROS2 driver waits indefinitely until companion computer running ROS2 starts
- Lua script potential deadlock fix when initialising mavlink in script
- Mcast and HW CAN bridging disabled by default
- Plane's TILT log message only logged on tilt-rotors
- ROMFS race condition when opening files that caused apparent script corruption fixed
- Serial flow control support on UARTS 6 to 8
- Serial passthrough fix to restore parity upon completion
- Serial protocol parameter fix to include I-Bus Telemetry
- uAvionix ping200X driver fixes
- Video stream information lua script param index fixed (avoids param conflict with other scripts)
- ViewPro object tracking fixed
------------------------------------------------------------------
Release 4.6.0-beta3 21-Jan-2025

Changes from 4.6.0-beta2

1) Board specfic changes

- AEROFOX-H7 support
- AET-H743-Basic support
- ESP32 memory initialisation fix
- MicoAir743AIO and MicoAir743v2 support
- OrqaF405Pro supports ICM42688 IMU
- TBS Lucid Pro support

2) Plane related enhancements and bug fixes

- RC aux channel option for C++ QuikTune
- TECs uses external HAGL (if available) for landing

3) DDS/ROS2 pre-arm check and copter takeoff service

4) Bug Fixes and minor enhancements

- BATTx_OPTIONS param desc fixed
- BLHeli telemetry ESC index fixed on boards with IOMCU
- CAN manager logging critical race condition fixed (only occurred if CAN_LOGLEVEL was 1 or higher)
- DShot EDTv2 logging fixed
- ICM45686 IMU FIFO read rate fixed (could read unnecessarily quickly)
- LDRobot LD06 proximity driver prevents possible read buffer overrun
- LDRobot LD06 proximity driver averages over 2deg slices (was 12 deg)
- RunCam/OSD menu movements obey RC channel reversal
- Topotek camera gimbal pitch rate control direction fixed
- TradHeli AutoTune rate and accel limiting fixed
- VTOL-quiktune script gets QUIK_ANGLE_MAX to prevent bad oscillation if tuning algorithm fails
------------------------------------------------------------------
Release 4.6.0-beta2 11 Dec 2024

Changes from 4.6.0-beta1

1) Board specfic changes

- FoxeerF405v2 supports BMP280 baro
- KakuteH7, H7-Mini, H7-Wing, F4 support SPA06 baro
- MUPilot support
- SkySakura H743 support
- TBS Lucid H7 support
- VUAV-V7pro README documentation fixed
- X-MAV AP-H743v2 CAN pin definition fixed

2) Copter specific enhancements and bug fixes

- AutoTune fix for calc of maximum angular acceleration
- Advanced Failsafe customer build server option

3) Plane related enhancements and bug fixes

- QuadPlane fix for QLand getting stuck in pilot repositioning
- QuikTune C++ conversion (allow running quiktun on F4 and f7 boards)
- Takeoff direction fixed when no yaw source
- TECS correctly handles home altitude changes

4) Bug Fixes and minor enhancements

- AIRSPEED_AUTOCAL mavlink message only sent when required and fixed for 2nd sensor
- CAN frame logging added to ease support
- CRSF reconnection after failsafe fixed
- EKF3 position and velocity resets default to user defined source
- Ethernet IP address default 192.168.144.x
- Fence autoenable fix when both RCn_OPTION=11/Fence and FENCE_AUTOENABLE = 3 (AutoEnableOnlyWhenArmed)
- Fence pre-arm check that vehicle is within polygon fence
- Fence handling of more than 256 items fixed
- FFT protection against divide-by-zero in Jain estimator
- Frsky telemetry apparent wind speed fixed
- Inertial sensors stop sensor converging if motors arm
- Inertial sensors check for changes to notch filters fixed
- Real Time Clock allowed to shift forward when disarmed
- ROS2/DDS get/set parameter service added
- Scripting gets memory handling improvements
- Scripting promote video-stream-information to applet
- Topotek gimbal driver uses GIA message to retrieve current angle
- Tramp VTX OSD power indicator fixed
------------------------------------------------------------------
Release 4.6.0-beta1 13 Nov 2024

Changes from 4.5.7

1) Board specific changes

- AnyLeaf H7 supports compass and onboard logging
- Blitz743Pro supports CAN
- BlueRobotics Navigator supports BMP390 baro
- Bootloader ECC failure check fixed on boards with >128k bootloader space (e.g CubeRed)
- CB Unmanned Stamp H743 support
- ClearSky CSKY405 support
- CUAV-7-Nano default batt monitor fixed
- CubeRed bootloader fixes including disabling 2nd core by default
- CubeRed supports PPP networking between primary and secondary MCU
- CubeRedPrimary supports external compasses
- ESP32 main loop rate improvements
- ESP32 RC input fixes and wifi connection reliability improved
- ESP32 safety switch and GPIO pin support
- FlyingMoon no longer support MAX7456
- Flywoo F405HD-AIOv2 ELRS RX pin pulled high during boot
- Flywoo H743 Pro support
- Flywoo/Goku F405 HD 1-2S ELRS AIO v2
- FlywooF745 supports DPS310 baro
- FPV boards lose SMBus battery support (to save flash)
- GEPRC F745BTHD support
- GEPRCF745BTHD loses parachute support, non-BMP280 baros (to save flash)
- Here4FC bootloader fix for mismatch between RAM0 and periph that could prevent firmware updates
- Holybro Kakute F4 Wing support
- iFlight 2RAW H743 supports onboard logging
- JFB110 supports measuring servo rail voltage
- JFB110 supports safety switch LED
- JHEM-JHEF405 bootloader supports firmware updates via serial
- JHEMCU GF30H743 HD support
- JHEMCU-GF16-F405 autopilot support
- JHEMCU-GSF405A becomes FPV board (to save flash)
- KakuteF7 only supports BMP280 baro (to save flash)
- KakuteH7Mini supports ICM42688 IMU
- Linux auto detection of GPS baud rate fixed
- Linux board scheduler jitter reduced
- Linux board shutdown fixes
- MakeFlyEasy PixPilot-V6Pro support
- MatekF405, Pixhawk1-1M-bdshot, revo-mini loses blended GPS (to save flash)
- MatekH7A3 support Bi-directional DShot
- MicoAir405v2 and MicoAir405Mini support optical flow and OSD
- MicoAir743 internal compass orientation fixed
- MicroAir405Mini, MicroAir743, NxtPX4v2 support
- MicroAir405v2 Bi-directional DShot and LED DMA fixes
- MicroAir405v2 defined as FPV board with reduced features (to save flash)
- ModalAI VOXL2 support including Starling 2 and Starling 2 max
- mRo Control Zero Classic supports servo rail analog input
- mRo KitCAN revC fixed
- Mugin MUPilot support
- OmnibusF7v2 loses quadplane support (to save flash)
- Pixhack-v3 board added (same as fmuv3)
- Pixhawk6C bootloader supports flashing firmware from SD card
- RadiolinkPIX6 imu orientation fixed
- RadiolinkPIX6 supports SPA06 baro
- ReaperF745 V4 FC supports MPU6000 IMU
- RPI5 support
- SDModelH7v2 SERIAL3/7/9_PROTOCOL param defaults changed
- Solo serial ports default to MAVLink1
- SpeedyBeeF405Wing gets Bi-directional DShot
- SpeedyBeeF405WING loses landing gear support, some camera gimbals (to save flash)
- Spektreworks boom board support
- TrueNavPro-G4 SPI does not share DMA
- X-MAV AP-H743v2 support

2) AHRS/EKF enhancements and fixes

- AHRS_OPTION to disable fallback to DCM (affects Plane and Rover, Copter never falls back)
- AHRS_OPTION to disable innovation check for airspeed sensor
- Airspeed sensor health check fixed when using multiple sensors and AHRS affinity
- DCM support for MAV_CMD_EXTERNAL_WIND_ESTIMATE (Plane only)
- EK2 supports disabling external nav (see EK2_OPTIONS)
- EK3 External Nav position jump after switch from Optical flow removed (see EK3_SRC_OPTION=1)
- EK3 uses filtered velocity corrections for IMU position
- EKF2, EKF3, ExternalAHRS all use common origin
- EKF3 accepts set origin even when using GPS
- EKF3 allows earth-frame fields to be estimated with an origin but no GPS
- EKF3 copes better with GPS jamming
- EKF3 logs mag fusion selection to XKFS
- EKF3 wind estimation when using GPS-for-yaw fixed
- External AHRS improvements including handling variances, pre-arm origin check
- Inertial Labs External AHRS fixes
- VectorNav driver fix for handling of error from sensor
- VectorNav External AHRS enhancements including validation of config commands and logging improvements
- VectorNav support for sensors outside VN-100 and VN-300

3) Driver enhancements and bug fixes

- ADSB fix to display last character in status text sent to GCS
- Ainstein LR-D1 radar support
- Airspeed ASP5033 whoami check fixed when autopilot rebooted independently of the sensor
- AIRSPEED message sent to GCS
- Analog temperature sensor extended to 5th order polynomial (see TEMP_A5)
- ARSPD_OPTIONS to report calibration offset to GCS
- Baro EAS2TAS conversions continuously calculated reducing shocks to TECS (Plane only)
- Baro provides improved atmospheric model for high altitude flight
- BARO_ALT_OFFSET slew slowed to keep EKF happy
- BATTx_ESC_MASK param supports flexible mapping of ESCs to batteries
- BATTx_OPTION to not send battery voltage, current, etc to GCS
- Benewake RDS02U radar support
- Bi-directional DShot on IOMCU supports reversible mask
- Bi-directional DShot telemetry support on F103 8Mhz IOMCUs
- BMM350 compass support
- CAN rangefinders and proximity sensors may share a CAN bus (allows NRA24 and MR72 on a single CAN bus)
- Compass calibration world magnetic model checks can use any position source (e.g. not just GPS)
- CRSF baro and vertical speeed fixed
- CRSF RX bind command support
- DroneCAN battery monitor check to avoid memory corruption when type changed
- DroneCAN DNA server fixes including removing use of invalid node IDs, faster ID allocation, elimination of rare inability to save info
- DroneCAN EFI health check fix
- DroneCAN ESC battery monitors calculate consumed mah
- DroneCAN ESCs forced to zero when disarmed
- DroneCAN RPM message support
- DroneCAN timeout fix for auxiliary frames
- DroneCAN to serial tunneling params accepts short-hand baud rates (e.g. '57' for '57600')
- F9P, F10-N and Zed-F9P support for GPSx_GNSS_MODE to turn on/off SBAS, Galileo, Beidou and Glonass
- FuelLevel battery monitor fix to report capacity
- GPS_xxx params renamed to GPS1_xxx, GPS_xxx2 renamed to GPS2_xxx
- Hirth EFI logging includes modified throttle
- Hirth ICEngine supports reversed crank direction (see ICE_OPTIONS parameter)
- Hott and LTM telemetry deprecated (still available through custom build server)
- i-BUS telemetry support
- ICE_PWM_IGN_ON, ICE_PWM_IGN_OFF, ICE_PWM_STRT_ON, ICE_PWM_STRT_OFF replaced with SERVOx_MIN/MAX/REVERSED (Plane only)
- ICE_START_CHAN replaced with RC aux function (Plane only)
- ICEngine retry max added (see ICE_STRT_MX_RTRY)
- IE 2400 generator error message severity to GCS improved
- INA2xx battery monitor support (reads temp, gets MAX_AMPS and SHUNT params)
- MCP9600 temperature sensor I2C address fixed
- MLX90614 temperature sensor support
- MSP GPS ground course scaling fixed
- MSP RC uses scaled RC inputs (fixes issue with RCx_REVERSED having no effect)
- Networking supports reconnection to TCP server or client
- OSD params for adjusting horizontal spacing and vertical extension (see OSD_SB_H_OFS, OSD_SB_V_EXT)
- Relay inverted output support (see RELAYx_INVERTED parameter)
- ROMFS efficiency improvements
- RS-485 driver enable RTS flow control
- Sagetech MXS ADSP altitude fix (now uses abs alt instead of terrain alt)
- Septentrio GPS sat count correctly drops to zero when 255 received
- Septentrio supports selecting constellations (see GPS_GNSS_MODE)
- Single LED for user notification supported
- SPA06 baro supported
- Sum battery monitor optionally reports minimum voltage instead of average
- Sum battery monitor reports average temp
- Torqeedo dual motor support (see TRQ1, TRQ2 params)
- Ublox GPS driver uses 64 bit time for PPS interrupt (avoids possible dropout at 1hour and 12 minutes)
- UBlox GPS time ignored until at least 2D fix
- VideoTX supports additional freq bands (RushFPV 3.3Ghz)
- Volz logs desired and actual position, voltage, current, motor and PCB temp
- Volz server feedback and logging fixed
- Volz servo output in its own thread resulting in smoother movements
- W25N02KV flash support

4) Networking enhancements and fixes

- Allow multiple UDP clients to connect/disconnect/reconnect
- Ethernet supports faster log downloading (raised SDMMC clock limit on H7)

5) Camera and gimbal enhancements

- Alexmos precision improved slightly
- CAMERA_CAPTURE_STATUS mavlink msg sent to GCS (reports when images taken or video recorded, used by QGC)
- CAMERA_FOV_STATUS mavlink reports lat,lon of what camera is pointing at
- DO_MOUNT_CONTROL yaw angle interpreted as body-frame (was incorrectly being interpreted as earth-frame)
- Dual serial camera gimbal mounts fixed
- Lua script bindings to send CAMERA_INFORMATION and VIDEO_STREAM_INFORMATION messages to GCS
- Retract Mount2 aux function added (see RCx_OPTION = 113)
- Servo gimbal reported angles respect roll, pitch and yaw limits
- Siyi driver sends autopilot location and speed (recorded in images via EXIF)
- Siyi picture and video download scripts
- Siyi ZT6 and ZT30 support sending min, max temperature (see CAMERA_THERMAL_RANGE msg)
- Siyi ZT6 and ZT30 thermal palette can be changed using camera-change-setting.lua script
- Siyi ZT6 hardware id and set-lens fixed
- Topotek gimbal support
- Trigger distance ignores GPS status and only uses EKF reported location

6) Harmonic notch enhancements

- Harmonic notch is active in forward flight on quadplanes
- Harmonic notch filter freq clamping and disabling reworked
- Harmonic notch handles negative ESC RPMs
- Harmonic notch supports per-motor throttle-based harmonic notch

7) Copter specific enhancements and bug fixes

- Attitude control fix to dt update order (reduces rate controller noise)
- Auto mode fix to avoid prematurely advancing to next waypoint if given enough time
- Auto mode small target position jump when takeoff completes removed
- Auto mode yaw drift when passing WP removed if CONDITION_YAW command used and WP_YAW_BEHAVIOR = 0/None
- Auto, Guided flight mode pause RC aux switch (see RCx_OPTION = 178)
- AutoRTL (e.g. DO_LAND_START) uses copter stopping point to decide nearest mission command
- AutoRTL mode supports DO_RETURN_PATH_START (Copter, Heli only)
- AutoTune fix to prevent spool up after landing
- AutoTune performance and safety enhancements (less chance of ESC desync, fails when vehicle likely can't be tuned well)
- Autotune test gains RC aux switch function allows testing gains in any mode (see RCx_OPTION = 180)
- Config error avoided if auto mode is paused very soon after poweron
- FLIGHT_OPTIONS bit added to require position estimate before arming
- Follow mode slowdown calcs fixed when target is moving
- Ground oscillation suppressed by reducing gains (see ATC_LAND_R/P/Y_MULT)
- Guided mode internal error fix when taking off using SET_ATTITUDE_CONTROL message
- Guided mode internal error resolved when switching between thrust or climb rate based altitude control
- Guided mode yaw fixed when WP_YAW_BEHAVIOR = 0/None and CONDITION_YAW command received containing relative angle
- Landing detector fixed when in stabilize mode at full throttle but aircraft is upside down
- Landing detector logging added to ease support (see LDET message)
- Loiter unlimited command accepts NaNs (QGC sends these)
- Mavlink SYSTEM_STATUS set to BOOT during initialisation
- MOT_PWM_TYPE of 9 (PWMAngle) respects SERVOx_MIN/MAX/TRIM/REVERSED param values
- Payload place bug fix when aborted because gripper is already released
- RC based tuning (aka CH6 tuning) can use any RC aux function channel (see RCx_OPTION = 219)
- RTL_ALT minimum reduced to 30cm
- SystemID position controller support (Copter and Heli)
- TriCopter motor test and slew-up fixed
- WPNAV_SPEED min reduced to 10 cm/s (Copter only)
- Loiter mode zeros desired accel when re-entering from Auto during RC failsafe

8) TradHeli specific enhancements

- Autorotation yaw behaviour fix
- Autotune improvements including using low frequency dwell for feedforward gain tuning and conducting sweep in attitude instead of rate
- Blade pitch angle logging added (see SWSH log message)
- Constrain cyclic roll for intermeshing
- ICE / turbine cool down fix
- Inverted flight extended to non manual throttle modes
- Inverted flight transitions smoothed and restricted to only Stabilize mode
- SWSH logging fix for reversed collectives

9) Plane specific enhancements and bug fixes

- AIRSPEED_STALL holds vehicle stall speed and is used for minimum speed scaling
- Allow for different orientations of landing rangefinder
- Assistance requirements evaluted on mode change
- FBWB/CRUISE climb/sink rate limited by TECS limits
- FLIGHT_OPTION to immediately climb in AUTO mode (not doing glide slope)
- Glider pullup support (available only through custom build server)
- Loiter breakout improved to better handle destinations inside loiter circle
- Manual mode throttle made consistent with other modes (e.g battery comp and watt limit is done if enabled)
- Mavlink GUIDED_CHANGE_ALTITUDE supports terrain altitudes
- Minimum throttle not applied during SLT VTOL airbrake (reduces increase in airspeed and alt during back transition)
- Q_APPROACH_DIST set minimum distance to use the fixed wing approach logic
- Quadplane assistance check enhancements
- Quadplane Deca frame support
- Quadplane gets smoother takeoff by input shaping target accel and velocity
- Servo wiggles in altitude wait staged to be one after another
- Set_position_target_global_int accepts MAV_FRAME_GLOBAL_RELATIVE_ALT and MAV_FRAME_GLOBAL_TERRAIN_ALT frames
- Takeoff airspeed control improved (see TKOFF_MODE, TKOFF_THR_MIN)
- Takeoff fixes for fence autoenable
- Takeoff improvements including less overshoot of TKOFF_ALT
- TECS reset along with other controllers (important if plane dropped from balloon)
- Tilt quadplane ramp of motors on back transition fixed
- Tiltrotor tilt angles logged
- TKOFF_THR_MIN applied to SLT transitions
- Twin motor planes with DroneCAN ESCs fix to avoid max throttle at arming due to misconfiguration
- VTOLs switch to QLAND if a LONG_FAILSAFE is triggered during takeoff

10) Rover specific enhancements and bug fixes

- Auto mode reversed state maintained if momentarily switched to Hold mode
- Circle mode tracks better and avoids getting stuck at circle edge
- Flight time stats fixed
- MAV_CMD_NAV_SET_YAW_SPEED deprecated
- Omni3Mecanum frame support
- Stopping point uses max deceleration (was incorrectly using acceleration)
- Wheel rate controller slew rate fix

11) Antenna Tracker specific enhancements and bug fixes

- Never track lat,lon of 0,0

12) Scripting enhancements

- advance-wp.lua applet supports advancing Auto mode WP via RC switch
- AHRS_switch.lua supports switching between EKF2 and EKF3 via RC switch
- battery_internal_resistance_check.lua monitors battery resistance
- CAN:get_device returns nil for unconfigured CAN device
- copter_terrain_brake.lua script added to prevent impact with terrain in Loiter mode (Copter only)
- Copter:get_target_location, update_target_location support
- crosstrack_restore.lua example allows returning to previous track in Auto (Plane only)
- Display text on OLED display supported
- Docs improved for many bindings
- EFI get_last_update_ms binding
- EFI_SkyPower.lua driver accepts 2nd supply voltage
- ESC_slew_rate.lua example script supports testing ESCs
- Filesystem CRC32 check to allow scripts to check module versions
- forced arming support
- GPIO get/set mode bindings (see gpio:set_mode, get_mode)
- GPS-for-yaw angle binding (see gps:gps_yaw_deg)
- Halo6000 EFI driver can log all CAN packets for easier debugging
- handle_external_position_estimate binding allows sending position estimate from lua to EKF
- I2C:transfer support
- IMU gyros_consistent and accels_consistent bindings added
- INF_Inject.lua driver more robust to serial errors, improved logging, throttle and ignition control
- INS bindings for is calibrating, gyro and accel sensor values
- IPV4 address bindings (see SocketAPM_ipv4_addr_to_string) to allow UDP server that responds to individual clients
- Logging of booleans supported
- Lua language checks improved (finds more errors)
- MAVLink commands can be called from scripting
- MCU voltage binding (see analog:mcu_voltage)
- NMEA 2000 EFI driver (see EFI_NMEA2k.lua)
- "open directory failed" false warning when scripts in ROMFS fixed
- Param_Controller.lua supports quickly switching between 3 parameter sets via RC switch
- Pass by reference values are always initialized
- pelco_d_antennatracker.lua applet supports sending Pelco-D via RS-485 to PTZ cameras
- plane_aerobatics.lua minor enhancements
- REPL applet (read-evaluate-print-loop, see repl.lua) for interactive testing and experimentation
- "require" function failures in rare circumstances fixed
- "require" function works for modules in ROMFS (e.g. not on SD card)
- revert_param.lua supports more params (e.g ATC_RATE_R/P/Y, PTCH2SRV_TCONST, RLL2SRV_TCONST, TECS_)
- Scripts may receive mavlink messages which fail CRC (e.g messages which FC does not understand)
- SD card formatting supported
- Serial device simulation support (allows Lua to feed data to any supported serial protocol for e.g. sensor simulation)
- set_target_throttle_rate_rpy allows rate control from scripts (new for Copter)
- sitl_standby_sim.lua example shows how to switch between redundant flight controllers using an RC switch
- Slung payload oscillation suppression applet added (see copter-slung-payload.lua)
- Temperature sensor bindings added
- uint64 support
- Various performance and memory usage optimizations
- VTOL-quicktune.lua minor enhancements including increasing YAW_FLTE_MAX to 8
- x-quad-cg-allocation.lua applet corrects for the CoM discrepancy in a quad-X frame

13) GCS / mavlink related changes and fixes

- BATTERY2 message deprecated (replaced by BATTERY_STATUS)
- CMD_MISSION_START/STOP rejected if first-item/last-item args provided
- Deny attempts to upload two missions simultaneously
- Fence and Rally points may be uploaded using FTP
- GPS_INPUT and HIL_GPS handles multiple GPSs
- HIGHRES_IMU mavlink message support (used by companion computers to receive IMU data from FC)
- MAV_CMD_COMPONENT_ARM_DISARM accepts force arm magic value of 21196
- MAV_CMD_DO_SET_SAFETY_SWITCH_STATE support
- MAV_CMD_SET_HAGL support (Plane only)
- MAVFTP respects TX buffer flow control to improve FTP on low bandwidth links
- MAVLink receiver support (RADIO_RC_CHANNELS mavlink message)
- Message interval supports TERRAIN_REPORT msg
- Mission upload may be cancelled using MISSION_CLEAR_ALL
- MOUNT_CONFIGURE, MOUNT_CONTROL messages deprecated
- RC_CHANNELS_RAW deprecated
- Serial passthrough supports parity allowing STM32CubeProgrammer to be used to program FrSky R9 receivers
- SET_ATTITUDE_TARGET angular rate input frame fixed (Copter only)
- TIMESYNC and NAMED_VALUE_FLOAT messages not sent on high latency MAVLink ports

14) Logging enhancements and fixes

- AC_PID resets and I-term sets logged
- ANG provides attitude at high rate (equivalent to ATT)
- ATT logs angles as floats (better resolution than ints)
- CAND message gets driver index
- DCM log message includes roll/pitch and yaw error
- EDT2 log msg includes stress and status received via extended DShot Telemetry v2
- EFI ECYL cylinder head and exhaust temp logs in degC (was Kelvin)
- ESCX log msg includes DroneCAN ESCs status, temp, duty cycle and power pct
- FMT messages written as required instead of all at beginning
- Logging restarted after download completes when LOG_DISARMED = 1
- MISE msg logs active mission command (CMD logged when commands are uploaded)
- ORGN message logging fixed when set using SET_GPS_GLOBAL_ORIGIN
- RPM sensor logging gets instance field, quality and health fields
- Short filename support removed (e.g log1.BIN instead of 00000001.BIN)
- Temperature sensor logging option for only sensors with no source (see TEMP_LOG)
- UART data rates logged at 1hz (see UART message)

15) ROS2 / DDS support

- Airspeed published
- Battery topic reports all available batteries
- Compile-time configurable rates for each publisher
- DDS_TIMEOUT_MS and DDS_MAX_RETRY set timeout and num retries when client pings XRCE agent
- GPS global origin published at 1 Hz
- High frequency raw imu data transmission
- Joystick support
- Support sending waypoints to Copter and Rover
- Remove the XML refs file in favor of binary entity creation

16) Safety related enhancements and fixes

- Accel/Gyro inconsistent message fixed for using with only single IMU
- Battery monitor failure reported to GCS, triggers battery failsafe but does not take action
- Far from EKF origin pre-arm check removed (Copter only)
- Fence breach warning message slightly improved
- Fence enhancements incluiding alt min fence (Copter only, see FENCE_AUTOENABLE, FENCE_OPTIONS)
- Fences can be stored to SD Card (see BRD_SD_FENCE param)
- ICEngine stopped when in E-Stop or safety engaged (Plane only)
- LEDs flash green lights based on EKF location not GPS
- Parachute option to skip disarm before parachute release (see CHUTE_OPTIONS)
- Plane FENCE_AUTOENABLE of 1 or 2 deprecation warning added
- Pre-arm check if OpenDroneID is compiled in but disabled
- Pre-arm check of duplicate RC aux functions fixed (was skipping recently added aux functions)
- Pre-arm checks alert user more quickly on failure
- Prearm check for misconfigured EAHRS_SENSORS and GPS_TYPE
- Proximity sensor based avoidance keeps working even if one proximity sensor fails (Copter, Rover)
- RC aux functions for Arm, Disarm, E-Stop and Parachute ignored when RC is first turned on
- Warning of duplicate SERIALx_PROTOCOL = RCIN

17) Other bug fixes and minor enhancements

- Accel cal fixed for auxiliary IMUs (e.g. IMU4 and higher)
- Bootloader fix to reduce unnecessary mass erasing of flash when using STLink or Jlink tools
- Bootloader rejects allocation of broadcast node ID
- CAN forward registering/de-registering fix (affected Mission Planner DroneCAN UI)
- Dijkstras fix to correct use of uninitialised variable
- DShot rates are not limited by NeoPixel rates
- Ethernet and CAN bootloader fix to prevent getting stuck in bootloader mode
- Filesystem does not show entries for empty @ files
- Filesystem efficiency improvements when reading files
- Flight statistics only reset if user sets STAT_RESET to zero (avoids accidental reset)
- Flight time statistics updated on disarm (avoids issue if vehicle powered-down soon after disarm)
- Internal error thrown if we lose parameters due to saving queue being too small
- MAVLink via DroneCAN baud rate fix
- SPI pins may also be used as GPIOs
- Terrain cache size configurable (see TERRAIN_CACHE_SZ)

18) Developer focused fixes and enhancements

- AP_Camera gets example python code showing how to use GStreamer with UDP and RTSP video streams
- Cygwin build fix for non-SITL builds
- Cygwin build fixed with malloc wrap
- DroneCAN and scripting support FlexDebug messages (see CAN_Dn_UC_OPTION, FlexDebug.lua)
- EKF3 code generator documentation and cleanup
- GPS jamming simulator added
- MacOS compiler warnings reduced
- SFML joystick support
- SITL support for OpenBSD
- Text warning if older Fence or Rally point protocols are used
------------------------------------------------------------------
Release 4.5.7 08 Oct 2024

Changes from 4.5.7-beta1

1) Reverted Septentrio GPS sat count correctly drops to zero when 255 received
------------------------------------------------------------------
Release 4.5.7-beta1 26 Sep 2024

Changes from 4.5.6

1) Bug fixes and minor enhancements

- VUAV-V7pro support
- CUAV-7-Nano correction for LEDs and battery volt and current scaling
- DroneCAN deadlock and saturation of CAN bus fixed
- DroneCAN DNA server init fix (caused logging issues and spam on bus)
- F4 boards with inverter support correctly uninvert RX/TX
- Nanoradar M72 radar driver fix for object avoidance path planning
- RC support for latest version of GHST
- Septentrio GPS sat count correctly drops to zero when 255 received
- TradHeli DDVP tail rotor pitch actuator fixed

2) ROS2/DDS and other developer focused enhancements

- AP quaternions normalised for ROS2 to avoid warnings
- Dependencies fixed for easier installation
- ROS2 SITL launch file enhancements including displaying console and map
- ROS_DOMAIN_ID param added to support multiple vehicles or instances of ROS2
- Python 3.12 support
------------------------------------------------------------------
Release 4.5.6 03 Sep 2024

No changes from 4.5.6-beta1
------------------------------------------------------------------
Release 4.5.6-beta1 20 Aug 2024

Changes from 4.5.5

1) Board specific enhancements and bug fixes

- 3DR Control Zero H7 Rev G support
- CUAV-7-Nano support
- FoxeerF405v2 servo outputs increased from 9 to 11
- Holybro Pixhawk6C hi-power peripheral overcurrent reporting fixed
- iFlight 2RAW H7 support
- MFT-SEMA100 support
- TMotorH743 support BMI270 baro
- ZeroOneX6 support

2) Minor enhancements and bug fixes

- Cameras using MAVLink report vendor and model name correctly
- DroneCAN fix to remove occasional NodeID registration error
- GPS NMEA and GSoF driver ground course corrected (now always 0 ~ 360 deg)
- ICP101XX barometer slowed to avoid I2C communication errors
- IMU temp cal param (INSn_ACCSCAL_Z) stored correctly when bootloader is flashed
- IMU gyro/accel duplicate id registration fixed to avoid possible pre-arm failure
- Logging to flash timestamp fix
- OSD displays ESC temp instead of motor temp
- PID controller error calculation bug fix (was using target from prev iteration)
- Relay on MAIN pins fixed

3) Copter specific fixes

- Payload place bug fix (calimb rate after releasing payload was unreliable)

------------------------------------------------------------------
Release 4.5.5 1st Aug 2024

No changes from 4.5.5-beta2
------------------------------------------------------------------
Release 4.5.5-beta2 27 July 2024

Changes from 4.5.5-beta1

1) Board specific enhancements and bug fixes

- CubeRed's second core disabled at boot to avoid spurious writes to RAM
- CubeRed bootloader's dual endpoint update method fixed
------------------------------------------------------------------
Release 4.5.5-beta1 1st July 2024

Changes from 4.5.4

1) Board specific enhancements and bug fixes

- fixed IOMCU transmission errors when using bdshot
- update relay parameter names on various boards
- add ASP5033 airspeed in minimal builds
- added RadiolinkPIX6
- fix Aocoda-RC H743Dual motor issue
- use ICM45686 as an ICM20649 alternative in CubeRedPrimary

2) System level minor enhancements and bug fixes

- correct use-after-free in script statistics
- added arming check for eeprom full
- fixed a block logging issue which caused log messages to be dropped
- enable Socket SO_REUSEADDR on LwIP
- removed IST8310 overrun message
- added Siyi ZT6 support
- added BTFL sidebar symbols to the OSD
- added CRSF extended link stats to the OSD
- use the ESC with the highest RPM in the OSD when only one can be displayed
- support all Tramp power levels on high power VTXs
- emit jump count in missions even if no limit
- improve the bitmask indicating persistent parameters on bootloader flash	
- fix duplicate error condition in the MicroStrain7

3) AHRS / EKF fixes

- fix infinite climb bug when using EK3_OGN_HGT_MASK

4) Copter specific changes

- fix MAV_CMD_CONDITION_YAW with relative angle

5) Other minor enhancements and bug fixes

- specify pymonocypher version in more places
- added DroneCAN dependencies to custom builds

------------------------------------------------------------------
Release 4.5.4 12th June 2024

Changes from 4.5.3

Disable highres IMU sampling on ICM42670 fixing an issue on some versions of Pixhawk6X

------------------------------------------------------------------
Release 4.5.3 28th May 2024

No changes from 4.5.3-beta1
------------------------------------------------------------------
Release 4.5.3-beta1 16th May 2024

Changes from 4.5.2

1) Board specific enhancements and bug fixes

- correct default GPS port on MambaH743v4
- added SDMODELV2
- added iFlight Blitz H7 Pro
- added BLITZ Wing H743
- added highres IMU sampling on Pixhawk6X

2) System level minor enhancements and bug fixes

- fixed rare crash bug in lua scripting on script fault handling
- fixed Neopixel pulse proportions to work with more LED variants
- fixed timeout in lua rangefinder drivers
- workaround hardware issue in IST8310 compass
- allow FIFO rate logging for highres IMU sampling

3) Copter specific changes

- fixed speed constraint during avoidance backoff

------------------------------------------------------------------
Release 4.5.2 14th May 2024

No changes from 4.5.2-beta1
------------------------------------------------------------------
Release 4.5.2-beta1 29th April 2024

Changes from 4.5.1

1) Board specific enhancements and bug fixes

- FoxeerF405v2 support
- iFlight BLITZ Mini F745 support
- Pixhawk5X, Pixhawk6C, Pixhawk6X, Durandal power peripherals immediately at startup

2) System level minor enhancements and bug fixes

- Camera lens (e.g. RGB, IR) can be selected from GCS or during missions using set-camera-source
- Crashdump pre-arm check added
- Gimbal gets improved yaw lock reporting to GCS
- Gimbal default mode fixed (MNTx_DEFLT_MODE was being overriden by RC input)
- RM3100 compass SPI bus speed reduced to 1Mhz
- SBUS output fix for channels 1 to 8 also applying to 9 to 16
- ViewPro gimbal supports enable/disable rangefinder from RC aux switch
- Visual Odometry delay fixed (was always using 1ms delay, see VISO_DELAY_MS)
- fixed serial passthrough to avoid data loss at high data rates

3) AHRS / EKF fixes

- Compass learning disabled when using GPS-for-yaw
- GSF reset minimum speed reduced to 1m/s (except Plane which remains 5m/s)
- MicroStrain7 External AHRS position quantization bug fix
- MicroStrain7 init failure warning added
- MicroStrain5 and 7 position and velocity variance reporting fix

4) Copter specific changes

- Auto mode condition yaw fix to avoid pointing at out-of-date target
- Guided mode angle control yaw target initialisation fix (was always turning North)

5) Other minor enhancements and bug fixes

- DDS_UDP_PORT parameter renamed (was DDS_PORT)
- Harmonic Notch bitmask parameter conversion fix (see INS_HNTCH_HMNCS)

------------------------------------------------------------------

Release 4.5.1 8th April 2024

This release fixes a critical bug in the CRSF R/C protocol parser that
can lead to a handfault and a vehicle crashing. A similar fix was
applied to the GHST protocol, although we believe that GHST could not
be affected by the bug, so this was just a precaution.

There are no other changes in this release.

------------------------------------------------------------------
Release 4.5.0 2nd April 2024

No changes from 4.5.0-beta4
------------------------------------------------------------------
Release 4.5.0-beta4 22nd March 2024

Changes from 4.5.0-beta3

1) system changes

- fixed a cache corruption issue with microSD card data on H7 based boards
- rename parameter NET_ENABLED to NET_ENABLE
- fixed FDCAN labels for adding new H7 boards
- avoid logging dma.txt to save CPU
- fixed roll/pitch in viewpro driver
- added band X in VideoTX
- fixed quaternion attitude reporting for Microstrain external AHRS
- add RPLidarC1 proximity support

2) new boards
- added MicoAir405v2
- add Orqa F405 Pro

------------------------------------------------------------------
Release 4.5.0-beta3 14-March-2024

Changes from 4.5.0-beta2

1) Board specific changes
- added PixFlamingo F7 board
- support ICM42688 on BlitzF745AIO
- fixed IMU orientation of CubeRedSecondary
- enable all FPV features on SpeedyBeeF405WING

2) System level changes

- improved robustness of CRSF parser
- reduced memory used by DDS/ROS2
- added filesystem crc32 binding in lua scripting
- support visual odometry quality metric and added autoswitching lua script
- allow for expansion of fence storage to microSD for larger pologon fences
- allow FTP upload of fence and rally points
- fixed vehicle type of ship simulation for ship landing
- make severity level depend on generator error level in IE 2400 generator
- speed up initial GPS probe by using SERIALn_BAUD first
- allow NanoRadar radar and proximity sensor to share the CAN bus
- added MR72 CAN proximity sensor
- only produce *_with_bl.hex not *.hex in builds if bootloader available
- fixed check for GPS antenna separation in moving baseline yaw
- added GPS_DRV_OPTIONS options for fully parsing RTCMv3 stream
- fixed logging of RTCM fragments in GPS driver
- fixed video recording while armed
- robostness and logging improvements for ExternalAHRS
- fixed RPM from bdshot on boards with IOMCU
- fixed accel cal simple to remove unused IMUs

3) Copter specific changes
- check fence breaches more often on copter for smaller overrun
- improved copter follow mode at close distances
- fixed default for FLTD for yaw
- fixed reset_target_and_rate method in attitude control

------------------------------------------------------------------
Copter 4.5.0-beta2 14-February-2024

Changes from 4.5.0-beta1:

1) New Autopilots supported
 - YJUAV_A6Ultra
 - AnyLeaf H7

2) System level changes
 - fixed float rounding issue in HAL_Linux millis and micros functions
 - fixed loading of defaults.parm parameters for dynamic parameter subtrees
 - fixed discrimination between GHST and CRSF protocols
 - fixed bug in DroneCAN packet parsing for corrupt packets that could cause a crash
 - fixed handling of network sockets in scripting when used after close
 - fixed bit timing of CANFD buses

3) Copter specific changes
 - added filter to EKF variances for EKF failsafe

4) Camera and gimbal enhancements
 - wait for non-zero camera version in SIYI driver

5) Miscellaneous
 - do relay parameter conversion for parachute parameters if ever has been used
 - broaden acceptance criteria for GPS yaw measurement for moving baseline yaw

------------------------------------------------------------------
Copter 4.5.0-beta1 30-Jan-2024
Changes from 4.4.4
1) New autopilots supported
    - ACNS-F405AIO
    - Airvolute DCS2 onboard FMU
    - Aocoda-RC-H743Dual
    - BrainFPV RADIX 2 HD
    - CAN-Zero
    - CM4Pilot
    - CubeRed
    - Esp32-tomte76, esp32nick, esp32s3devkit
    - FlyingMoonH743
    - Flywoo F405 Pro
    - FlywooF405S-AIO with alternative IMUs
    - Here4 GPS as flight controller
    - Holybro 6X revision 6
    - Holybro6X-45686 with 3x ICM45686 IMUs
    - JAE JFB110
    - KakuteH7 using ICM42688
    - PixFlamingo (uses STM32L4PLUS CPU)
    - PixPilot-C3
    - PixSurveyA1-IND
    - QiotekAdeptF407
    - Sierra TrueNavIC
    - SPRacing H7RF
    - SW-Nav-F405
    - YJUAV_A6
    - YJUAV_A6SE, YJUAV_A6SE_H743
2) Autopilot specific changes
    - 1MB boards lose features to save flash (Payload Place, some battery monitors, NMEA Output, bootloaders, Turtle mode)
    - CubeOrangePlus supports IMU high resolution sampling (works with ICM42688, ICM42652, ICM42670, ICM45686 IMUs)
    - F4 processors with only 1 IMU gain Triple Harmonic Notch support
    - F765-SE bdshot support on 1st 4 pin
    - F7 and H7 boards lose DMA on I2C ports (not required, limited DMA better used elsewhere)
    - FlyingMoonH743, FlyingMoonF427 2nd and 3rd IMU order swapped
    - HEEWING-F405 supports CRSF
    - MatekL431-RC bootloader added, DMA used for RC and GPS
    - PH4-mini, PH4-mini-bdshot, Swan-K1 and TBS-Colibri-F7 BRD_SER4_RTSCTS param conflict fixed
    - Pixhawk6C supports BMI088 baro
    - TMotorH743, Heewing-F405 serial parameter definitions fixed
3) AHRS/EKF enhancements and fixes
    - AHRS_OPTIONS supports disabling fallback to DCM
    - BARO_ALT_OFFSET slews more slowly (was 20sec, now 1min)
    - EKF2 removed (can be re-enabled with Custom build server)
    - External AHRS support for multiple GPSs
    - InertialLabs INS-U external AHRS support
    - Lord external AHRS renamed to MicroStrain5
    - MAV_CMD_EXTERNAL_POSITION_ESTIMATE supports setting approximate position during dead-reckoning
    - Microstrain7 (aka 3DM-QG7) external AHRS support
4) Driver enhancements
    - 3DR Solo supports up to 6S batteries
    - Airspeed health checks vs GPS use 3D velocity
    - BDshot on the first four channels of boards with F103-based IOMCUs (e.g. Pixhawk 6X)
    - Dshot on all IOMCU channels on all boards with an IOMCU (e.g. all CubePilot autopilots)
    - Dshot commands (e.g. motor reversal abd beeps) and EDT supported on IOMCU
    - DroneCAN battery monitors calculate consumed energy if battery doesn't provide directly
    - DroneCAN RC and Ghost RC protocol support
    - EFI MAVLink driver
    - Extended DShot Telemetry support (requires BLHeli32 ver 32.10 or BlueJay, set SERVO_DSHOT_ESC=3 or 4)
    - GPS L5 band health override to enable L5 signal use (see GPS_DRV_OPTIONS)
    - GPS-for-yaw works at lower update rate (3hz minimum)
    - GSOF GPS supports GPS_COM_PORT parameter
    - Hirth ICEngine support
    - ICE option to enable/disable starting while disarmed
    - ICE support for starter via relay
    - IMUDATA serial protocol outputs raw IMU data on serial port (only available using custom build server)
    - Innomaker LD06 360deg lidar support
    - Intelligent Energy fuel cells new protocol support
    - IRC Tramp supports 1G3 bands A and B
    - IRC Ghost support
    - JAE JRE-30 radar
    - KDECAN driver rewrite (params moved to KDE_, works on all vehicles)
    - MCP9601 temperature sensor support
    - NanoRadar NRA24 rangefinder support
    - NeoPixelsRGB support
    - NoopLoop TOFSense, TOFSenseF-I2c rangefinder support
    - OSD shows flashing horizon when inverted
    - OSD2 support (e.g. second OSD)
    - QMC5883P compass support
    - Relay refactored to support RELAYx_FUNCTION, RELAY_STATUS message support added
    - Reventech fuel level support (extends existing analog driver, see BATT_FL_xx parameters)
    - RPLidarS1 360 deg lidar support and improved reliability for all RPLidars
    - SBF GPS supports yaw from dual antennas
    - Temperature sensor using analog voltages supported
    - Trimble PX-1 support added as a GPS
    - Winch driver enhancements including stuck protection, option for spin-free on startup
5) Control and navigation changes and enhancements
    - Auto missions can always be cleared while disarmed (would fail if mission still running)
    - DO_ENGINE_CONTROL allows starting engine even when disarmed
    - DO_SET_MISSION_CURRENT command can reset mission (see Reset Mission field)
    - DO_SET_SERVO, DO_REPEAT_SERVO work with servo outputs set to RCInxScaled
    - Fractional Loiter Turn Support in missions
    - HarmonicNotch supports up to 16 harmonics
    - JUMP command sends improved text msg to pilot (says where will jump to)
    - MAV_CMD_AIRFRAME_CONFIGURATION can control landing gear on all vehicles
    - MOT_OPTIONS allows voltage compensation to use raw battery voltages (instead of current corrected voltage)
    - PID controllers get DFF/D_FF (derivative feed-forward), NTF (target notch filter index) and NEF (error notch filter index)
    - PID controllers get PDMX param to limit P+D output (useful for large vehicles and/or slow actuators)
    - PID notch filter configured via new filter library using FILT parameters
    - RTL mode uses RTL_CLIMB_MIN even if within cone slope (previously always climbed 2m)
6) Copter specific enhancements
    - MOT_SPOOL_TIM_DN allows slower spool down of motors
    - Precision landing gets fast descent option (see PLND_OPTIONS)
    - Throw mode min and max altitude support (see THROW_ALT_MIN/MAX)
    - TKOFF_TH_MAX allows lower throttle during takeoff
    - ZigZag mode sends position target to GCS
7) TradHeli specific enhancements
    - Arming checks improved
    - Motor test removed (it was non-functional)
    - OSD shows main rotor RPM
    - RPM based dynamic notch can track below reference (e.g. below INS_HNTCH_FM_RAT)
    - Thrust linearization for DDFP tails
    - Heli_idle_control.lua closed loop throttle control Lua script
8) Parameters renamed
    - COMPASS_TYPEMASK renamed to COMPASS_DISBLMSK
9) ROS2 / DDS support
    - Added support for EProsima MicroXRCEDDS as a transport in SITL and hardware
    - Added sensor data topic support such as NavSatStatus and BatteryState
    - Added velocity control support in Copter
    - Added a new AP_ExternalControl library for generic control support in SI units
    - Added support for building ArduPilot with the colcon build system
    - DDS topics comply with ROS REP-147
    - Added Gazebo Garden and Gazebo Harmonic simulation with examples
    - Added support for ROS 2 services such as Arm and Mode control
    - Added high level goal interface for waypoints
    - Wiki updated to support ROS 2
    - Added ROS 2 launch scripts for SITL, MAVProxy and micro-ROS agent
    - Add pytests for DDS client and ROS 2 launch scripts and integrate into CI
10) Camera and gimbal enhancements
    - Calculates location where camera gimbal is pointing (see CAMERA_FOV_STATUS)
    - CAMx_MNT_INST allows specifying which mount camera is in
    - Camera lens (e.g. live video stream) selectable using RC aux function
    - Follow mode can point gimbal at lead vehicle
    - Circle mode can point gimbal at circle center (see CIRCLE_OPTIONS)
    - Interval timer (for taking pictures at timed intervals)
    - Image tracking support (ViewPro only)
    - MAVLink Gimbal Protocol v2 support for better GCS integration
    - MNTx_SYSID_DFLT allows easier pointing gimbal at another vehicle
    - MOUNT_CONTROL, MOUNT_CONFIGURE messages deprecated
    - RangeFinder support (only logged, only supported on Siyi, Viewpro)
    - Pilot's RC input re-takes manual control of gimbal (e.g. switches to RC_TARGETING mode)
    - Siyi driver gets Zoom control, sends autopilot attitude and time (reduces leans)
    - Video recording may start when armed (see CAMx_OPTIONS)
    - ViewPro driver (replaces equivalent Lua driver)
    - Xacti camera gimbal support
    - Zoom percentage support (for both missions and GCS commands)
11) Logging and reporting changes
    - Battery logging (e.g. BAT) includes health, temperature, state-of-health percentage
    - CAM and MNT messages contain camera gimbal's desired and actual angles
    - INS_RAW_LOG_OPT allows raw, pre-filter and post-filter sensor data logging (alternative to "batch logging", good for filtering analysis)
    - PID logging gets reset and I-term-set flags
    - Rangefinder logging (e.g. RFND) includes signal quality
    - RC aux functions sorted alphabetically for GCS
    - RC logging (RCI, RCI2) include valid input and failsafe flags
    - RTK GPS logging includes number of fragments used or discarded
12) Scripting enhancements
    - Autopilot reboot support
    - Baro, Compass, IMU, IOMCU health check support
    - Battery cell voltage bindings
    - Battery driver support
    - BattEsimate.lua applet estimates SoC from voltage
    - Camera and Mount bindings improved
    - CAN input packet filtering reduces work required by Lua CAN drivers
    - DJI RS2/RS3 gimbal driver supports latest DJI firmware version (see mount-djirs2-driver.lua)
    - EFI drivers for DLA serial, InnoFlight Inject EFI driver
    - EFI bindings improved
    - Fence support
    - Generator drivers for Halo6000, Zhuhai SVFFI
    - GCS failsafe support
    - Hobbywing_DataLink driver (see Hobbywing_DataLink.lua)
    - is_landing, is_taking_off bindings
    - led_on_a_switch.lua sets LED brightness from RC switch
    - MAVLink sending and receiving support
    - Mission jump_to_landing_sequence binding
    - mount-poi.lua upgraded to applet, sends better feedback, can lock onto Location
    - Networking/Ethernet support
    - Proximity driver support
    - Rangefinder drivers can support signal quality
    - revert_param.lua applet for quickly reverting params during tuning
    - RockBlock.lua applet supports setting mode, fix for battery voltage reporting
    - Serial/UART reading performance improvement using readstring binding
    - sport_aerobatics.lua rudder control fixed
    - Thread priority can be set using SCR_THD_PRIORITY (useful for Lua drivers)
    - Wind alignment and head_wind speed bindings
13) Safety related enhancements and fixes
    - Arm/Disarmed GPIO may be disabled using BRD_OPTIONS
    - Arming check of compass vs world magnetic model to detect metal in ground (see ARMING_MAGTHRESH)
    - Arming check of GPIO pin interrupt storm
    - Arming check of Lua script CRC
    - Arming check of mission loaded from SD card
    - Arming check of Relay pin conflicts
    - Arming check of emergency stop skipped if emergency stop aux function configured
    - Arming failures reported more quickly when changing from success to failed
    - ARMING_OPTIONS allows supressing "Armed", "Disarmed" text messages
    - BRD_SAFETY_MASK extended to apply to CAN ESCs and servos
    - Buzzer noise for gyro calibration and arming checks passed
    - Dijkstras object avoidance supports "fast waypoints" (see AVOID_OPTIONS)
    - FENCE_OPTIONS supports union OR intersection of all polygon fences
    - FLTMODE_GCSBLOCK blocks GCS from changing vehicle to specified modes
    - GCS failsafe action to switch to Brake mode (see FS_GCS_ENABLE)
    - Main loop lockup recovery by forcing mutex release (only helps if caused by software bug)
    - Rally points supports altitude frame (AMSL, Relative or Terrain)
    - SERVO_RC_FS_MSK allows outputs using RC passthrough to move to SERVOx_TRIM on RC failsafe
    - TKOFF_RPM_MAX aborts takeoff if RPM is too high (for cases where a propeller has come off)
14) System Enhancements
    - CAN port can support a second CAN protocol on the same bus (2nd protocol must be 11 bit, see CAN_Dn_PROTOCOL2)
    - CAN-FD support (allows faster data transfer rates)
    - Crash dump info logged if main thread locksup (helps with root cause analysis)
    - Ethernet/Networking support for UDP and TCP server and client (see NET_ENABLED) and PPP (see SERIALx_PROTOCOL)
    - Firmware flashing from SD card
    - Linux board SBUS input decoding made consistent with ChibiOS
    - Linux boards support DroneCAN
    - Parameter defaults stored in @ROMFS/defaults.parm
    - SD Card formatting supported on all boards
    - Second USB endpoint defaults to MAVLink (instead of SLCAN) except on CubePilot boards
    - Serial over DroneCAN (see CAN_D1_UC_SER_EN) useful for configuring F9P DroneCAN GPSs using uCenter
15) Custom Build server include/exclude features extended to include
    - APJ Tools
    - Brake mode
    - Bootloader flashing
    - Button
    - Compass calibration
    - DroneCAN GPS
    - ExternalAHRS (e.g. MicroStrain, Vectornav)
    - Generator
    - Highmark Servo
    - Hobbywing ESCs
    - Kill IMU
    - Payload Place
    - Precision landing
    - Proximity sensor
    - RC Protocol
    - Relay
    - SBUS Output
    - ToneAlarm
    - Winch
16) Developer specific items
    - ChibiOS upgrade to 21.11
    - UAVCAN replaced with DroneCAN
    - AUTOPILOT_VERSION_REQUEST message deprecated (use REQUEST_MESSAGE instead)
    - PREFLIGHT_SET_SENSOR_OFFSETS support deprecated (was unused by all known ground stations)
    - MISSION_SET_CURRENT message deprecated (use DO_SET_MISSION_CURRENT command instead)
    - MISSION_CURRENT message sends num commands and stopped/paused/running/complete state
    - Python version requirement increased to 3.6.9
    - mavlink_parse.py shows all suported mavlink messages
    - COMMAND_INT messages can be used for nearly all commands (previously COMMAND_LONG)
17) Bug fixes:
    - 3DR Solo gimbal mavlink routing fixed
    - Airspeed health always checked before use (may not have been checked when using "affinity")
    - Auto mode fix for DO_CHANGE_SPEED commands placed immediately after TAKEOFF (previously were ignored)
    - Bootloop fixed if INS_GYRO_FILTER set too high
    - Button Internal Error caused by floating pin or slow device power-up fixed
    - CAN Compass order maintained even if compass powered up after autopilot
    - Compass device IDs only saved when calibrated to ensure external compasses appear as primary on new boards
    - Cut corners more by defaulting WPNAV_ACCEL_C to 2x WPNAV_ACCEL
    - Currawong ECU EFI does not send exhaust gas temperature
    - DJI RS2/RS3 gimbal reported angle fix
    - DO_SET_ROI, ROI_LOCATION, ROI_NONE bug fix that could lead to gimbal pointing at old target
    - Generator parameter init fix (defaults might not always have been loaded correctly)
    - GPS_TC_BLEND parameter removed (it was unused)
    - Guided mode protection against targets with NaN values
    - Guided mode yaw fix (vehicle might rotate too slowly)
    - Harmonic Notch gets protection against extremely low notch filter frequencies
    - IE 650/800 Generators report fuel remaining
    - INS calibration prevents unlikely case of two calibrations running at same time
    - LPS2XH Baro supported over I2C fixed
    - MatekH743 storage eeprom size fixed
    - MAVLink routing fix to avoid processing packet meant for another vehicle
    - Mount properly enforces user defined angle limits
    - MPU6500 IMU filter corrected to 4k
    - NMEA output time and altitude fixed
    - OSD gets labels for all supported serial protocols
    - OSD RF panel format fixed
    - RobotisServo initialisation fix
    - RPM accuracy and time wrap handling improved
    - Sagetech ADSB MXS altitude fix (needs amsl, was sending alt-above-terrain)
    - SageTechMXS ADSB climb rate direction fixed
    - SBUS out exactly matches SBUS in decoding
    - Serial port RTS pins default to pulldown (SiK radios could getting stuck in bootloader mode)
    - SERIALx_ parameters removed for ports that can't actually be used
    - Servo gimbal attitude reporting fix
    - Servo output fix when using scaled RC passthrough (e.g. SERVOx_FUNCTION = RCinXScaled)
    - Siyi continuous zoom stutter fixed
    - Siyi gimbal upside-down mode fixed (avoid bobbing if upside-down)
    - SmartRTL premature "buffer full" failure fixed
    - ST24 RC protocol fixed
    - STM32L496 CAN2 init fix (AP_Periph only?)
    - Tricopter, SingleCopter, CoaxCopter fins centered if using BLHeli/DShot
    - VFR_HUD climb rate reports best estimate during high vibration events (previously it would stop updating)
    - Visual Odometry healthy check fix in case of out-of-memory
    - VTX_MAX_POWER restored (allows setting radio's power)
    - Yaw limit calculations fixed
------------------------------------------------------------------
Copter 4.4.4 19-Dec-2023 / 4.4.4-beta1 05-Dec-2023
Changes from 4.4.3
1) Autopilot related enhancement and fixes
    - CubeOrange Sim-on-hardware compilation fix
    - RADIX2HD supports external I2C compasses
    - SpeedyBeeF405v4 support
2) Bug fixes
    - DroneCAN battery monitor with cell monitor SoC reporting fix
    - NTF_LED_TYPES parameter description fixed (was missing IS31FL3195)
    - ProfiLED output fixed in both Notify and Scripting
    - Scripting bug that could cause crash if parameters were added in flight
    - STAT_BOOTCNT param fix (was not updating in some cases)
    - Takeoff RPM check fixed where motors are attached to AUX channels
    - don't query hobbywing DroneCAN ESC IDs while armed
------------------------------------------------------------------
Copter 4.4.3 14-Nov-2023
Changes from 4.4.3-beta1
1) AP_GPS: correct uBlox M10 configuration on low flash boards
------------------------------------------------------------------
Copter 4.4.3-beta1 07-Nov-2023
Changes from 4.4.2
1) Autopilot related enhancements and fixes
    - BETAFTP-F405 board configuration fixes
    - CubeOrangePlus-BG edition ICM45486 IMU setup fixed
    - YJUAV_A6SE_H743 support
2) Minor enhancements
    - GPS_DRV_OPTION allows using ellipsoid height in more GPS drivers
    - Lua script support for fully populating ESC telemetry data
3) Bug fixes
   - AK09916 compass being non-responsive fixed
   - IxM42xxx IMUs "stuck" gyros fixed
   - MAVLink response fixed when no airspeed sensor during preflight calibration
   - Notch filtering protection when using uninitialised RPM source in ESC telemetry
   - SIYI gimbal driver parsing bug fixed (was causing lost packets)
------------------------------------------------------------------
Copter 4.4.2 22-Oct-2023 / Copter 4.4.2-beta1 13-Oct-2023
Changes from 4.4.1
1) Autopilot related enhancements and fixes
    - BETAFPV-F405 support
    - MambaF405v2 battery and serial setup corrected
    - mRo Control Zero OEM H7 bdshot support
    - SpeedyBee-F405-Wing gets VTX power control
    - SpeedyBee-F405-Mini support
    - T-Motor H743 Mini support
2) EKF3 supports baroless boards
3) GPS-for-yaw allows base GPS to update at only 3Hz
4) INA battery monitor supports config of shunt resistor used (see BATTx_SHUNT)
5) Log VER message includes vehicle type
6) OpenDroneId option to auto-store IDs in persistent flash
7) RC SBUS protection against invalid data in first 4 channels
8) Bug fixes
    - BMI088 IMU error value handling fixed to avoid occasional negative spike
    - Dev environment CI autotest stability improvements
    - OSD correct DisplayPort BF MSP symbols
    - OSD option to correct direction arrows for BF font set
    - Sensor status reporting to GCS fixed for baroless boards
------------------------------------------------------------------
Copter 4.4.1 26-Sep-2023 / 4.4.1-beta2 14-Sep-2023
Changes from 4.4.1-beta1
1) Autopilot related enhancements
    - H750 external flash optimisations for to lower CPU load
    - MambaF405Mini fixes to match manufacturer's recommended wiring
    - RADIX2 HD support
    - YJUAV_A6SE support
2) Bug fixes
    - Airbotf4 features minimised to build for 4.4
    - ChibiOS clock fix for 480Mhz H7 boards (affected FDCAN)
    - RPI hardware version check fix
------------------------------------------------------------------
Copter 4.4.1-beta1 05-Sep-2023
Changes from 4.4.0
1) Autopilot related fixes and enhancements
    - KakuteH7-wing get 8 bit directional dshot channel support
    - Luminousbee5 boards defaults updated
    - Navigator autopilot GPIOs fix (PWM output was broken)
    - Pixhawk6C Serial RTS lines pulled low on startup
    - QiotekZealotF427 and QiotekZealotH743 battery monitor default fixed
    - SDMODELH7V1 support
2) Driver enhancements
    - DroneCAN battery monitors allow reset of battery SoC
    - Himark DroneCAN servo support
    - Hobbywing DroneCAN ESC support
3) EKF3 high vibration handling improved with EK3_GLITCH_RADIUS option
4) Custom build server gets mission storage on SDCard selection
5) SITL default parameter handling bug fix
------------------------------------------------------------------
Copter 4.4.0 18-Aug-2023 / 4.4.0-beta5 12-Aug-2023
Changes from 4.4.0-beta4
1) Autopilots specific changes
    - SIYI N7 support
2) Bug fixes
    - DroneCAN airspeed sensor fix to handle missing packets
    - DroneCAN GPS RTK injection fix
    - Notch filter gyro glitch caused by race condition fixed
------------------------------------------------------------------
Copter 4.4.0-beta4 27-July-2023
Changes from 4.4.0-beta3
1) Autopilots specific changes
    - Diatone-Mamba-MK4-H743v2 uses SPL06 baro (was DPS280)
    - DMA for I2C disabled on F7 and H7 boards
    - Foxeer H743v1 default serial protocol config fixes
    - HeeWing-F405 and F405v2 support
    - iFlight BlitzF7 support
2) Scripts may take action based on VTOL motor loss
3) Bug fixes
    - BLHeli returns battery status requested via MSP (avoids hang when using esc-configurator)
    - CRSFv3 rescans at baudrates to avoid RX loss
    - EK3_ABIAS_P_NSE param range fix
    - Scripting restart memory corruption bug fixed
    - Siyi A8/ZR10 driver fix to avoid crash if serial port not configured
------------------------------------------------------------------
Copter 4.4.0-beta3 03-July-2023
Changes from 4.4.0-beta2
1) Autopilots specific changes
    - Holybro KakuteH7-Wing support
    - JFB100 external watchdog GPIO support added
    - Pixhawk1-bdshot support
    - Pixhawk6X-bdshot support
    - SpeedyBeeF4 loses bdshot support
2) Device drivers
    - added LP5562 I2C LED driver
    - added IS31FL3195 LED driver
3) TradHeli gets minor fix to RSC servo output range
4) Camera and Gimbal related changes
    - DO_SET_ROI_NONE command support added
5) Applet changes
    - added QUIK_MAX_REDUCE parameter to VTOL quicktune lua applet
6) Bug fixes
    - ADSB sensor loss of transceiver message less spammy 
    - AutoTune Yaw rate max fixed
    - EKF vertical velocity reset fixed on loss of GPS
    - GPS pre-arm failure message clarified
    - SERVOx_PROTOCOL "SToRM32 Gimbal Serial" value renamed to "Gimbal" because also used by Siyi
    - SERIALx_OPTION "Swap" renamed to "SwapTXRX" for clarity
    - SBF GPS ellipsoid height fixed
    - Ublox M10S GPS auto configuration fixed
    - ZigZag mode user takeoff fixed (users could not takeoff in ZigZag mode previously)
------------------------------------------------------------------
Copter 4.4.0-beta2 05-Jun-2023
Changes from 4.4.0-beta1
1) Autopilots specific changes
    - FlywooF745 update to motor pin output mapping and baro
    - FoxeerH743 support
    - JFB100 support
    - Mamba-F405v2 supports ICM42688
    - Matek-F405-TE/VTOL support
    - Matek-H743 IMU SPI slowed to 1Mhz to avoid init issues
    - SpeedyBee-405-Wing support
2) Copter specfic fixes and enhancements
    - RTL speed fix so changes to WPNAV_SPEED have no effect if RTL_SPEED is non-zero
    - RTL mode accepts do-change-speed commands from GCS
3) AHRS/EKF related fixes and Enhancements
    - EKF allocation failure handled to avoid watchdog
    - EKF3 accel bias calculation fix and tuning for greater robustness
    - Airspeed sensor remains enabled during dead-reckoning (few copters have airspeed sensors)
    - Wind speed estimates updates reduced while dead-reckoning
4) Other Enhancements
    - Attitude control slew limits always calculated (helps tuning reporting and analysis)
    - INA228 and INA238 I2C battery monitor support
    - LOG_DISARMED=3 logs while disarmed but discards log if never eventually armed
    - LOG_DARM_RATEMAX reduces logging while disarmed
    - Serial LEDs threading enhancement to support longer lengths without dshot interference
4) Bug fixes
    - Analog battery monitor2 current parameter default fixed
    - AutoTune fix for loading Yaw Rate D gains
    - BRD_SAFETYOPTION parameter documentation fix (ActiveForSafetyEnable and Disable were reversed)
    - Compassmot fix to protect against bad gyro biases from GSF yaw
    - ICE engine fix for starting after reaching a specified altitude
    - LED thread locking fix to avoid watchdog
    - Logging rotation on disarm disabled if Replay logging active (avoids gaps in logs)
    - RC input on IOMCU bug fix (RC might not be regained if lost)
    - Serial passthrough fixed
5) Custom build server fix to which features are included/excluded
------------------------------------------------------------------
Copter 4.4.0-beta1 19-Apr-2023
Changes from 4.3.6
1) New autopilots supported
    - ESP32
    - Flywoo Goku F405S AIO
    - Foxeer H743v1
    - MambaF405-2022B
    - PixPilot-V3
    - PixSurveyA2
    - rFCU H743
    - ThePeach K1/R1
2) Autopilot specific changes
    - Bi-Directional DShot support for CubeOrangePlus-bdshot, CUAVNora+, MatekF405TE/VTOL-bdshot, MatekL431, Pixhawk6C-bdshot, QioTekZealotH743-bdshot
    - Bi-Directional DShot up to 8 channels on MatekH743
    - BlueRobotics Navigator supports baro on I2C bus 6
    - BMP280 baro only for BeastF7, KakuteF4, KakuteF7Mini, MambaF405, MatekF405, Omnibusf4 to reduce code size (aka "flash")
    - CSRF and Hott telemetry disabled by default on some low power boards (aka "minimised boards")
    - Foxeer Reaper F745 supports external compasses
    - OmnibusF4 support for BMI270 IMU
    - OmnibusF7V2-bdshot support removed
    - KakuteF7 regains displayport, frees up DMA from unused serial port
    - KakuteH7v2 gets second battery sensor
    - MambaH743v4 supports VTX
    - MatekF405-Wing supports InvensenseV3 IMUs
    - PixPilot-V6 heater enabled
    - Raspberry 64OS startup crash fixed
    - ReaperF745AIO serial protocol defaults fixed
    - SkystarsH7HD (non-bdshot) removed as users should always use -bdshot version
    - Skyviper loses many unnecessary features to save flash
    - UBlox GPS only for AtomRCF405NAVI, BeastF7, MatekF405, Omnibusf4 to reduce code size (aka "flash")
    - VRBrain-v52 and VRCore-v10 features reduced to save flash
3) Driver enhancements
    - ARK RTK GPS support
    - BMI088 IMU filtering and timing improved, ignores bad data
    - CRSF OSD may display disarmed state after flight mode (enabled using RC_OPTIONS)
    - Daiwa winch baud rate obeys SERIALx_BAUD parameter
    - EFI supports fuel pressure and ignition voltage reporting and battery failsafe
    - ICM45686 IMU support
    - ICM20602 uses fast reset instead of full reset on bad temperature sample (avoids occasional very high offset)
    - ICM45686 supports fast sampling
    - MAX31865 temp sensor support
    - MB85RS256TY-32k, PB85RS128C and PB85RS2MC FRAM support
    - MMC3416 compass orientation fix
    - MPPT battery monitor reliability improvements, enable/disable aux function and less spammy
    - Multiple USD-D1-CAN radar support
    - NMEA output rate configurable (see NMEA_RATE_MS) 
    - NMEA output supports PASHR message (see NMEA_MSG_EN)
    - OSD supports average resting cell voltage (see OSD_ACRVOLT_xxx params)
    - Rockblock satellite modem support
    - Serial baud support for 2Mbps (only some hardware supports this speed)
    - SF45b lidar filtering reduced (allows detecting smaller obstacles
    - SmartAudio 2.0 learns all VTX power levels)
    - UAVCAN ESCs report error count using ESC Telemetry
    - Unicore GPS (e.g. UM982) support
    - VectorNav 100 external AHRS support
    - 5 IMUs supported
4) EKF related enhancements
    - Baro compensation using wind estimates works when climbing or descending (see BAROx_WCF_UP/DN)
    - External AHRS support for enabling only some sensors (e.g. IMU, Baro, Compass) see EAHRS_SENSORS
    - Magnetic field tables updated
    - Non-compass initial yaw alignment uses GPS course over GSF (mostly affects Planes and Rover)
5) Control and navigation enhancements
    - AutoTune of attitude control yaw D gain (set AUTOTUNE_AXES=8)
    - Circle moode and Loiter Turns command supports counter-clockwise rotation (set CIRCLE_RATE to negative number)
    - DO_SET_ROI_NONE command turns off ROI
    - JUMP_TAG mission item support
    - Missions can be stored on SD card (see BRD_SD_MISSION)
    - NAV_SCRIPT_TIME command accepts floating point arguments
    - Pause/Resume returns success if mission is already paused or resumed
    - Payload Place enhancements
        - Descent speed is configurable (see PLDP_SPEED_DN)
        - Manual release supported (detects pilot release of gripper)
        - Post release delay is configurable (see PLDP_DELAY)
        - Range finder range used to protect against premature release (see PLDP_RNG_MIN)
        - Touchdown detection threshold is configurable (see PLDP_THRESH)
    - Position controller angle max adjusted inflight with CH6 Tuning knob (set TUNE=59)
    - Surface tracking time constant allows tuning response (see SURFTRAK_TC)
    - Throttle-Gain boost increases attitude control gains when throttle high (see ATC_THR_G_BOOST)
    - Waypoint navigation cornering acceleration is configurable (see WPNAV_ACCEL_C)
    - WeatherVane into the wind in Auto and Guided modes (see WVANE_ENABLE)
6) TradHeli specific enhancements
    - Manual autorotation support
    - Improved collect to yaw compensation
7) Filtering enhancements
    - FFT notch can be run based on filtered data
    - Warn of motor noise at RPM frequency using FFT
    - In-flight FFT can better track low frequency noise
    - In-flight FFT logging improved
    - IMU data can be read and replayed for FFT analysis
8) Camera and gimbal enhancements
    - BMMCC support included in Servo driver
    - DJI RS2/RS3-Pro gimbal support
    - Dual camera support (see CAM2_TYPE)
    - Gimbal/Mount2 can be moved to retracted or neutral position
    - Gremsy ZIO support
    - IMAGE_START_CAPTURE, SET_CAMERA_ZOOM/FOCUS, VIDEO_START/STOP_CAPTURE command support
    - Paramters renamed and rescaled
        - CAM_TRIGG_TYPE renamed to CAM1_TYPE and options have changed
        - CAM_DURATION renamed to CAM1_DURATION and scaled in seconds
        - CAM_FEEDBACK_PIN/POL renamed to CAM1_FEEBAK_PIN/POL
        - CAM_MIN_INTERVAL renamed to CAM1_INTRVAL_MIN and scaled in seconds
        - CAM_TRIGG_DIST renamed to CAMx_TRIGG_DIST and accepts fractional values
    - RunCam2 4k support
    - ViewPro camera gimbal support
9) Logging changes
    - BARD msg includes 3-axis dynamic pressure useful for baro compensation of wind estimate
    - MCU log msg includes main CPU temp and voltage (was part of POWR message)
    - RCOut banner message always included in Logs
    - SCR message includes memory usage of all running scripts
    - CANS message includes CAN bus tx/rx statistics
    - OFCA (optical flow calibration log message) units added
    - Home location not logged to CMD message
    - MOTB message includes throttle output
10) Scripting enhancements
    - Copter deadreckoning upgraded to applet
    - EFI Skypower driver gets improved telem messages and bug fixes
    - Generator throttle control example added
    - Heap max increased by allowing heap to be split across multiple underlying OS heaps
    - Hexsoon LEDs applet
    - Logging from scripts supports more formats
    - Parameters can be removed or reordered
    - Parameter description support (scripts must be in AP's applet or driver directory)
    - Rangefinder driver support
    - Runcam_on_arm applet starts recording when vehicle is armed
    - Safety switch, E-Stop and motor interlock support
    - Scripts can restart all scripts
    - Script_Controller applet supports inflight switching of active scripts
11) Custom build server enhancements
    - AIS support for displaying nearby boats can be included
    - Battery, Camera and Compass drivers can be included/excluded
    - EKF3 wind estimation can be included/excluded
    - PCA9685, ToshibaLED, PLAY_TUNE notify drivers can be included/excluded
    - Preclanding can be included/excluded
    - RichenPower generator can be included/excluded
    - RC SRXL protocol can be excluded
    - SIRF GPSs can be included/excluded
12) Safety related enhancements and fixes
    - Arming check for high throttle skipped when arming in Auto mode
    - Arming check for servo outputs skipped when SERVOx_FUNCTION is scripting
    - Arming check fix if both "All" and other bitmasks are selected (previously only ran the other checks)
    - "EK3 sources require RangeFinder" pre-arm check fix when user only sets up 2nd rangefinder (e.g. 1st is disabled)
    - Pre-arm check that low and critical battery failsafe thresholds are different
    - Pre-arm message fixed if 2nd EKF core unhealthy
    - Pre-arm check if reboot required to enabled IMU batch sampling (used for vibe analysis)
    - RC failsafe (aka throttle failsafe) option to change to Brake mode
    - RC failsafe timeout configurable (see RC_FS_TIMEOUT)
    - Takeoff check of motor RPM (see TKOFF_RPM_MIN)
    - Turtle mode warns user to raise throttle to arm
13) Minor enhancements
    - Boot time reduced by improving parameter conversion efficiency
    - BRD_SAFETYENABLE parameter renamed to BRD_SAFETY_DEFLT
    - Compass calibration auxiliary switch function (set RCx_OPTION=171)
    - Disable IMU3 auxiliary switch function (set RCx_OPTION=110)
    - Frame type sent to GCS defaults to multicopter to ease first time setup
    - Rangefinder and FS_OPTIONS param conversion code reduced (affects when upgrading from 3.6 or earlier)
    - MAVFTP supports file renaming
    - MAVLink in-progress reply to some requests for calibration from GCS
14) Bug fixes:
    - ADSB telemetry and callsign fixes
    - Battery pct reported to GCS limited to 0% to 100% range
    - Bi-directional DShot fix on H7 boards after system time wrap (more complete fix than in 4.3.6)
    - DisplayPort OSD screen reliability improvement on heavily loaded OSDs especially F4 boards
    - DisplayPort OSD artificial horizon better matches actual horizon
    - EFI Serial MS bug fix to avoid possible infinite loop
    - EKF3 Replay fix when COMPASS_LEARN=3
    - ESC Telemetry external temp reporting fix
    - Fence upload works even if Auto mode is excluded from firmware
    - FMT messages logged even when Fence is exncluded from firmware (e.g. unselected when using custom build server)
    - Guided mode slow yaw fix
    - Hardfault avoided if user changes INS_LOG_BAT_CNT while batch sampling running
    - ICM20649 temp sensor tolerate increased to avoid unnecessary FIFO reset
    - IMU detection bug fix to avoid duplicates
    - IMU temp cal fix when using auxiliary IMU
    - Message Interval fix for restoring default rate https://github.com/ArduPilot/ardupilot/pull/21947
    - RADIO_STATUS messages slow-down feature never completely stops messages from being sent
    - SERVOx_TRIM value output momentarily if SERVOx_FUNCTION is changed from Disabled to RCPassThru, RCIN1, etc.  Avoids momentary divide-by-zero
    - Scripting file system open fix
    - Scripting PWM source deletion crash fix
    - MAVFTP fix for low baudrates (4800 baud and lower)
    - ModalAI VOXL reset handling fix
    - MPU6500 IMU fast sampling rate to 4k (was 1K)
    - NMEA GPGGA output fixed for GPS quality, num sats and hdop
    - Position control reset avoided even with very uneven main loop rate due to high CPU load
    - SingleCopter and CoaxCopter fix to fin centering when using DShot
    - SystemID mode fix to write PID log messages
    - Terrain offset increased from 15m to 30m (see TERRAIN_OFS_MAX)to reduce chance of "clamping"
    - Throttle notch FFT tuning param fix
    - VTX protects against pitmode changes when not enabled or vehicle disarmed
15) Developer specific items
    - DroneCAN replaces UAVCAN
    - FlighAxis simulator rangefinder fixed
    - Scripts in applet and drivers directory checked using linter
    - Simulator supports main loop timing jitter (see SIM_TIME_JITTER)
    - Simulink model and init scripts
    - SITL on hardware support (useful to demo servos moving in response to simulated flight)
    - SITL parameter definitions added (some, not all)
    - Webots 2023a simulator support
    - XPlane support for wider range of aircraft
------------------------------------------------------------------
Copter 4.3.8 24-Aug-2023 / 4.3.8-beta1 12-Aug-2023
Changes from 4.3.7
1) Bug fixes
    - DroneCAN GPS RTK injection fix
    - INAxxx battery monitors allow for battery reset remaining
    - Notch filter gyro glitch caused by race condition fixed
    - Scripting restart memory corruption bug fixed
------------------------------------------------------------------
Copter 4.3.7 31-May-2023 / 4.3.7-beta1 24-May-2023
Changes from 4.3.6
1) Bug fixes
    a) EKF3 accel bias calculations bug fix
    b) EKF3 accel bias process noise adjusted for greater robustness
    c) GSF yaw numerical stability fix caused by compassmot
    d) INS batch sampler fix to avoid watchdog if INS_LOG_BAT_CNT changed without rebooting
    e) Memory corruption bug in the STM32H757 (very rare)
    f) RC input on IOMCU bug fix (RC might not be regained if lost)
------------------------------------------------------------------
Copter 4.3.6 05-Apr-2023 / 4.3.6-beta1, 4.3.6-beta2 27-Mar-2023
Changes from 4.3.5
1) Bi-directional DShot fix for possible motor stop approx 72min after startup 
------------------------------------------------------------------
Copter Copter 4.3.5 14-Mar-2023 / 4.3.5-rc1 01-Mar-2023
Changes from 4.3.4
1) Bug fixes
    a) GPS unconfigured error fix for non-M10 uBlox GPS
    b) Gremsy gimbal fix when attached to autopilot's serial3 (or higher)
    c) Landing detector fix with large AHRS_TRIM values (>0.1)
    d) MambaF405 2022 gets VTX power on support
    e) MCU voltage enabled on H757 CPUs (including CubeOrangePlus)
    f) PiccoloCAN fix for ESC voltage and current scaling 
    g) Servo gimbal mount yaw handling fix (only affects 3-axis servo gimbals)
------------------------------------------------------------------
Copter 4.3.4 01-Mar-2023
Changes from 4.3.4-rc1
1) Lua script PWMSource feature disabled (will be back in 4.4.x)
------------------------------------------------------------------
Copter 4.3.4-rc1 14-Feb-2023
Changes from 4.3.3
1) AutoPilot specific enhancements
    a) CubeOrangePlusBG support
    b) Foxeer ReaperF745 supports external compass
    c) MambaH743v4 supports VTX power
2) Precision landing option to resume tracking target after pilot repositioning
3) Bug fixes
    a) Arming check fix for terrain following with rangefinder (failed if terrain db was disabled)
    b) Arming check fix if BARO_FIELD_ELEV set
    c) Compass calibration diagonals set to 1,1,1 if incorrectly set to 0,0,0
    d) FFT notch tune feature disabled (will be re-released in 4.4)
    e) Gimbal's yaw feed-forward set to zero when landed (affects Gremsy gimbals)
    f) IOMCU double reset and safety disable fix
    g) Logging fix for duplicate format messages
    h) OpenDroneId sets emergency status on crash or chute deploy
    i) Peripheral firmware updates using MAVCAN fixed
    j) RC protocol cannot be changed once detected on boards with IOMCU
    k) Surface tracking uses filtered and corrected rangefinder values
------------------------------------------------------------------
Copter 4.3.3 20-Jan-2023
Changes from 4.3.3-rc1
1) Bug fixes
    a) MAVFTP fix to terminate session error (could cause FTP failures)
    b) IMU fast fifo reset log message max frequency reduced
------------------------------------------------------------------
Copter 4.3.3-rc1 09-Jan-2023
Changes from 4.3.2
1) Autopilot related changes
    a) AIRLink LTE module enable pin and HEAT_ params added
    b) CUAV Nora/Nora+ bdshot firmware (allows Bi-directional DShot)
    c) CubeOrange, CubeYellow gets fast reset of ICM20602
    d) MambaH743v2 with dual ICM42688 supported
    e) PixPilot-V6
2) Attitude and Navigation controllers use real-time dt (better handles variable or slow main loop)
3) MAVFTP speed improvement including faster param download
4) Bug fixes
    a) Analog rangefinder GPIO pin arming check fixed
    b) Arming check of AHRS/EKF vs GPS location disabled if GPS disabled
    c) CRSF gets RC_OPTIONS for ELRS baudrate to avoid RC failsafes
    d) Null pointer checks avoid watchdog when out of memory
    e) Position Controller limit handling improved to avoid overshooting and hard landings
    f) Position Controller internal error after 70min of flight fixed
    g) PSC_ANGLE_MAX param reduction causing WPNAV_ACCEL to be set too low fixed
    h) Servo gimbal yaw jump to opposite side fixed
    i) Siyi A8 gimbal driver's record video feature fixed
    j) SToRM32 serial gimbal driver actual angle reporting fixed (pitch and yaw angle signs were reversed)
    k) Takeoff in Auto, Guided fixed when target altitude is current altitude
    l) Takeoff in Auto handles baro drift before takeoff
    m) Takeoff twitch due to velocity integrator init bug fixed
------------------------------------------------------------------
Copter 4.3.2 23-Dec-2022
Changes from 4.3.2-rc1
1) Reverted arming check that main loop is running at configured speed (e.g. SCHED_LOOP_RATE)
------------------------------------------------------------------
Copter 4.3.2-rc1 10-Dec-2022
Changes from 4.3.1
1) Arming check that main loop is running at configured speed (e.g. SCHED_LOOP_RATE)
2) uBlox M10 support
3) Autopilot specific changes
    a) CubeOrange defaults to using 2nd IMU as primary
    b) SIRF and SBP GPS disabled on BeastF7v2, MatekF405-STD, MAtekF405-Wing, omnibusf4pro
4) Bug fixes
    a) AutoTune gains loaded correctly during pilot testing
    b) Camera driver's CAM_MIN_INTERVAL fixed if pilot manually triggers extra picture
    c) EKF3 fix when using EK3_RNG_USE_HGT/SPD params and rangefinder provides bad readings
    d) Main loop slowdown after arming fixed (parameter logging was causing delays)
    e) Main loop's fast tasks always run (caused twitches in Loiter on heavily loaded CPUs)
    f) MAVLink commands received on private channels checked for valid target sysid
    g) ModalAI cameras support fixed (ODOMETRY message frame was consumed incorrectly)
    h) Param reset after firmware load fixed on these boards
        - BeastF7v2
        - CubeYellow-bdshot
        - f405-MatekAirspeed
        - FlywooF745Nano
        - KakuteF4Mini
        - KakuteF7-bdshot
        - MatekF405-bdshot
        - MatekF405-STD
        - MatekF405-Wing-bdshot
        - MatekF765-SE
        - MatekF765-Wing-bdshot
    i) Siyi A8 gimbal support fixed
    j) Windows builds move to compiling only 64-bit executables
------------------------------------------------------------------
Copter 4.3.1 05-Dec-2022 / 4.3.1-rc1 17-Nov-2022
Changes from 4.3.0
1) Autopilot specific enhancements
   a) ARKV6X support
   b) MatekH743 supports 8 bi-directional dshot channels
   c) Pixhawk boards support MS5607 baros
   d) SpeedbyBee F405v3 support
2) DroneCAN Airspeed sensor support including hygrometer (aka water vapour) readings
3) EFI support (electronic fuel injection engines)
4) Pre-arm warning if multiple UARTs with SERIALx_PROTOCOL = RCIN
5) Siyi gimbal support
6) Bug fixes
    a) Arm check warning loses duplicate "AHRS" prefix
    b) AtomRCF405NAVI bootloader file name fixed
    c) BRD_SAFETY_MASK fixed on boards with both FMU safety switch and IOMCU
    d) Compass calibration continues even if a single compass's cal fails
    e) Gremsy gimbal driver sends autopilot info at lower rate to save bandwidth
    f) Invensense 42605 and 42609 IMUs use anti-aliasing filter and notch filter
    g) Mode change to AUTOTUNE message shortened
    h) OSD stats screen fix
    i) RC input on serial port uses first UART with SERIALx_PROTOCOL = 23 (was using last)
    j) RunCam caching fix with enablement and setup on 3-pos switch
    k) RTK CAN GPS fix when GPSs conneted to separate CAN ports on autopilot
    l) SkyViper GPS fix
    m) Turtle mode safety fixes (e.g. can only enter Tutle mode with at zero throttle)
------------------------------------------------------------------
Copter 4.3.0 31-Oct-2022 / 4.3.0-beta4 24-Oct-2022
Changes from 4.3.0-beta3
1) Scripting supports implementing AUX functions
2) Bug fixes
    a) BMI085 accel scaling fixed
    b) Build with gcc 11.3 fixed (developer only)
    c) EKF3 alt discrepancy if GPS or baro alt changed soon after startup fixed
    d) Harmonic Notch and ESC telem fix when motor outputs are non-contiguous
    e) NMEA GPS's KSXT message parsing fixed (affected position accuracy)
    f) Scripting random number generator fix
------------------------------------------------------------------
Copter 4.3.0-beta3 14-Oct-2022
Changes from 4.3.0-beta2
1) Pixhawk1-1M, fmuv2, fmuv3 display warning if firmware mismatches board's flash size (1M and 2M)
2) Scripting support for multi-byte i2c reads
3) Bug fixes
    a) Airspeed CAN sensor ordering fixed (ordering could change if using multiple airspeed sensors)
    b) BRD_SAFETY_MASK fix for enabling outputs when safety is on
    c) Defaults.parm file processing fixed when a line has >100 characters and/or no new line (developer only)
    d) NMEA serial output precision fixed (was only accurate to 1m, now accurate to 1cm)
------------------------------------------------------------------
Copter 4.3.0-beta2 4-Oct-2022
Changes from 4.3.0-beta1
1) Autopilot specific fixes and enhancements
    a) AIRLink autopilot supports UART2
    b) CUAV V6X supports CAN battery monitor by default
    c) MatekF405-CAN board uses less memory to fix compass calibration issues
    d) Pixhawk1-1M only supports uBlox and NMEA GPSs to save flash space
    e) SkystarsH7HD-bdshot (allows Bi-directional DShot)
    f) SkystarsH7HD supports VTX power by default
2) EFI support
    a) Currawong ECU support (added as Electronic Fuel Injection driver)
    b) Scripting support for EFI drivers (allows writing EFI drivers in Lua)
    c) SkyPower and HFE CAN EFI drivers (via scripting)
3) Safety features
    a) Arming check that SPIN_MIN less than 0.3 and greater than SPIN_ARM
4) Minor enhancements
    a) Autopilot board names max length increased to 23 characters (was 13)
    b) CAN actuators can report PWM equivalent values (eases debugging)
    c) Log download speed improved for boards with "block" backends
    d) Notch filter slew limit reduces chance of notch freq moving incorrectly 
    e) SLCAN disabled when vehicle is armed to reduce CPU load
5) Bug fixes
    a) DO_JUMP mission command fixed if active command changed before changing to Auto mode
    b) EKF3 altitude error fix when using dual GPSs and affinity enabled
    c) FFT indexing bug fixed
    d) Gimbal mount fix to default mode (see MNTx_DEFLT_MODE parameter)
    e) MSP fix to report arm status to DJI FPV goggles
    f) Notch fix for non-throttle notch (was being incorrectly disabled)
    g) OSD fixes for params, font and resolution
    h) RPM reporting from harmonic notch fixed
    i) "Sending unknown message (50)" warning removed
    j) SBF/GSOF/NOVA GPS auto detction of baud rate fixed
    k) VideoTX fixes for buffer overruns and Tramp video transmitter support
------------------------------------------------------------------
Copter 4.3.0-beta1 14-Sep-2022
Changes from 4.2.3
1) New autopilot support
    a) AtomRCF405
    b) CubeOrange-SimOnHardWare
    c) DevEBoxH7v2
    d) KakuteH7Mini-Nand
    e) KakuteH7v2
    f) Mamba F405 Mk4
    g) SkystarsH7HD
    h) bi-directional dshot (aka "bdshot") versions for CubeOrange, CubeYellow, KakuteF7, KakuteH7, MatekF405-Wing, Matek F765, PH4-mini, Pixhawk-1M
2) EKF enhancements and fixes
    a) EK3_GPS_VACC_MAX threshold to control when GPS altitude is used as alt source
    b) EKF ring buffer fix for very slow sensor updates (those that update once every few seconds)
    c) EKF3 source set change captured in Replay logs
3) Gimbal enhancements
    a) Angle limit params renamed and scaled to degrees (e.g. MNT1_ROLL_MIN, MNT1_PITCH_MIN, etc)
    b) BrushlessPWM driver (set MNT1_TYPE = 7) is unstabilized Servo driver
    c) Dual mount support (see MNT1_, MNT2 params)
    d) Gremsy driver added (set MNT1_TYPE = 6)
    e) MAVLink gimbalv2 support including sending GIMBAL_DEVICE_STATUS_UPDATE (replaces MOUNT_STATUS message)
    f) "Mount Lock" auxiliary switch supports follow and lock modes in RC targetting (aka earth-frame and body-frame)    
    g) RC channels to control gimbal set using RCx_OPTION = 212 (Roll), 213 (Pitch) or 214 (Yaw)
    h) RC targetting rotation rate in deg/sec (see MNT1_RC_RATE which replaces MNT_JSTICK_SPD)
    i) Yaw can be disabled on 3-axis gimbals (set MNTx_YAW_MIN = MNTx_YAW_MAX)
4) Navigation and Flight mode enhancements
    a) Auto mode ATTITUDE_TIME command allows specifying lean angle for specified number of seconds (GPS not required)
    b) Auto mode support of DO_GIMBAL_MANAGER_PITCHYAW command
    c) Auto mode LOITER_TURNS command max radius increased to 2.5km
    d) AutoTune allows higher ANGLE_P gains
    e) Guided mode support DO_CHANGE_SPEED commands
    f) Manual modes throttle mix reduced (improves landing)
    g) Payload touchdown detection reliability improved    
    h) Takeoff detection improved to reduce chance of flip before takeoff if GPS moves
    i) TKOFF_SLEW_TIME allows slower takeoffs in Auto, Guided, etc
5) Notch filter enhancements
    a) Attitude and filter logging at main loop rate
    b) Batch sampler logging both pre and post-filter
    c) FFT frame averaging
    d) In-flight throttle notch parameter learning using averaged FFTs
    e) Triple harmonic notch 
5) RemoteId and SecureBoot enhancements
    a) Remote update of secure boot's public keys (also allows remote unlocking of bootloader)
6) Safety enhancements
    a) Arming checks of FRAME_CLASS/TYPE made mandatory (even if ARMING_CHECK=0)
    b) crash_dump.bin file saved to SD Card on startup (includes details re cause of software failures)
    c) Dead-reckoning for 30sec on loss of GPS (requires wind estimation be enabled)
    d) Dead-reckoning Lua script (On loss of GPS flies towards home for specified number of seconds)
    e) Disabling Fence clears any active breaches (e.g. FENCE_TYPE = 0 will clear breaches)
    f) "GPS Glitch" message clarified to "GPS Glitch or Compass error"
    g) Pre-arm check that configured AHRS is being used (e.g. checks AHRS_EKF_TYPE not changed since boot)
    h) Pre-arm check that gimbals are healthy (currently only for Gremsy gimbals, others in future release)
    i) Pre-arm check that all motors are setup
    j) Pre-arm check that scripts are running
    k) Pre-arm check that terrain data loaded if RTL_ALT_TYPE set to Terrain
    l) Pre-arm messages are correctly prefixed with "PreArm:" (instead of "Arm:")
    m) RC auxiliary switch option for Arm / Emergency Stop
    n) RC failsafe made pre-arm check (previously only triggered at arming)
    o) RC failsafe option (see FS_OPTIONS) to continue in Guided obeyed even if GCS failsafe disabled
    p) TKOFF_RPM_MIN checks all motors spinning before takeoff
    q) Vibration compensation disabled in manual modes
7) Scripting enhancements
    a) CAN2 port bindings to allow scripts to communicate on 2nd CAN bus
    b) ESC RPM bindings to allow scripts to report engine RPM
    c) Gimbal bingings to allow scripts to control gimbal
    d) Pre-arm check bindings (allows scripts to check if pre-arm checks have passed)
    e) Semicolon (:) and period (.) supported (e.g both Logger:write() and Logger.write will work)
8) Sensor driver enhancements
    a) Benewake H30 radar support
    b) BMI270 IMU performance improvements
    c) IRC Tramp VTX suppor
    d) Logging pause-able with auxiliary switch.  see RCx_OPTION = 165 (Pause Stream Logging)
    e) Proximity sensor support for up to 3 sensors
    f) Precision Landing consumes LANDING_TARGET MAVLink message's PositionX,Y,Z fields
    g) RichenPower generator maintenance-required messages can be suppressed using GEN_OPTIONS param
    h) TeraRanger Neo rangefinder support
    i) GPS support to provide ellipsoid altitude instead of AMSL (see GPS_DRV_OPTIONS)
    j) W25N01GV 1Gb flash support
9) Bug fixes
    a) Accel calibration throws away queued commands from GCS (avoids commands being run long after they were sent)
    b) Airmode throttle mix at zero throttle fix
    c) Cygbot proximity sensor fix to support different orientations (see PRXx_ORIENT)
    d) Loiter fix to avoid potential wobble on 2nd takeoff (was not clearing non-zero attitude target from previous landing)
    e) Lutan EFI message flood reduced
    f) Missions download to GCS corruption avoided by checking serial buffer has space
    g) Payload place fix so vehicle flies to specified Lat,Lon (if provided).  Previously it could get stuck
    h) Safety switch disabled if IOMCU is disabled (see BRD_IO_ENABLE=0)
    i) Script restart memory leak fixed
    j) Takeoff vertical velocity limits enforced correctly even if PILOT_TKOFF_ALT set to a significant height
10) Developer items
    a) Custom controller support
    b) Fast loop task list available in real-time using @SYS/tasks.txt
    c) Parameter defaults sent to GCS with param FTP and recorded in onboard logs
    d) ROS+ArduPilot environment installation script
    e) Sim on Hardware allows simulator to run on autopilot (good for exhibitions)
    f) Timer info available in real-time using @SYS/timers.txt
------------------------------------------------------------------
Copter 4.2.4 16-Aug-2023
1) Loiter fix to avoid potential wobble or flip on takeoff
------------------------------------------------------------------
Copter 4.2.3 30-Aug-2022
Changes from 4.2.3-rc3
1) OpenDroneId bug fix to consume open-drone-id-system-update message
------------------------------------------------------------------
Copter 4.2.3-rc3 20-Aug-2022
Changes from 4.2.3-rc2
1) OpenDroneId improvements including reporting if operator location is lost
2) Firmware ID and CRC check (disabled by default)
3) Bug Fixes
    a) Auto takeoff with terrain altitude frame fix (could cause climb away if rangefinder became out of range)
    b) Revert Notch filter ordering on loss of RPM source (see 4.2.3-rc1's 3g below) because fix is incomplete
------------------------------------------------------------------
Copter 4.2.3-rc2 13-Aug-2022
Changes from 4.2.3-rc1
1) BlueRobotics Navigator autopilot filesystem fix
------------------------------------------------------------------
Copter 4.2.3-rc1 12-Aug-2022
Changes from 4.2.2
1) OpenDroneId support (aka RemoteID)
2) New autopilot support
    a) CubeOrange+
    b) Foxeer Reaper F745
    c) MFE PixSurveyA1
    d) Pixhawk6C and Pixhawk6X
3) Bug Fixes and minor enhancements
    a) Battery monitor health check fixed to check all enabled monitors
    b) ICE Lutan EFI update serial flood fixed
    c) ICM42xxx IMU filter settings improved and allow for faster sample rates
    d) INA2xx batteries may init after startup
    e) KakuteH7 OSD parameter menu enabled
    f) Lua script support to set desired speed in Auto mode
    g) Notch filter ordering bug on loss of RPM source fixed
    h) Payload Place mission command obeys specified altitude type (was always terrain alt)
    i) PreArm check that MOT_PWM_MIN/MAX are non-zero
    j) PreArm check of Rangefinder pin conflict and servo outputs
    k) SCurve logs debug if internal error occurs
    l) WSL2 upload fixed (developer issue only)
------------------------------------------------------------------
Copter 4.2.2 18-Jul-2022 / 4.2.2-rc2 04-Jul-2022
No changes from 4.2.2
------------------------------------------------------------------
Copter 4.2.2-rc1 21-Jun-2022
Changes from 4.2.1
1) MambaH743v4 and MambaF405 MK4 autopilot support
2) Second full harmonic notches available (see INS_HNTC2_ parameters)
3) UAVCAN memory usage reduced (see CAN_Dn_UC_POOL parameter to control DroneCAN pool size)
4) VTOL QuikTune lua script added
5) Watchdog (caused by hardfault) saves crash dump logs to SD card
6) Bug fixes
    a) Circle mode stops below altitude fence
    b) CRSF protection against watchdog on bad frames
    c) CRSF reset in flight handled
    d) FFT init watchdog fix when ARMING_REQUIRE=0 (not actually possible on Copter)
    e) OSD flight modes menu includes newer flight modes
    f) Param download (via MAVFTP) fixed for params with overlapping names
    g) PWM rangefinder bug fix and added SCALING parameter support
    h) Replay bug fix when EK3_SRCs changed
    i) SERIALx_OPTION fix when "Don't forward mavlink to/from" selected (resolves MAVLink gimbal detection)
    j) TradHeli Autotune fix which could cause incorrect gains to be loaded
    k) VL53L1X rangefinder preserves addresses
------------------------------------------------------------------
Copter 4.2.1 07-Jun-2022 / 4.2.1-rc1 28-May-2022
Changes from 4.2.0
1) CAN ESCs bus bandwidth efficiency improvements (see CAN_Dx_UC_ESC_OF parameter)
2) DShot timing improvements to support for ESC variants
3) LOITER_TURNS command radius max increased from 255m to 2550m (but only 10m accuracy when over 255)
4) Luftan EFI measures fuel consumption (see EFI_COEF1, EFI_COEF2)
5) Bug fixes
    a) CAN ESCs work on boards with no safety switch (e.g. MatekH743)
    b) Inflight Compass calibration checks GSF yaw estimate is good
    c) LOITER_TURNS command's Turns field limited to 255 (previously would wrap to lower number)
    d) NeoPixel colour fix
    e) Precision Landing maintains yaw during retries
------------------------------------------------------------------
Copter 4.2.0 23-May-2022 / 4.2.0-rc4 14-May-2022
Changes from 4.2.0-rc3
1) FlyingMoon F407 and F427 autopilots supported
2) Vibration failsafe disabled in manual modes
3) Bug fixes
    a) Log file list with over 500 logs fixed
    b) Loiter mode ignores ATC_SLEW_YAW parameter
    c) RSSI when using IOMCU pin 103 fixed
    d) TradHeli internal error during takeoff fixed
------------------------------------------------------------------
Copter 4.2.0-rc3 07-May-2022
Changes from 4.2.0-rc2
1) Bug fixes
    a) Blended Z axis accel calculation fix when not using first IMU
    b) Custom compass orientation for DroneCAN compasses
------------------------------------------------------------------
Copter 4.2.0-rc2 29-Apr-2022
Changes from 4.2.0-rc1
1) Minor Enhancements
    a) Button, Relay and RPM GPIO pin conflict pre-arm check improved
    b) DShot uses narrower bitwidths for more accurate timing (allows BLHeli BlueJay to work)
    c) iFlight Chimera default parameters file added
    d) INS_NOTCH parameters renamed to INS_HNTC2
    e) Matek F765-Wing-bdshot firmware added
    f) Matek H743 supports ICM42688
    g) QiotekZealot H743 supports ICM4xxxx
    h) Scripting heap size increased to 100k on F7/H7
    i) SPRacingH7 improvements including external flash performance improvements
2) Bug fixes
    a) BMI088 IMU FIFO overruns fixed
    b) DO_SET_SERVO with SERVOn_FUNCTION=0 fixed, added pre-arm check of servo functions configured on disabled channels
    c) Log file descriptor init fixed (issues only seen on Linux autopilots)
    d) Log list cope with gaps, performance improvement to reduce impact on EKF and some ESCs
    e) Proximity sensor fix when using MAVLink lidars in non-forward orientations
    f) RPM sensor fix to avoid "failed to attach pin" spam to GCS
    g) STM32 DMA fatal exceptions disabled (caused watch dogs reboots with zero information)
    h) SysID mode bug fix (was not restoring body-frame feedforward setting upon exit)
    i) Tradheli autotune fix when max frequency is exceeded
------------------------------------------------------------------
Copter 4.2.0-rc1 10-Apr-2022
Changes from 4.2.0-beta3
1) Minor Enhancements
    a) Log and monitor threads stack size increased
    b) SPro H7 Extreme QSPI support improved
2) Bug fixes
    a) EKF3 accel bias fixed when an IMU is disabled
    b) MatekH743 buzzer fixed by reverting to 16 bit timer
    c) STM32 H7 flash storage bug fixed that caused re-init on overflow
    d) @SYS file logging fixed
    e) Timer bug fixed that could cause a watchdog on boards using flash storage
    f) UART driver incorrect lock class fixed
------------------------------------------------------------------
Copter 4.2.0-beta3 30-Mar-2022
Changes from 4.2.0-beta2
1) Minor Enhancements
    a) BATT_OPTIONS supports sending resting voltage (corrected for internal resistance) to the ground station
    b) KakuteH7-bdshot support
    c) MatekH743 uses a 32 bit timer to resolve occasional 68ms timing glitch
    d) RC input protocol text message sent to GCS (helps pilot awareness during RC handover)
    e) Autotune code changes to reduce flash size (no functional impact)
2) Bug fixes
    a) Battery remaining percentage fixed when using Sum battery
    b) DShot reversal bug with IOMCU based boards (see SERVO_BLH_RVMASK)
    c) GPS blending fix that could have resulted in the wrong GPS being used for a short time
    d) Param conversion bug (impacted airspeed enable)
    e) RC handover between IOMCU RC input and a secondary RC input on a serial port fixed
    f) Terrain reference adjustment ensures alt-above-terrain is zero at takeoff (see TERRAIN_OFS_MAX)
    g) QioTek Zealot H743 SLCAN port and relays fixed
------------------------------------------------------------------
Copter 4.2.0-beta2 10-Mar-2022
Changes from 4.2.0-beta1
1) Auto and Guided mode changes
    a) Delay removed when waypoints are very close together
    b) Pause and continue support (GCS changes are still pending)
    c) Takeoff and landing use position controller with slower reposition speed (also affects RTL, Land modes)
    d) Waypoint navigation will make use of maximum waypoint radius to maximize speed through corners
2) Follow mode supports FOLLOW_TARGET message (sent by QGC ground station)
3) Bug fixes
    a) Arming checks ignore SERVOx_MIN, MAX if setup as GPIO pin
    b) BeastH7v2 BMI270 baro support
    c) DShot prescaler fix (ESCs were not initialising correctly)
    d) EKF3 variance constraint fix used to prevent "ill-conditioning"
    e) POWR log message MCU voltage fix (min and max voltage were swapped)
    f) SPRacingH7 firmware install fix
------------------------------------------------------------------
Copter 4.2.0-beta1 28-Feb-2022
Changes from 4.1.5
1) AHRS/EKF improvements
    a) EKF startup messages reduced
    b) GPS<->Opticalflow automatic switching improved (see ahrs-source-gps-optflow.lua script)
    c) LORD Microstrain CX5/GX5 external AHRS support
    d) ModalAI VOXL non-GPS system supported (set VISO_TYPE=3, uses ODOMETRY mavlink msg)
2) Control and flight mode enhancements
    a) Acro max rate params renamed and improved (see ACRO_RP_RATE, ACRO_Y_RATE, PILOT_Y_RATE, PILOT_Y_EXPO)
    b) Airmode NOT enabled when arming with switch (see ACRO_OPTIONS and "ArmDisarm with AirMode" RCx_OPTION)
    c) Auto and Guided support pausing (uses DO_PAUSE_CONTINUE mavlink command)
    d) Auto supports up to 100 DO_JUMP commands on high memory boards
    e) Autotune reporting improved (less spam, prints final tune)
    f) FLIGHT_OPTIONS to release gripper on thrust loss
    g) ForceFlying auxiliary switch allows pilot to disable land-detector
    h) Guided mode acceleration target support
    i) Guided mode logging of targets improved (GUID msg split into GUIP, GUIA)
    j) Harmonic notch filter on more than four motors
    k) Land mode pilot controlled repositioning max speed reduced (also affects RTL and Auto)
    l) Precision landing improvements esp when using PLAND_EST_TYPE = 1/KalmanFilter
    m) RTL's safe altitude (RTL_ALT) increased 85km (was 325m)
    n) Simple mode heading reset via auxiliary switch
    o) Sport mode deprecated
    p) SURFTRAK_MODE allows disabling surface tracking in Loiter, AltHold, etc
    q) Turtle mode (allows righting a copter while on the ground)
3) Custom build server support (see https://custom.ardupilot.org)
4) Lua scripting improvements
    a) ahrs::get_location replaces get_position (get_position still works for now)
    b) Auto support for NAV_SCRIPT_TIME commands (Lua within Auto)
    c) Frame string support (allows scripting based frame to display custom description after startup)
    d) Parameter support (no need to always use SCR_USERx)
    e) Servo output control by channel number (previously was only possibly by specifying the SERVOn_FUNCTION value) 
    f) Script logged to onboard log (can be disabled by setting SCR_DEBUG_OPTS)
5) New autopilots supported
    a) AirLink
    b) BeastF7v2, BeastH7v2
    c) JHEMCU GSF405A
    d) KakuteH7, KakuteH7Mini
    e) MambaF405US-I2C
    f) MatekF405-TE
    g) ModalAI fc-v1
    h) PixC4-Jetson
    i) Pixhawk5X
    j) QioTekZealotH743
    k) RPI-Zero2W
    l) SPRacingH7 Extreme
    m) Swan-K1
6) Safety improvements
    a) Dijkstra's avoidance performance improvements including converting to A*
    b) Motor PWM range always uses MOT_PWM_MIN/MAX (if these are not set, they are defaulted to RC3_MIN/MAX values)
    c) Parachute option to leave servo in open position (see CHUTE_OPTIONS parameter)
    d) Parachute released arming check added
    e) Pre-arm check of IMU heater temp
    f) Pre-arm check of rangefinder health
    g) Pre-arm check of throttle position skipped if PILOT_THR_BHV is "Feedback from mid stick"
7) Sensor driver enhancements
    a) ADIS16470, ADIS16507 and BMI270 IMU support
    b) Airspeed sensor support (reporting only, not used for estimation or control)
    c) AK09918 compass support
    d) Battery monitor supports voltage offset (see BATTx_VLT_OFFSET)
    e) Benewake TFMiniPlus I2C address defaults correctly
    f) Buzzer can be connected to any GPIO on any board
    g) Compass calibration (in-flight) uses GSF for better accuracy
    h) CRSFv3 support, CSRF telemetry link reports link quality in RSSI
    i) Cybot D1 Lidar
    j) DroneCan (aka UAVCAN) battery monitors support scaling (see BATTx_CURR_MULT)
    k) DroneCan (aka UAVCAN) GPS-for-yaw support
    l) Electronic Fuel Injection support incl Lutan EFI
    m) FETtecOneWire resyncs if EMI causes lost bytes
    n) IMU heater params renamed to BRD_HEAT_xxx
    o) Landing gear enable parameter added (see LGR_ENABLE)
    p) Lightware SF40C ver 0.9 support removed (ver 1.0 and higher still supported)
    q) Maxbotix serial sonar driver support RNGFNDx_SCALING parameter to support for varieties of sensor
    r) MPPT solar charge controller support
    s) MTK GPS driver removed
    t) Optical flow in-flight calibration
    u) Ping200x support
    v) Proximity sensor min and max range (see PRX_MIN, PRX_MAX)
    w) QSPI external flash support
    x) uLanding (aka USD1) radar provides average of last few samples
    y) Unicore NMEA GPS support for yaw and 3D velocity 
8) TradHeli enhancements
    a) Attitude control default gains improved
        - ATC_RAT_RLL_FF, _I, _IMAX, _ILMI, _FLTT
        - ATC_RAT_PIT_FF, _I, _IMAX, _ILMI, _FLTT
        - ATC_RAT_YAW_IMAX, _FLTT
    b) AutoTune mode
    c) Collective setup  (users will be forced to setup new collective parameters)
    d) Rotor Speed Controller Internal Governor improvements (users required to retune governor)
    e) Rotor Speed Controller Cooldown timer added for ICE and turbine engines
    f) _VFF params renamed to _FF
9) Other System enhancements
    a) Board ID sent in AUTOPILOT_VERSION mavlink message
    b) Compass calibration stick gestures removed
    c) DO_SET_CAM_TRIG_DIST supports instantly triggering camera
    d) DJI FPV OSD multi screen and stats support
    e) GPIO pins configured by setting SERVOx_FUNCTION to -1 (also see SERVO_GPIO_MASK. BRD_PWM_COUNT removed)
    f) GPIO pin support on main outputs on boards with IOMCU
    g) GyroFlow logging (see LOG_BITMASK's "VideoStabilization" option)
    h) Firmware version logged in VER message
    i) SD card format via MAVLink
    j) Serial port option to disable changes to stream rate (see SERIALx_OPTIONS)
    k) VIBE logging units to m/s/s
10) Bug fixes
    a) Auto and Guided mode takeoff alt fixed if taking off from below home or frame set to absolute (aka AMSL)
    b) Auto mode CONDITION_YAW command completion fix
    c) Auto mode infinite loop with nav commands that fail to start fixed
    d) AutoTune disables ATC_RAT_xxx_SMAX during twitch (SMAX may limit gains to reduce oscillation) 
    e) BLHeli passthrough reliability improvements
    f) Compass learning (inflight) fixed to ignore unused compasses (e.g. those with COMPASS_USE = 0)
    g) EKF optical flow terrain alt reset fixed (large changes in rangefinder alt might never be fused)
    h) EKF resets due to bad IMU data occur at most once per second
    i) GPIO pin fix on CubeOrange, F4BY, mRoControlZeroF7, R9Pilot
    j) Guided mode yaw rate timeout fix (vehicle could keep yawing even if companion computer stopped sending target)
    k) MAVlink2 serial ports always send MAVLink2 messages (previously waited until other side sent MAVLink2)
    l) Motor Test, Turtle mode respect emergency stop switch
    m) Omnibusf4pro bi-directional dshot fix
    n) PosHold braking fix if SCHED_LOOP_RATE set to non-default value
    o) Precision landing in RTL activates even if pilot had earlier deactivated by repositioning in Land
    p) Real-Time-Clock (RTC) oldest possible date updated to Jan-2022
    q) Tricopter, Coax copter fin trim fix when using DShot/BLheli
------------------------------------------------------------------
Copter 4.1.6 16-Mar-2023
Changes from 4.1.5
1) Loiter fix to avoid potential wobble or flip on takeoff
------------------------------------------------------------------
Copter 4.1.5 19-Feb-2022 / 4.1.5-rc1 10-Feb-2022
Changes from 4.1.4
1) Bug fixes
    a) attitude control I-term always reset when landed (previously only reset after spool down)
    b) revert SBUS RC frame gap change from 4.1.4
------------------------------------------------------------------
Copter 4.1.4 08-Feb-2022 / 4.1.4-rc1 27-Jan-2022
Changes from 4.1.3
1) Benewake CAN Lidar support
2) CAN GPS default lag dropped to 0.1 seconds (was 0.22 seconds)
3) Bug fixes
    a) Compass custom orientation is never overwritten during calibration
    b) EKF logging gaps fixed (some messages were occasionally being skipped)
    c) Guided mode cornering improvements
    d) IMU logging fix for IREG message (records IMU register changes)
    e) LOITER_TO_ALT mission command's climb rate fixed (could climb or descend too quickly)
    f) Position controller init fix to avoid twitch on vehicles with high vibrations
    g) Position controller init fix to better handle high speed entry to flight mode
    h) Position controller prioritises reducing cross track error
    i) Position controller relax fix
    j) SBUS RC frame gap increased to better handle some new receivers
    k) SERVOx_FUNCTION protection to avoid memory overwrite if set too high
    l) SD card init triggering watchdog fixed
    m) Spline path max lateral acceleration reduced (vehicle stays on path better but may be slower)
    n) Takeoff bug fix if taking off below home or frame specified as MSL
------------------------------------------------------------------
Copter 4.1.3 31-Dec-2021 / 4.1.3-rc2 21-Dec-2021
Changes from 4.1.3-rc1
1) Suport for IIM-42652, ICM-40605 and ICM-20608-D IMUs
2) Bug fixes
    a) Autotune twitches at no more than ATC_RATE_R/P/Y_MAX param value
    b) SmartAudio high CPU load fix (previously it could starve other threads of CPU)
    c) Debug pins disabled by default to prevent rare inflight reset due to electrostatic discharge
    d) EKF3 reset causing bad accel biases fixed
    e) RC protocol detection fix that forced PH4-mini users to powerup autopilot before transmitter
------------------------------------------------------------------
Copter 4.1.3-rc1 18-Dec-2021
Changes from 4.1.2
1) Enhancements:
    a) CUAV-X7 servo voltage detection support
2) Bug fixes
    a) Main loop delay fix for boards with 16 bit timers (affects KakuteF4, MatekH743, MatekF405, MatekF765, SpeedybeeF4)
    b) MOT_MIX_MAX constrained between 0.1 and 4.0 (would previously reset to 0.5 if set too high or low)
    c) Polygon Fence upload fix when replacing fence with one that has fewer points
    d) TradHeli fix for missions which continue after a Land command
------------------------------------------------------------------
Copter 4.1.2 07-Dec-2021 / 4.1.2-rc1 22-Nov-2021
Changes from 4.1.1
1) CAN_Dn_UC_OPTION param added to help resolve DroneCAN DNA conflicts
2) Durandal with alternative ICM-20602 IMU
3) OBAL autopilot support (Open Board Architecture for Linux)
4) FETtec One ESC protocol support
5) Bug Fixes
    a) ADSB vertical velocity reporting fix
    b) APM/LOGS directory creation fixed on some boards
    c) AutoTune fix to disable SMAX limits that could interfere with tune
    d) EKF3 fix to switch to non-zero primary core when disarmed
    e) Notch filter update rate fix
    f) Surface tracking fix if rangefinder glitches
    g) TradHeli rename of H_COLL_HOVER to H_COL_HOVER
------------------------------------------------------------------
Copter 4.1.1 10-Nov-2021 / 4.1.1-rc1 16-Oct-2021
Changes from 4.1.0 
1) EK3_PRIMARY allows selection of which EKF core/IMU to use on startup
2) ESC telemetry sent during compassmot
3) TradHeli landing detector improvement
4) Bug Fixes
    a) Auto/Guided mode fix to EXTENDED_SYS_STATE message's "landed state" field after takeoff
    b) MAVFTP init fix (could cause slow parameter download)
    c) Scripting fix when logging strings
    d) Serial flow control fix (affected at least Lightware LW20 serial lidar)
    e) QiotekZealotF427 IMU (ICM42605) orientation fixed
------------------------------------------------------------------
Copter 4.1.0 08-Oct-2021 / 4.1.0-rc4 01-Oct-2021
Changes from 4.1.0-rc3
1) Position controller PSC/Z logging changed to PSCN/E/D and includes "desired"
2) Bug Fixes
    a) Position Controller init fix (could cause unexpected movement after entering Guided)
    b) Serial8 parameter description fixed 
------------------------------------------------------------------
Copter 4.1.0-rc3 27-Sep-2021
Changes from 4.1.0-rc2
1) Enhancements
    a) Guided mode supports yaw control during takeoff
    b) MatekH743 supports icm42605 IMU 
2) Bug Fixes
    a) CSRF fix to avoid intermittent loss of telemetry
    b) EKF3 fix to init of wind speed variance
    c) Guided mode fix to yaw change after takeoff completes
    d) Guided mode uses GUID_OPTIONS to ignore pilot yaw during takeoff
    e) Lightware SF45b jumpy readings fixed by using lower update rate
------------------------------------------------------------------
Copter 4.1.0-rc2 15-Sep-2021
Changes from 4.1.0-rc1
1) Enhancements
    a) Guided mode supports using SCurves and OA for position control
2) Bug Fixes
    a) Serial port auto detection of flow control fix
------------------------------------------------------------------
Copter 4.1.0-rc1 10-Sep-2021
Changes from 4.1.0-beta8
1) Enhancements
    a) Log download speeds improved on F765 and F777 based boards (USB buffers increased)
    b) Serial port DMA contention heuristics improved (reduces chance of delays writing to serial devices)
    c) WPNAV_SPEED param changes take immediate effect in Auto
2) Bug Fixes
    a) Airmode fix if throttle held at zero for long time
    b) Auto mode SCurve jerk time calculation fix (prevents jerk from being set too high)
    c) CRSF frame timeout increased to reduce RC failsafes
    d) Declination automatic lookup rounding fix (caused inaccurate declination in some parts of world)
    e) DShot (bi-directional) support on Pixhawk4, CUAVv5, CUAVv5-Nano
    f) IMU semaphore fix to avoid occasional corruption
    g) QioTek Zealot F427 GPIO pin fix
    h) Replay/DAL RMGH log message format fix
    i) Rangefinder initial buffer size and baudrate fix (affected Aintein US-D1 radar)
    j) Surface tracking fix to reduce lag and overshoot
    k) WPNAV_SPEED change with ch6 tuning knob feature gets divide-by-zero protection
    l) WPNAV_SPEED_DN handles negative values
------------------------------------------------------------------
Copter 4.1.0-beta8 28-Aug-2021
Changes from 4.1.0-beta7
1) Enhancements
    a) Flywoo F745 Goku Nano support
    b) MatekF765-Wing support
    c) Scripting support for getting circle mode radius and setting rate
    d) Scripting support for new Guided mode controls including acceleration control
2) Bug Fixes
    a) ATC_RATE_Y_MAX applies to all modes including Loiter
    b) ChibiOS scheduling slip workaround to avoid occasional 60ms delays found on MatekH7
    c) EKF2 divide-by-zero protection when using optical flow (issue only found in simulator)
    d) External AHRS (e.g. VectorNav driver) init fix
    e) KakuteF4Mini SBUS fix
    f) Pixhawk4 blue and red LEDs swapped
    g) Position control fixes to limit initial desired accel (horizontal and vertical) which could lead to an excessive climb or descent
    h) TradHeli takeoff in Guided fix
------------------------------------------------------------------
Copter 4.1.0-beta7 14-Aug-2021
Changes from 4.1.0-beta6
1) Enhancements
    a) Attitude and altitude control changes to support higher lean angles
    b) Flywoo F745 supports external I2C compasses
    c) GPS-for-yaw arming check added
    d) GPS_DRV_OPTIONS allows forcing UBlox GPS to 115200 baud
    e) Guided mode accepts higher speed and accel targets (no longer limited by WPNAV_ parameters)
    f) Lua scripts can be placed in root of ROMFS (only relevant for developers)
    g) PSC_VELXY_FILT renamed to _FLTE, PSC_VELXY_D_FILT renamed to _FLTD
2) Bug Fixes
    a) Beacon driver protected from requests for data for non-existant beacons
    b) CAN threading fix to resolve potential lockup when lua scripts use CAN
    c) EKF3 GSF can be invoked multiple times with source switching (no longer limited by EK3_GSF_RST_MAX)
    d) EKF3 IMU offset fix (vehicle's reported position was slightly incorrect if INS_POS_XYZ params set)
    e) Guided mode terrain following init fix (might fly at incorrect alt on second use)
    f) Guided mode yaw rate target timeout fix (vehicle could keep spinning even after targets stopped arriving)
    g) OSD overwrite and nullptr check fix
    h) Proximity Avoidance auxiliary switch also disables avoidance using upward facing lidar
    i) Proximity sensor pre-arm check disabled if avoidance using proximity sensors is disabled 
    j) RCOut banner displayed at very end of startup procedure to avoid invalid output
    k) Tricopter tail servo alway uses regular PWM (fixes use with BLHeli motors)
------------------------------------------------------------------
Copter 4.1.0-beta6 23-Jul-2021
Changes from 4.1.0-beta5
1) Enhancements
    a) ACRO_Y_EXPO supports negative numbers (-0.5 to +1)
    b) GPS-for-yaw enhancements including using position and yaw from different GPSs
    c) Guided mode acceleration control
    d) Long distance travel supported (thousands of km) including double precision EKF and moving origin
2) Bug Fixes
    a) Auto and Guided mode terrain following fixed (could impact terrain if terrain was very steep. see WPNAV_TER_MARGIN)
    b) BendyRuler avoidance fixed (was slow and jerky)
    c) BLHeli fix that could cause failure to boot
    d) Crosstrack reporting fixed
    e) CRSF message spamming and firmware string length fixed
    f) Display re-enabled on 1MB boards
    g) DShot always sends 0 when disarmed (protects against motors spin while disarmed due to misconfiguration)
    h) DShot fix that could cause main loop jitter
    i) DShot buzzer tone disabled during motor test to remove bad interation
    j) Guided mode accepts position targets at high rate
    k) Longitude wrap fix (allows autonomous flights as longitude wraps between -180 and 180 deg)
    l) Log created on forced arm
    m) MatekF405-bdshot NeoPixel LEDs re-enabled on PWM5
    n) Precision landing init fix (if pilot took control, subsequent landings might not trying to land on target)
    o) Serial port performance improvements using FIFO on H7 boards
    p) TradHeli ground check of yaw fixed (yaw servo was not moving when landed)
    q) Throw mode waits for throttle up to improve reliability
------------------------------------------------------------------
Copter 4.1.0-beta5 30-Jun-2021
Changes from 4.1.0-beta4
1) Enhancements
    a) Arming check of distance from EKF origin increased to 250km horizontally (was 50km), 50km vertically (was 500m)
    b) Position control accuracy improved at very long distances (aka "Loiter bucketing")
    c) Pre-arm check of FLTMODE_CH and RCx_OPTION conflict
    d) QioTekZealotH743 autopilot support
    e) Scripting support for set home and get EKF origin
2) Bug fixes
    a) Autonomous mode (Auto, RTL, etc) stopping point calculation fixed (could cause alt drop entering RTL)
    b) Auto mode Internal Error fixed when origin and destination are very close
    c) Auto mode Terrain following less bouncy and RNGFND_FILT param added for configurable filtering
    d) EKF alt estimate more robust when using GPS-for-yaw
    e) EKF origin altitudes consistent across cores even if user takes off with no GPS lock
    f) Logging start does not affect EKF (EKF could become unhealthy due to timing delays)
    g) Loiter rocking while landing fixed
    h) Loiter speed fixed with low LOIT_ACC_MAX values
    i) Loiter aggressive deceleration fixed (occured if vehicle was previously switched out of Loiter during decleration)
    j) Longitude wrap fixed (allows flying across international date line)
    k) MOT_THST_EXPO limits fixed to allow values between -1 and +1 (was 0.25 to +1)
    l) Position jump fixed during GPS glitch and GPS/Non-GPS transitions
    m) Proximity sensor status (used for object avoidance) fixed when using only upward facing lidar
    n) Vibration failsafe fixed
    o) 6DOF copter fixed
------------------------------------------------------------------
Copter 4.1.0-beta4 14-Jun-2021
Changes from 4.1.0-beta3
1) Minor enhancements (or changes)
    a) CSRF telemetry improvements to power setting and pass param requests more quickly
    b) CUAV X7/Nora supports ICM42688P IMU
    c) Pix32v5 USB product string fixed and IMU heater enabled 
    d) RunCam Hybrid supported (see RUNCAM_TYPE parameter)
    e) VisualOdom feature removed from 1MB boards
2) Bug fixes
    a) BLHeli Auto only affects telemetry passthrough to ease setup
    b) Circular complex fence radius not truncated
    c) CubeOrange serial1/2 DMA fixed
    d) EKF ground effect compensation fixed
    e) ESC telemetry fixes including motor index on boards with I/O mcu
    f) I/O MCU reset fix if user had disabled safety switch (recovery from reset would leave motors not spinning)
    g) MSP temperature scaling fixed
    h) Pilot yaw rate input during arming ignored
    i) PreArm check of roll/pitch and yaw angle difference fixed
    j) Serial port info file (@SYS/uarts.txt) easier to understand
    k) Scheduler fix of premature run of tasks every 163 seconds
    l) Visual odometry yaw alignment fixed
    m) WPNAV_RADIUS never less than 5cm
------------------------------------------------------------------
Copter 4.1.0-beta3 24-May-2021
Changes from 4.1.0-beta2
1) Dshot bug fix for autopilots with I/O boards (CubeBlack, CubeOrange, etc)
------------------------------------------------------------------
Copter 4.1.0-beta2 19-May-2021
Changes from 4.1.0-beta1
1) Attitude control and Navigation
    a) Auto mode Spline command fix when using terrain altitudes
    b) Drift after takeoff fix if GPS position has moved
    c) Feed forward angular velocity calculation fix
    d) Guided mode option (GUID_OPTIONS) to configure how SET_ATTITUDE_TARGET's thrust field is interpreted
    e) Improved position control during loss of yaw control by using thrust vector with heading
    f) Standby mode disables hover learning
2) BLHeli improvements and fixes
    a) Bi-directional ESC telemetry fixes
    b) Bi-directional dshot 1200 supported
    c) Control of digital with non-digital outputs fixed
    d) Support dshot commands for LED, buzzer and direction control
    e) Passthru reliability improved
3) New autopilot boards
    a) PixC4-Pi and PixC4-Jetson
4) Avoidance fixes
    a) OBSTACLE_DISTANCE_3D boundary cleared after 0.75 seconds, allows easier multi-camera support
    b) Simple avoidance logging re-enabled 
5) Other enhancements
    a) Auxiliary function logging shows how it was invoked (switch, button or scripting)
    b) External IST8308 compass supported on CubeBlack
    c) FLOW_TYPE parameter hides/displays other FLOW_ parameters
    d) FrSky telem reports failsafe, terrain alt health and fence status
    e) Harmonic notch support for CAN ESCs (KDECAN, PiccoloCAN, ToshibaCAN, UAVCAN)
    f) OSD gets fence icon
    g) RunCam OSD and camera control
    h) Septentrio GPSs support GPS_SBAS_MOD parameter
6) Bug fixes
    a) Barometer averaging fixes for BMP380, BMP280, LPS2XH, SPL06 drivers
    b) EKF3 fix to reset yaw after GPS-for-yaw recovers
    c) KDECAN output range, motor order and pre-arm check messages fixed
    d) Logging memory leak when finding last log fixed
    e) Pixhawk4 mini safety switch fix
    f) SD card slowdown with early mounts fixed
------------------------------------------------------------------
Copter 4.1.0-beta1 14-Apr-2021
Changes from 4.0.7
1) EKF changes:
    a) EKF3 is default estimator (EKF2 is available as an option)
    b) External AHRS/IMU support (e.g. VectorNav)
    c) Gaussian Sum Filter (GSF) allows emergency yaw correction using GPS
    d) GPS-for-yaw (dual F9 UBlox, Septentrio, NMEA GPS can provide yaw)
    e) Lane switching logic improvements
    f) Sensor affinity (improves sensor failure redundancy)
    g) Source switching for GPS/Non-GPS transitions (see EK3_SRCx_ parameters)
    h) Yaw estimation and reliability improvements
    i) Wind speed estimation and barometer interference compensation
2) Control and Navigation improvements:
    a) Acro "air mode" support (see ACRO_OPTIONS)
    b) Auto mode arming and takeoff (see AUTO_OPTIONS)
    c) Circle options to face direction of travel and/or init at circle center (see CIRCLE_OPTIONS)
    d) DO_LAND_START command support for landing sequences
    e) Horizontal Velocity controller gets feed forward and logging
    f) Position controller update to remove hard-coded Alt Hold velocity derivative and other enhancements
    g) Rate PID slew limiting to to detect and suppress oscillations
    h) SCurves for waypoint navigation
    i) Yaw imbalance check
3) TradHeli improvements:
    a) Conventional and compound helicopter SITL dynamic models improved
    b) Intermeshing rotor and coaxial rotor support added to Dual heli frame
4) 6DoF frame support
5) Object avoidance:
    a) BendyRuler hesitancy improvements
    b) Intel Realsense 435/455 camera support (companion computer required)
    c) Obstacle database now 3D
    d) Obstacle filtering improvements
    e) Obstacles ignored in cylinder around home (see OA_DB_ALT_MIN)
    f) Obstacles ignored near ground (see PRX_IGN_GND)
    g) Vertical BendyRuler
    h) Simple avoidance backs away from obstacles (see AVOID_BACKUP_SPD parameter)
    i) Simple avoidance accel limited (see AVOID_ACCEL_MAX parameter)
    j) Simple avoidance enabled above min altitude (see AVOID_ALT_MIN)
    k) Simultaneous Dijkstra and BendyRuler path planning
6) Compass enhancements
    a) Custom orientations
    b) In-flight learning improvements (see COMPASS_LEARN = 3)
    c) Large vehicle calibration support (e.g. point vehicle north and push button in MP)
7) Intel RealSense T265 support (see VISO_TYPE = 2, companion computer required)
    a) Position and velocity from external sources accepted at up to 50hz
    b) Resets from external sources accepted
8) New autopilot boards
    a) FlywooF745
    b) iFlight BeastF7 and BeastH7
    c) MambaF405v2
    d) QioTekZealotF427
9) IMU improvements:
    a) temperature calibration
    b) faster gyro sampling on high performance autopilots (F7 and faster, see INS_GYRO_RATE)
10) New drivers
    a) AllyStar NMEA GPS
    b) BMM150 as external compass
    c) CRSF and SRXL2 RC protocols
    d) Dshot (bi-directional) for RPM telemetry
    e) GY-US32-v2 lidar
    f) HC-SR04 lidar
    g) Intelligent Energy hydrogen fuel cell
    h) Lightware SF45b lidar
    i) MSP protocol support (and DJI DPV systems)
    j) RichenPower generator
    k) Rotoye smart battery
    l) RunCam Split 4 and RunCam hybrid support
    m) Smart Audio
    n) SMBus batteries up to 12 cells
    o) USD1 CAN radar
11) Harmonic Notch Improvements
    a) Bi-directional dshot support
    b) Double notch support (see INS_HNTCH_OPTS = 1)
    c) In-flight FFT (see INS_HNTCH_MODE = 4, FFT_* params)
    d) In-flight learning of throttle notch using in-flight FFT
    e) Notch per motor support using ESC telemetry and notch-per-peak with FFT (INS_HNTCH_OPTS = 2)
    f) Notch slewing and increased update rate to avoid "shot" noise
    g) RPM status driven from harmonic notch input
12) Scripting enhancements:
    a) Button, Proximity, RPM sensor support
    b) DO_ mission commands can be triggered from scripts
    c) I2C sensor driver support (i.e. allows writing sensor drivers in Lua)
    d) Logging (i.e. allows Lua scripts to write to onboard logs)
    e) Mission item read support
    f) Motor drivers support allowing custom frame types and 6DOF
    g) Pre-arm checks (i.e. allows writing custom pre-arm checks in Lua)
    h) ROMFS support (allows writing scripts to ROMFS instead of SD Card)
    i) Serial port support (allows reading/writing to serial port from Lua)
    j) ToshibaCAN ESC usage time read support
13) Other enhancements:
    a) Baro parameters start with BARO_ (was GND_)
    b) Barometers get device id for easier identification
    c) ChibiOS upgrade to 20.3
    d) CRSF passthrough for Yaapu widget
    e) DShot rates increased (see SERVO_DSHOT_RATE)
    f) Filesystem/MAVFTP expansion including @SYS for performance monitoring
    g) GCS failsafe timeout configurable
    h) MAV_CMD_DO_REPOSITION support
    i) MAVFTP performance improvements
    j) Serial option to disable forwarding of mavlink to/from a port
    k) Serial ports may use shared DMA for better performance
    l) Spektrum VTX control
    m) Spektrum SRXL2 listen-only device support
    n) Vibration logged for all IMUs
    o) @SYS/tasks.txt, dma.txt, uart.txt for near real-time inspection of system performance
14) Bug fixes:
    a) CAN GPS ordering fix (previously order could switch meaning GPS_POS_ params were used on the wrong GPS)
------------------------------------------------------------------
Copter 4.0.8 12-Oct-2021 (TradHeli only release)
Changes from 4.0.7
1) TradHeli landing detector fix
2) Proximity sensor distance validity checked before pushing to object database
------------------------------------------------------------------
Copter 4.0.7 22-Feb-2021
Changes from 4.0.7-rc1
1) fixed build on Durandal board
2) multiple fixes for mRo boards: ControlZero*, PixracerPro
------------------------------------------------------------------
Copter 4.0.7rc1 6-Feb-2021
Changes from 4.0.6
1) added automatic backup/restore of parameters in case of FRAM corruption for F7/H7 boards with 32k FRAM parameter storage
2) fixed a bug in EKF2/EKF3 that could cause memory corruption if external naviagtion sources (such as vision based position and velocity data) is supplied before the EKF has initialised
3) fixed a problem with low accuracy data from UAVCAN GPS modules when GPS blending is enabled
4) fixed an arming check failure with u-blox M9 based GPS modules
5) fixed a race condition in SmartRTL which could cause a LAND mode to be triggered
------------------------------------------------------------------
Copter 4.0.6 25-Jan-2021 / 4.0.6-rc2 16-Jan-2021
Changes from 4.0.6-rc1
1) Add support for keeping a backup of storage for last 100 boots
2) Bug fixes:
    a) Fix support for BLHeli_S passthru
------------------------------------------------------------------
Copter 4.0.6-rc1 23-Dec-2020
Changes from 4.0.5
1) Bug fixes:
    a) Fix vertical flyaways when rangefinder stops providing data and the user has configured EK*_ALT_SOURCE=1
    b) Correct units on raw accel data
    c) Fport RSSI value fix
    d) Correct compilation when Advanced Failsafe compile time option is enabled
    e) Correct time wrap in RAW_IMU mavlink message
    f) PixracerPro - Fix analog volt pin assignments
    g) fix landing detector for tradheli in acro mode
2) Small enhancements:
    a) Parameter documentation enhancements and corrections
    b) Improve harmonic notch filter parameter documentation
    c) Report prearm check status in the MAV_SYS_STATUS_PREARM_CHECK flag of the SYS_STATUS mavlink message
    d) Smooth I term reset over 0.5 seconds
3) TradHeli enhancements:
    a) Differential Collective Pitch (DCP) trim support for Dual Heli
    b) Incorporate hover collective learning
    c) Option for pitch, roll and yaw I term to be based on takeoff/landing
------------------------------------------------------------------
Copter 4.0.5 27-Oct-2020 / 4.0.5-rc2 08-Oct-2020
Changes from 4.0.5-rc1
1) Bug fixes:
    a) Serial/UART DMA race condition that could cause watdog reset fixed (Critical fix)
    b) SBUS output when no RC input fixed (Critical fix)
------------------------------------------------------------------
Copter 4.0.5-rc1 02-Oct-2020
Changes from 4.0.4
1) Bug fixes:
    a) Acro expo calculation fix (fixes sluggish behaviour)
    b) F32Lightening board IMU fast sampling fix
    c) GPS minimum accuracy added to protect EKF from unrealistic values
    d) KakuteF7/mini DShot glitch fix
    e) RC input gets additional protection against out-of-range inputs (<RCx_MIN or >RCx_MAX)
    f) RC_OPTION = 4 fix on boards with IOMCU (Pixhawk, etc). This allows ignoring SBUS failsafes 
2) Small enhancements:
    a) Linux boards accept up to 16 RC inputs when using UDP
    b) Protect against two many interrupts from RPM sensor, etc
    c) RM3100 compass support for probing all four I2C addresses
    d) Durandal telem3 port enabled
------------------------------------------------------------------
Copter 4.0.4 16-Sep-2020
Changes from 4.0.4-rc4
1) Matek H743, 765-Wing and F405-Wing get DPS310 Baro support 
------------------------------------------------------------------
Copter 4.0.4-rc4 28-Aug-2020
Changes from 4.0.4-rc3
1) Bug fixes:
    a) Compass startup reordering based on compass priorities fix
    b) TradHeli servo test fix
    c) Precision landing logging fix
    d) Mavlink commands ignored after reboot request
------------------------------------------------------------------
Copter 4.0.4-rc3 30-Jul-2020
Changes from 4.0.4-rc2
1) Bug Fixes and minor enhancements:
    a) Compass ids from missing compasses reset after compass cal
    b) LIS3MDL compass enabled on all boards
    c) Lightware I2C lidar fix when out-of-range
    d) Parameter erase fix for revo-mini and other boards that store to flash  
------------------------------------------------------------------
Copter 4.0.4-rc2 16-Jun-2020
Changes from 4.0.4-rc1
1) Bug Fixes:
    a) Watchdog monitor memory increased (may have caused watchdog reset)
    b) Compass ordering fix when COMPASS_PRIOx_ID is 0
    c) Hex CubeOrange 2nd current sensor pin correction
    d) Hott telemetry fix
    e) Lightware I2C driver fix when out-of-range
    f) MatekF765-Wing voltage and scaling fixed
    g) MatekH743 baro on I2C2 bus
    h) Proximity (360 lidar) ignore zone fix
2) Flight controller support:
    a) Bitcraze Crazyflie 2.1
    b) CUAV Nora V1.2
    c) Holybro Pix32v5
    d) mRo Pixracer Pro
3) Minor enhancements:
    a) Benewake RangeFinder parameter descriptions clarified
    b) Pre-arm check of attitude ignores DCM if multiple EKF cores present
------------------------------------------------------------------
Copter 4.0.4-rc1 26-May-2020
Changes from 4.0.3
1) Bug Fixes:
    a) I/O CPU fix so safety remains off after inflight reboot (Critical fix)
    b) Acro mode yaw expo supports values under 0.5 (see ACRO_Y_EXPO param)
    c) Auto mode Loiter-Turn commands points towards center
    d) Change-Speed commands applied smoothly
    e) Compass scaling factor threshhold increased
    f) EKF compass variance reporting to GCS made consistent with onboard logs
    g) Gimbal control using RC input ignores RCx_TRIM param
    h) Holybro Durandal buzzer fix
    i) Parameter reset fix caused by Eeprom race condition
    j) Read-only parameter write failure msg only sent once to GCS
    k) Compass declination can be overriden with COMPASS_DEC param (and EKF stops using world magnetic tables)
    l) Terrain database (SRTM) file fix (will cause all terrain to be reloaded after upgrade)
2) Bootloader update to reduce chance of param resets during firmware load
3) Compass ordering and prioritisation improvements
4) Flight controller support:
    a) CUAV-Nora
    b) CUAV-X7
    c) MatekSys H743
    e) mRo Nexus
    d) R9Pilot
5) GPS moving baseline (aka Yaw from GPS) for UBlox F9 GPSs
6) Graupner Hott telemetry support
7) Landing detector filter improvement improves detection on hard surfaces
8) Object Avoidances Fixes and improvements:
    a) BendyRuler runs more slowly to reduce CPU load and reduce timeouts
    b) Dijkstra's avoidance works with more fence points
    c) Proximity drivers (i.e. 360deg lidar) simplified to reduce CPU load
9) ProfiLED LEDs support
10) Smart Battery improvements:
    a) Cycle count added
    b) NeoDesign battery support
    c) SUI battery support 
11) Other enhancements:
    a) Betaflight X frame type support
    b) Landing gear auto deploy/retract configurable using LGR_OPTIONS param
    c) MOT_PWM_MIN/MAX pre-arm check (checks fail if only one has been set)
    d) Solo gimbal and camera control improvements
    e) USB IDs updated to new ArduPilot specific IDs
------------------------------------------------------------------
Copter 4.0.3 28-Feb-2020 / 4.0.3-rc1 20-Feb-2020
Changes from 4.0.2
1) Bug Fixes:
    a) "RCInput: decoding" message watchdog reset when using MAVLink signing (Critical Fix)
    b) HeliQuad yaw control fix
    c) Do-Set-Servo commands can affect sprayer, gripper outputs
    d) BLHeli passthrough fix for H7 boards (CubeOrange, Holybro Durandal)
2) USB IDs updated for "composite" devices (fixes GCS<->autopilot connection issues for boards which present 2 USB ports)
3) RCOut banner helps confirm correct setup for pwm, oneshot, dshot 
4) ZigZag mode supports arming, takeoff and landing
------------------------------------------------------------------
Copter 4.0.2 11-Feb-2020
Changes from 4.0.2-rc4
1) MAVFTP stack size increased to 3k (fixes reboot when using MAVFTP)
------------------------------------------------------------------
Copter 4.0.2 11-Feb-2020 / 4.0.2-rc4 05-Feb-2020
Changes from 4.0.2-rc3
1) Bug Fixes:
    a) Spektrum receivers decoding fix for Pixracer
    b) Current Alt frame always relative to home (RTL could return at wrong alt)
    c) Circle mode pitch control direction fixed
    d) EKF only uses world magnetic tables if COMPASS_SCALE is set
    e) Logging reliability improvements especially for FRAM logs
    f) RangeFinders using PWM interface use RNGFNDx_OFFSET param (attempt2)
    g) SpeedyBeeF5 probes all I2C ports for external baro
2) Rangefinder fallback support (both must have same _ORIENT) 
------------------------------------------------------------------
Copter 4.0.2-rc3 01-Feb-2020
Changes from 4.0.2-rc2
1) Bug Fixes:
    a) AutoTune fix to restore original gains when AutoTune completes
------------------------------------------------------------------
Copter 4.0.2-rc2 31-Jan-2020
Changes from 4.0.1
1) Bug Fixes:
    b) IO CPU timing fix which reduces ESC sync issues
    c) PX4Flow driver probes all I2C ports on Hex Cubes
    d) RangeFinders using PWM interface (like Garmin LidarLite) use RNGFNDx_OFFSET param
    e) RC override fix when RC_OVERRIDE_TIME=-1 (allows disabling timeout when using joystick) 
    f) TradHeli attitude control parameter description fixes (does not affect flight)
    g) cygwin compiler fix (affects developers only)
2) Minor enhancements:
    a) GCS failsafe warning lights and tones
    b) Circle mode pitch control direction swapped
------------------------------------------------------------------
Copter 4.0.1 25-Jan-2020 / 4.0.1-rc3 19-Jan-2020
Changes from 4.0.1-rc2
1) Bug Fixes:
    a) Semaphore fixes for Logging, Filesystem and UAVCAN Dyanmic Node Allocation
    b) RangeFinder parameter fix (4th rangefinder was using 1st rangefinder's params)
    c) TradHeli STB_COL_x parameter description fixed
2) Minor Enhancements:
    a) Autorotate flight mode renamed to Heli_Autorotate
    b) Solo default parameters updated
    c) "Prepared log system" initialisation message removed
------------------------------------------------------------------
Copter 4.0.1-rc2 10-Jan-2020
Changes from 4.0.1-rc1
1) FrSky telemetry status text handling fix (Critical Fix)
------------------------------------------------------------------
Copter 4.0.1-rc1 10-Jan-2020
Changes from 4.0.0
1) Circle mode allows pilot control of radius and rotation speed
2) CAN servo feedback logged
3) Magnetic declination tables updated
4) Serial0 protocol forced to MAVLink (avoids accidental disabling of USB port)
5) Bug Fixes:
   a) TradHeli RSC RC passthrough fixed
   b) CubeOrange and Durandal I2C timing fixed (was running slow)
   c) Compass calibration auto orientation skips "pitch 7" which could cause cal to fail
   d) Durandal's fourth I2C port fixed
   e) Linux boards with CAN support fixed
   f) Neopixel added to SERVOx_FUNCTION param description
   g) NMEA Output fixed (was sending an extra CR)
   h) Optflow messages sent even if EKF has no height estimate
   i) SkyViper build fixed
   j) Spektrum/DSM 22ms RC input fixed
   k) "UC Node Down" message removed (was unnecessarily scary)
------------------------------------------------------------------
Copter 4.0.0 29-Dec-2019 / 4.0.0-rc6 28-Dec-2019
Changes from 4.0.0-rc5
1) Compiler updated to gcc 6.3.1 (2nd attempt)
------------------------------------------------------------------
Copter 4.0.0-rc5 23-Dec-2019
Changes from 4.0.0-rc4
1) RM3100 compass enabled on all boards
2) GCS failsafe disabled by default (see FS_GCS_ENABLE parameter)
3) Bug Fixes
   a) Bootloader fix for H7 boards (could brick CubeOrange, CUAV V5 Nano, etc)
   b) OmnibusF4pro GPS fix
   c) MatekF405-Wing missing serial ports restored
   d) MatekF765-Wing RTSCTS parameter defaults set correctly
   e) CUAV V5 Nano battery monitor param defaults improved
------------------------------------------------------------------
Copter 4.0.0-rc4 20-Dec-2019
Changes from 4.0.0-rc3
1) Compiler updated to gcc 6.3.1
2) Solo default parameters updated
3) Bug Fix to RCMAP channel number sanity check
------------------------------------------------------------------
Copter 4.0.0-rc3 16-Dec-2019
Changes from 4.0.0-rc2 
1) Flight mode and control improvements:
   a) Auto mode Takeoff getting stuck fixed (very rare)
   b) AutoTune protection against ESC sync loss at beginning of a twitch
   c) SystemID mode parameters hidden by default (set SID_AXIS to non-zero to see all)
   d) Takeoff from slanted surfaces improved by reducing I-term build-up in attitude controllers 
2) Lua Script related enhancements:
   a) MAV FTP support to ease uploading and downloading Lua scripts
   b) NeoPixel/WS2812 LED control from Lua scripts
   c) Pre-arm check that Lua scripting feature has enough memory
3) TradHeli enhancements:
   a) Autonomous autorotation (compile time option, not available in stable firmware)
   b) CCW Direct Drive Fixed Pitch tail support (see H_TAIL_TYPE parameter)
   c) Parameter description improvements
   d) STAB_COL_1/2/3 param range changed to 0 to 100 (was 0 to 1000)
4) Lightware SF40c driver for latest sensors with "streaming" protocol
5) Board/Frame specfic fixes:
   a) Hex CubeOrange IMU heater control gain tuning improvement
   b) Holybro Durandal IMUs ordering fix so EKx_IMU_MASK bits are intuitive
   c) Holybro Pixhawk4 B/E LED fix (was flickering)
   d) MatekF765 PWM outputs 5 and 6 now functional
   e) MatekF765, MatekF405 time wrap CRITICAL fix for vehicles flying more than 72min
   f) MatekF765 LED fixes
   g) mRobotics ControlZeroF7 I2C bus numbering fix
   h) Solo default params updated for 4.0.0
   i) Bootloaders for boards using STM32H7 CPUs
   j) Bootloader protection against line noise that could cause the board to get stuck during bootup
   k) LED timing fix for FMUv5 boards with LEDs on one of first four auxiliary PWM outputs
6) Minor Enhancements and Bug Fixes
   a) I2C storm High level protection
   b) Internal errors (like I2C storms) reported to GCS by setting Heartbeat status to Critical
   c) Dataflash Logging CTUN.TAlt and SAlt scaling fixed
   d) DO_DIGICAM_CONTROL messages are sent using Mavlink1/2 as specified by SERIALx_PROTOCOL
   e) DO_SET_SPEED sanity check fixed to protect against negative speeds
   f) FrSky telemetry scheduling improvement (fixes issue in which GPS data would not be sent)
   g) GPS auto configuration fix for non-Ublox-F9 (F9 config was interfering with other Ublox GPSs)
   h) Landing gear DEPLOY message fix (could be displayed multiple times or when gear not configured)
   i) Pre-arm check of position estimate when arming in Loiter even if arming is "forced"
   j) Pre-arm check that Terrain database has enough memory
   k) RangeFinder parameter conversion fix (some parameters were not upgraded from 3.6.x to 4.0.0)
   l) RangeFinder TYPE parameter descriptions clarified for LidarLite and Benewake Lidars
   m) Serial parameters hidden if they do not exist on the particular board
   n) UAVCAN GPS fix for GPSs that don't provide "Aux" message (like the Here2)
   o) NMEA Output bug fix (output was stopping after 90 seconds)
------------------------------------------------------------------
Copter 4.0.0-rc2 04-Nov-2019
Changes from 4.0.0-rc1
1) Failsafe changes:
   a) GCS failsafe triggers when telemetry connection is lost (previously only triggered when using joystick)
   b) FS_OPTION parameter allows continue-in-auto and continue-in-pilot-modes for RC/Radio and GCS failsafe
2) Dynamic Notch Filter supports filter range based on BLHeli ESC feedback
3) Improved protection against high roll/pitch gains affecting altitude control
4) Bug Fixes:
   a) Altitude control param conversion fix (PSC_ACCZ_FILT converted to PSC_ACCZ_FLTE)
   b) BLHeli fix to RPM calcs and telemetry (aka 0x8000 error)
   c) ChibiOS SPI timeout fix (non-critical)
   d) Fence upload is less strict about altitude types (fences don't have altitudes)
   e) Motor loss detection bug fix (would sometimes think the wrong motor had failed)
   f) Pre-arm message fix to reports AHRS/EKF issue (was blank)
   g) Sparky2 autopilot firmware available
   h) Startup message "AK09916: Unable to get bus semaphore" removed (was not an error)
   i) Yaw control fix for fast descent and after large attitude disturbances
------------------------------------------------------------------
Copter 4.0.0-rc1 25-Oct-2019
Changes from 3.6.11
1) Path Planning for Object Avoidance (aka Bendy Ruler and Dijkstra's) replaces "Dodge" avoidance
2) Complex Fence support (aka stay-out zones)
3) Lua scripting support on the flight controller
4) New flight controllers:
    a) Hex Cube Orange
    b) Holybro Durandal
5) Attitude Control changes:
    a) Attitude Control filters on target, Error and D-term (see ATC_RAT_x_FLTT/FLTE/FLTD)
    b) Harmonic notch filter
6) Flight mode changes:
    a) ZigZag mode (designed for crop spraying) 
    b) SystemId for "chirping" attitude controllers to determine vehicle response
    c) StandBy mode for vehicles with multiple flight controllers
    d) SmartRTL provides warnings when buffer is nearly full
    e) Follow mode offsets reset to zero when vehicle leaves follow mode
    f) Upward facing surface tracking using lidar
    g) Circle mode points more accurately towards center
7) Traditional Heli:
    a) Removed the parameter H_LAND_COL_MIN and functionality now uses H_COL_MID. CAUTION: ensure H_COL_MID is set to collective blade pitch that produces zero thrust
    b) Incorporated a rotor speed governor in rotor speed control (RSC)
    c) Moved all RSC parameters to the RSC library
    d) Converted throttle curve parameters to percent
    e) Converted RSC_CRITICAL, RSC_IDLE, and RSC_SETPOINT to percent
    f) Created swashplate library that has presaved swashplate types for use with Heli_Single and Heli_Dual frames
    g) Motor interlock with passthrough settable through RC option feature 
    h) Removed collective too high pre-arm check
    i) Added virtual flybar for Acro flight mode
    j) Fixed H_SV_MAN minimum and maximum settings for Heli_Dual
8) Frames:
    a) BiCopter support
    b) OctoV mixing improvements 
9) RC input/output changes:
    a) Serial protocols supported on any serial port
    b) IBUS R/C input support
    c) DO_SET_SERVO and manual passthrough can operate on the same channel
10) Battery improvements:
    a) Up to 10 batteries can be monitored
    b) "Sum" type consolidates monitoring across batteries
    c) Fuel flow battery (for use with gas tanks)
11) Sensor/Accessory changes:
    a) Robotis servo support
    b) KDECAN ESCs
    c) ToshibaCAN ESCs
    d) BenewakeTF03 lidar
    e) SD Card reliability improvements (if card removed, logging restarts)
    f) Yaw from some GPS (including uBlox RTK GPS with moving baseline)
    g) WS2812 LEDs (aka NeoPixel LEDs)
    h) NTF_BUZZ_VOLUME allows controlling buzzer volume
12) Landing Gear:
    a) Retracts automatically after Takeoff in Auto completes
    b) Deployed automatically using SRTM database or Lidar
13) UAVCAN improvements:
    a) dynamic node allocation
    b) SLCAN pass-through
    c) support for UAVCAN rangefinders, buzzers, safety switch, safety LED
14) Serial and Telemetry:
    a) MAVLink Message-Interval allows reducing telemetry bandwidth requirements
    b) SERIALn_OPTIONS for inversion, half-duplex and swap 
15) Safety Improvements:
    a) Vibration failsafe (switches to vibration resistant estimation and control)
    b) Independent WatchDog gets improved logging
    c) EKF failsafe triggers slightly more quickly  
------------------------------------------------------------------
Copter 3.6.12 13-Dec-2019 / 3.6.12-rc1 06-Dec-2019
Changes from 3.6.11
1) More ChibiOS fixes for I2C storm 
2) COMPASS_SCALE param to allow manual correction of compass scaling
------------------------------------------------------------------
Copter 3.6.11 01-Oct-2019 / 3.6.11-rc1 16-Sep-2019
Changes from 3.6.10
1) EKF and IMU improvements:
    a) IMU3 enabled by default if present
    b) IMU3 fast sampling enabled by default on Cube autopilots
    c) EKF protection against large baro spikes causing attitude error
    d) EKF origin fixes (consistent across cores, set externally only when not using GPS)
    e) EKF logging of 3rd core
2) Minor enhancements:
    a) Land mode supports heading requests (ie. ROI)
    b) Support Hexa-H frame
    c) MatekF405-STD binaries created
    d) Benewake TFminiPlus lidar support
3) Bug Fixes
    a) Barometer health checks include sanity check of temperature
    b) Lightware serial driver handles invalid distances
    c) IO firmware fix involving delayed writes to serial ports (ChibiOS only)
    d) CAN Compass fis to for unintialised device IDs
    e) mRo x2.1-777 USB ID fix
    f) ChibiOS fix for I2C storm
------------------------------------------------------------------
Copter 3.6.10 29-Jul-2019 / 3.6.10-rc2 2-Jul-2019
Changes from 3.6.10-rc1
1) mRobotics ControlZeroF7 board support
2) Motor Test fixed by removing delay that triggered CPU Watch Dog
------------------------------------------------------------------
Copter 3.6.10-rc1 08-Jul-2019
Changes from 3.6.9
1) EKF improvements:
    a) learns biases even for inactive IMUs
    b) EKF uses earth magnetic field model to reduce in-flight compass errors
    c) EKF switches to first healthy IMU when disarmed
    d) IMU fix for dropped samples during high CPU usage
    e) Optical flow fusion start fix when some gyros disabled
2) Ublox F9 GPS support
3) Integrated CPU Watch Dog for STM boards including logging of reset reason
4) ChibiOS I/O firmware for ChibiOS builds to support Spektrum binding
5) Auxiliary switch changes always logged
6) CUAVv5 Nano LED fix
7) Solo Gimbal fix when some gyros disabled
------------------------------------------------------------------
Copter 3.6.9 27-May-2019 / 3.6.9-rc1/rc2 30-Apr-2019
Changes from 3.6.8
1) CX-OF flow sensor enabled on all boards
2) CUAVv5 Nano board support added
3) RangeFinders supported on all I2C ports
4) Compass maximum acceptable offsets increased
5) Septentrio GPS driver loses logging of camera feedback which could cause lag
6) Bug Fixes:
    a) Acro mode leveling fix for when ACRO_TRAINER was 1
    b) Tradheli collective position pre-arm check removed
    c) Fallback to microSD for storage fixed
    d) GPS blending memory access fix that could lead to unhealthy GPS
    e) fixed POWR flags for 2nd power module on fmuv3 boards
7) bootloader binary update for many boards
------------------------------------------------------------------
Copter 3.6.8 26-Apr-2019 / 3.6.8-rc1 24-Apr-2019
Changes from 3.6.7
1) Safety fixes
    a) Prevent loss of active IMU from causing loss of attitude control
    b) Added startup check for Hex CubeBlack sensor failure
    c) don't reset INS_ENABLE_MASK based on found IMUs
------------------------------------------------------------------
Copter 3.6.7 06-Mar-2019 / 3.6.7-rc1 28-Feb-2019
Changes from 3.6.6
1) Bug fixes and minor enhancements
    a) Matek405-Wing compass fix (thread initialisation fix)
    b) Landing flips reduced by reducing I-term build-up
    c) Land mission command slows down less aggressively
    d) Lightware LW20 lidar more reliably switches to serial mode 
------------------------------------------------------------------
Copter 3.6.6 15-Feb-2019 / Copter 3.6.6-rc2 09-Feb-2019
Changes from 3.6.6-rc1
1) Bug fixes
    a) EKF compass switching fix for vehicles with 3 compasses
    b) CAN fixed on Pixhawk4 and PH4-mini
    c) Mini-pix uart telem1 and telem2 reversed
    d) Divide-by-zero protection if _FILT_HZ params set to zero
    e) Guided_NoGPS skips GPS pre-arm check
------------------------------------------------------------------
Copter 3.6.6-rc1 02-Feb-2019
Changes from 3.6.5
1) AP_RSSI fixes and minor enhancements
    a) RSSI pwm input driver for ChibiOS
    b) add V5 Nano to RSSI_ANA_PIN parameter description
2) Bug fixes and minor enhancements
    a) TradHeli roll trim ramp time fix
    b) First Spline waypoint no longer skipped
    c) RC override (from GCS Joystick) disable fix (command to disable overrides could be interpreted as real input)
------------------------------------------------------------------
Copter 3.6.5 24-Jan-2019 / 3.6.5-rc3 21-Jan-2019
Changes from 3.6.5-rc2
1) Bug fixes:
    a) Pixhawk4 SBUS input and Serial6 pin mapping conflict resolved
------------------------------------------------------------------
Copter 3.6.5-rc2 15-Jan-2019
Changes from 3.6.5-rc1
1) Bug fixes and minor enhancements
    a) RC DSM sync bug fix (channel 8 could temporarily become zero, ChibiOS only)
    b) Pixhawk board LED fix (ChibiOS only)
------------------------------------------------------------------
Copter 3.6.5-rc1 11-Jan-2019
Changes from 3.6.4
1) Bug fixes and minor enhancements
    a) SD Card reliability improvement (BRD_SD_SLOWDOWN param) and allow re-inserting card after boot
    b) Mode and Auxiliary switch range check to protect during FrSky SBUS failsafe recovery
    c) Follow mode reports target distance and heading to ground station
    d) Pixhawk4-mini safety switch and LED fix
    e) Bebop2 build fix
2) RC protocol decoding for SRXL, SUMD and ST24 extended to all boards including pixracer and ChibiOS-only boards
3) DrotekP3 Pro support
4) Cube Purple support
------------------------------------------------------------------
Copter 3.6.4 27-Dec-2018 / 3.6.4-rc1 20-Dec-2018
Changes from 3.6.3
1) Bug fixes and minor enhancements
    a) Detect and clear stuck I2C bus when using ChibiOS
    b) Pixhawk4 fix for battery monitor voltage scaling
    c) TradHeli Dual (aka Chinook) fix for scaling of second swashplate movement
    d) Omnibus F7 IMU orientation fix
    e) Gimbal fix to avoid extreme movement if pilot's transmitter is off at startup 
    f) Follow mode fix to obey FOLL_SYSID parameter (controls which vehicle to follow)
2) Cheerson CX-OF optical flow sensor support
------------------------------------------------------------------
Copter 3.6.3 04-Dec-2018 / 3.6.3-rc1 01-Dec-2018
Changes from 3.6.2
1) Bug fixes and minor enhancements
    a) Pixhawk4 RC input fix
    b) TradHeli parameter default fixes for Loiter and PosHold
    c) Onboard OSD fix for MatekSys 405
    d) Solo power off button delay time increased
------------------------------------------------------------------
Copter 3.6.2 24-Nov-2018 / 3.6.2-rc4 22-Nov-2018
Changes from 3.6.2-rc3
1) Bug fixes and minor changes:
    a) Benewake TFmini short range scaling fix
    b) Fix alt drop after quick switch from RTL to stabilize to RTL
    c) Winch removed from build by default
------------------------------------------------------------------
Copter 3.6.2-rc3 19-Nov-2018
Changes from 3.6.2-rc2
1) Bug fixes:
    a) Benewake TFmini and TF02 driver reliability fix
    b) Spektrum RC parsing fix when more than 7 channels
2) remove bootloader from px4-v2 builds (reduces firmware size)
------------------------------------------------------------------
Copter 3.6.2-rc1/rc2 15-Nov-2018
Changes from 3.6.1
1) Bug fixes:
    a) Benewake TFmini and TF02 driver checksum fix (was missing many sensor readings)
    b) Range finders report healthy to GCS when out-of-range
    c) RPM sensor reliability fix by initialising analog input pin
------------------------------------------------------------------
Copter 3.6.1 10-Nov-2018 / 3.6.1-rc1 06-Nov-2018
Changes from 3.6.0
1) Garmin LidarLite V3HP support 
2) VFR HUD messages send relative altitude if DEV_OPTIONS = 2.  Useful for older OSDs and GCSs
3) Bug fixes:
    a) Battery failsafe voltage parameter conversion fix
    b) Safety switch startup fix (was occasionally not initialised properly)
    c) Benewake TFmini and TF02 driver discards distances over 327m (avoids reporting 655m when can't read distance)
    d) Dataflash erase only availabled when disarmed (avoids crash if user attempted to erase logs while flying)
4) ChibiOS fixes and enhancements:
    a) Pixracer LEDs colours fixed
    b) Terrain support fixed on Pixracer, MindPx-v2, Radiolink mini-pix
    c) RC input processing fix to avoid memory corruption in some rare cases
    d) FuriousFPV F-35 Lightning board support
    e) SpeedyBee F4 board support
    f) Bootloaders for OmnibusF4v6, mRoX2.1-777, Radiolink mini-pix 
    g) Revo-mini support for external barometer
    h) Pins numbers made consistent across boards (setup of some features now more consistent across boards)
    i) enable safety switch on Pixhawk family f7 boards
------------------------------------------------------------------
Copter 3.6.0 26-Oct-2018
Changes from 3.6.0-rc12
1) Bug Fixes:
    a) Object avoidance fix when using 360 Lidar (mini fence was not including first distance)
    b) Flowhold mode descending fix
2) ChibiOS related fixes:
    a) Kakutef7 support pin mapping fixes
    b) PixRacer LED fix
    c) I2C Lidar driver fix to avoid freeze at startup if sensor not connected
------------------------------------------------------------------
Copter 3.6.0-rc12 05-Oct-2018
Changes from 3.6.0-rc11
1) Bug Fixes:
    a) DSHot ESC driver race condition fix (could result in board freezing up)
    b) EKF fix to rounding off home altitude (could result in slightly inaccurate home altitude)
    c) Pixracer safety switch fix (BRD_SAFETYOPTION parameter was handled incorrectly)
------------------------------------------------------------------
Copter 3.6.0-rc11 01-Oct-2018
Changes from 3.6.0-rc10
1) Bug Fixes:
    a) PosHold fix to initialise desired accelerations
    b) Automatic compass orientation fix to account for equivalent rotations
    c) ChibiOS fix to RSSI voltage reading
------------------------------------------------------------------
Copter 3.6.0-rc10 15-Sep-2018
Changes from 3.6.0-rc9
1) Arming/disarming from transmitter can be disabled with ARMING_RUDDER parameter
2) ChibiOS fixes:
    a) UART baud rate rounding fix (allow 921600 baud to work)
    b) ADC fix to allow more simultaneous channels
3) Bug Fixes:
    a) Lean angle limit (to maintain altitude) always allows at least 10deg of lean
    b) Attitude Control and PID initialisation fix to reduce twitches when switching modes
------------------------------------------------------------------
Copter 3.6.0-rc9 08-Sep-2018
Changes from 3.6.0-rc8
1) Landing gear can be operated using AIRFRAME_CONFIGURATION mavlink messages
2) Support playing tunes received via MAVLink (i.e. from GCS)
3) Bug Fixes:
    a) Twitch reduced when entering Loiter at high speed
    b) Safety switch state fix when Pixhawk/Cube I/O board reset in flight
    c) External display fix to allow plugging into either I2C port
------------------------------------------------------------------
Copter 3.6.0-rc8 28-Aug-2018
Changes from 3.6.0-rc7
1) TeraRanger Tower EVO lidar support added
2) PCA9685 and NCA5623 LED support added (replacements for Toshiba LED that is becoming end-of-life)
3) Missions can start with DO_ commands (first NAV command must still be takeoff)
4) ChibiOS fixes:
    a) DShot and BLHeli stability improvements
    b) added OmnibusNanoV6 bootloader
5) Bug fixes:
    a) AP_Stat only saves values to eeprom if they change (reduces CPU impact)
    b) boost throttle respects safety switch
    c) TradHeli reduce chance of tip-over when landed in Loiter
------------------------------------------------------------------
Copter 3.6.0-rc7 01-Aug-2018
Changes from 3.6.0-rc6
1) Compass rotation detection during calibration (see COMPASS_AUTO_ROT param)
2) Tone alarm system consistent on all boards and support for playing tones from GCS
3) OnBoard OSD improvements including showing dir to home (ChibiOS only)
4) External compass support and microSD improvements for many ChibiOS-only boards including Kakute F4, Mateksys F405-Wing, Airbot F4, Crazyflie2, MindPXv2, Mini-PIX, Omnibus F7
5) Bug fixes:
    a) Battery monitor parameter fix to support BLHeli ESCs
    b) Ch12 auxiliary function fixed
    c) compassmot fix
    d) Pixracer external compass fix
    e) Pre-arm failure regarding RCx_TRIM removed for non critical channels
    f) SBUS failsafe fix when receiver configured to "hold"
    g) UART flow control fixes (ChibiOS only)
    h) VL53L0X lidar fix
6) Minor enhancements:
    a) Compass device IDs (i.e. COMPASS_DEVID) set to zero if no compass detected
    b) Fence action "Always Land" option added
    c) Motor test maximum time extended to 10min
    d) Receiver RSSI pwm input on AUX5,6
    e) USB Serial port protocol default set to Mavlink2
7) TradHeli fixes:
    a) "Channel already assigned" message fixed
    b) External gyro output method PWM based
    c) Swash servo output properly accounts for servo trim
------------------------------------------------------------------
Copter 3.6.0-rc6 16-Jul-2018
Changes from 3.6.0-rc5
1) ChibiOS small enhancement and fixes:
    a) KakuteF4 UART order fix
    b) mRobotics bootloader and autobuild of binaries for AUAV2.1 and Pixracer
    c) Mateksys F405-Wing uart reorder to better match labels on board
2) Improve telemetry/GPS detection by only clearing UART when baud rate changes
------------------------------------------------------------------
Copter 3.6.0-rc5 07-Jul-2018
Changes from 3.6.0-rc4
1) Support new boards CubeBlack and HolyBro Pixhawk4-mini
2) ChibiOS small enhancement and fixes:
    a) make board serial number match NuttX
    b) relay fix for Pixhawk boards by defaulting BRD_PWM_COUNT to 4 (same as NuttX)
    c) MatekF405-Wing default compass orientation fix
    d) Pixhawk4 default compass orientation fix
    e) Omnibus F4 Pro I2C fix
3) Bug fixes:
    a) Telemetry baud rate fix
    b) RC failsafe fix to RTL in all modes except Auto when FS_THR_ENABLE is continue-with-mission
    c) waypoint navigation related safety check that PSC_POS_XY_P is non-zero 
    d) aerofc-v1 update bootloader utility fix
------------------------------------------------------------------
Copter 3.6.0-rc4 05-Jul-2018
Changes from 3.6.0-rc3
1) add support for Cube Black and Cube Orange
2) Built-in OSD improvements including home direction fix, altitude fix
3) Bug fixes:
    a) ESC Telemetry current scaling fix
    b) LED pre-arm GPS colour fix if glitch occurs
------------------------------------------------------------------
Copter 3.6.0-rc3 02-Jul-2018
Changes from 3.6.0-rc2
1) ChibiOS:
    a) airbotf4, matekF405, matekF405-wing, omnibusf4pro, mini-pix, omnibusf7v2, revo-mini, sparky2 binaries built on firmware.ardupilot.org
    b) STM32F7 support including the CUAV v5 Autopilot and the PixHawk 4 (aka fmuv5)
    c) fixed dataflash logfile dates
    d) bootloader included in firmware for easier uploading
    e) additional UART added to fmuv4 and fmuv5 (STM32 F7 boards)
    f) ChibiOS version written to dataflash log files
2) TradHeli
    a) fix collective jump on rotor shutdown in AltHold and Loiter
    b) fix interlock pre-arm check
3) Follow uses relative altitudes (FOLL_ALT_TYPE param allows specifying relative or absolute alts)
4) Auxiliary switch de-bounced to avoid false positives
5) Bug fixes and minor enhancements:
    a) ESC calibration fix
    b) ESC_TELEMETRY messages sent to ground station
------------------------------------------------------------------
Copter 3.6.0-rc2 01-Jun-2018
Changes from 3.6.0-rc1
1) ChibiOS support for Holybro Kakute F4
2) Range finders:
    a) Benewake TFmini and TF02 support
    b) WASP200 laser range finder support
3) D-Shot and ESC telemetry support
4) Vision-position-estimate support for use with ROS
5) Bug fixes and minor enhancements:
    a) TradHeli swash plate servo trim fixes
    b) SmartRTL gives pilot yaw control
    c) marvelmind reliability improvements
    d) CoaxCopter and SingleCopter output fixes
    e) Maxbotics I2C sonar health fix
6) Safety fixes/improvements:
    a) GPS/EKF failsafe disabled in Acro/Stabilize modes
    b) interlock switch arming check fixed
    c) Auxiliary switch de-bouncing
    d) pre-arm check for GPS/AHRS difference changed to 2D (to avoid false positives)
    e) startup crash fixed when VL53L0X lidar configured but not connected
------------------------------------------------------------------
Copter 3.6.0-rc1 19-Apr-2018
Changes from 3.5.5
1) ChibiOS support including F4BY, OpenPilot Revo Mini, TauLabs Sparky2 boards
2) Flight mode changes:
    a) New Loiter with faster roll and pitch response, braking
    b) SmartRTL (returns home using outgoing path)
    c) Follow mode (allows following without relying on ground station commands)
    d) FlowHold (position hold with optical flow without lidar)
3) Battery failsafe allows two stages of actions and monitoring of multiple batteries
4) New sensors/drivers:
    a) Marvelmind for Non-GPS navigation
    b) RPLidarA2/A3 for object avoidance
    c) Winch for lowering packages without landing
    d) Septentrio GPS driver robustness improved
5) New frames:
    a) dodeca-hexa copter (12 motors on 6 arms)
    b) Variable pitch quadcopter support (aka HeliQuad)
    c) multicopters with vertical boosting motor (see MOT_BOOST_SCALE parameter)
6) Miscellaneous changes and enhancements:
    a) Auxiliary switch to enable/disable RC overrides from ground station joystick
    b) Barometer temperature calibration learning (see TCAL_ENABLED parameter)
    c) Camera triggering by distance only in Auto mode if CAM_AUTO_ONLY set to 1
    d) Compass noise rejection using COMPASS_FLTR_RNG parameter
    e) Compass interference compensation for up to 4 motors (see COMPASS_PMOT parameters)
    f) Command Line Interface (aka CLI) removed to reduce firmware size
    g) Flight mode channel can be changed using FLTMODE_CH parameter
    h) IMU notch filter to ignore specific frequencies (use INS_NOTCH_ENABLE parameter)
    i) LAND_ALT_LOW parameter allows setting altitude at which vehicle slows to LAND_SPEED in Land and RTL modes
    j) Mission cleared automatically on each reboot if MIS_OPTIONS parameter set to 1
    k) Object avoidance configurable to "stick" to barriers (previously would always slide past)
    l) PILOT_SPEED_UP, PILOT_SPEED_DN allow different max climb and descent rates in AltHold, Loiter, PosHold
    m) Safety switch configurable (i.e. only allow arming but not disarming via switch)
    n) Servo output rate configurable (see SERVO_RATE parameter)
    o) Vehicle rotation rate limits (see ATC_RATE_P/R/Y_MAX parameters)
    p) SI units in parameters, mavlink messages and dataflash logs
7) TradHeli improvements:
    a) H3-135/140 swashplate support
    b) Reversed collective option for leading/trailing edge control
    c) AltHold angle limiter fix
    d) Five point spline-smoothed throttle curve 
8) parameter and log message name changes:
    a) ACCEL_Z_ renamed to PSC_ACCZ_
    b) ACC_ parameters renamed to PSC_ACC
    c) POS_ parameters renamed to PSC_POS
    d) VEL_ parameters renamed to PSC_VEL
    e) FS_BATT_ renamed to BATT_FS_
    f) BATT_AMP_PERVOLT renamed to BATT_AMP_PERVLT
    g) PILOT_VELZ_MAX to PILOT_SPEED_UP (and PILOT_SPEED_DN)
    h) RC_FEEL_RP replaced with ATC_INPUT_TC (functionality is same, scaling is different)
    i) WPNAV_LOIT_ params renamed to LOIT_    
    j) NTUN dataflash message renamed to PSC
------------------------------------------------------------------
Copter 3.5.7 17-Jul-2018 / 3.5.7-rc1 12-Jul-2018
Changes from 3.5.6
1) Bug fix to ESC calibration safety switch issue 
------------------------------------------------------------------
Copter 3.5.6 11-Jul-2018 / 3.5.6-rc1 07-Jul-2018
Changes from 3.5.5
1) Safety switch fix to refuse presses once armed (BRD_SAFETYOPTION controls behaviour) 
------------------------------------------------------------------
Copter 3.5.5 31-Jan-2018 / 3.5.5-rc1 24-Jan-2018
Changes from 3.5.4
1) ICM20948 compass orientation fix
2) LIS3MDL compass support on Pixracer
3) support for easy embedding of defaults in firmware binary
4) do-mount-control control command sets copter yaw when using 2-axis gimbal
5) TradHeli fix for direct drive variable pitch tail rotors
------------------------------------------------------------------
Copter 3.5.4 23-Nov-2017 / 3.5.4-rc2 17-Nov-2017
Changes from 3.5.3
1) Compass improvements / bug fixes:
    a) probe for LIS3MDL on I2C address 0x1e
    b) bug fix pixracer compass detection
------------------------------------------------------------------
Copter 3.5.4-rc1 08-Nov-2017
Changes from 3.5.3
1) Compass improvements:
    a) support added for QMC5883L, LIS3MDL, IST8310
    b) COMPASS_TYPEMASK parameter allows enabling/disabling individual compass drivers
2) LightWare and MaxBotix range finders supported on both I2C buses
3) Serial port 5 can be used for telemetry or sensors (previously was only for NuttX console)
4) px4pro flight controller supported
5) TradHeli fixes:
    a) motor runup check applies to all flight modes (previously only loiter, poshold, althold)
    b) swashplate behaviour changes when on ground in acro, stabilize and althold
    c) servo test function fixed
    d) direct drive fixed pitch tail fix
    e) Z-axis Accel P gain default lowered to 0.3
6) EKF origin set using SET_GPS_GLOBAL_ORIGIN mavlink message instead of SET_HOME_POSITION
7) Intel Aero supports 921600 baud rate between main flight controller and companion computer
8) Bug fix to I2C race condition on Pixhawk that could occasionally cause I2C device to not be detected
------------------------------------------------------------------
Copter 3.5.3 21-Sep-2017 / Copter 3.5.3-rc1 12-Sep-2017
Changes from 3.5.2
1) Guided mode support yaw and yaw-rate fields from set-position-target message
2) Optical Flow startup retries 10 times 
3) Bug fixes:
    a) Septentrio RTK GPS driver robustness improvements for long messages
    b) dataflash not-logging checks if not initialised
    c) fix reporting of relative-position-NE-to-home (most users will not notice this difference) 
------------------------------------------------------------------
Copter 3.5.2 14-Aug-2017 / Copter 3.5.2-rc1 05-Aug-2017
Changes from 3.5.1
1) GPS glitch arming check and notification (flashing blue-yellow LED, message on HUD)
2) Arming check for minimum voltage (see ARMING_VOLT_MIN parameter)
3) Landing Gear startup behaviour now configurable (see LGR_STARTUP parameter)
4) Solo LED fix when using "boat" mode
5) Intel Aero RTF gets one more I2C bus
6) Bug fixes:
    a) resolve barometer floating point exception when ground pressure set to be negative
    b) resolve freeze if SERVOx_FUNCTION is set above 80   
------------------------------------------------------------------
Copter 3.5.1 31-July-2017 / 3.5.1-rc1 24-July-2017
Changes from 3.5.0
1) fix to RC input corruption when using Spektrum DSMx with Pixracer 
------------------------------------------------------------------
Copter 3.5.0 17-July-2017 / 3.5.0-rc11 12-July-2017
Changes from 3.5-rc10
1) GPS fixes:
    a) Re-enable auto detection of NMEA GPS (revert change from -rc9)
    b) Fix SBF race condition on start (this should resolve SBF GPS detection issues for some users)
2) SF40c driver fix to ignore zero distances
------------------------------------------------------------------
Copter 3.5.0-rc10 08-July-2017
Changes from 3.5-rc9
1) Intel Aero build and default parameter updates
2) EKF final yaw reset lowered from 5m to 2.5m to speed up bad compass detection after takeoff
------------------------------------------------------------------
Copter 3.5.0-rc9 03-July-2017
Changes from 3.5-rc8
1) AutoTune improvement when using position hold (twitches in body-frame instead of earth-frame)
2) GPS fixes:
    a) RTK GPS fix when large RTK correction messages are sent
    b) auto-detection for NMEA disabled (NMEA users must explicitly set GPS_TYPE to 5)
3) EKF bug fixes:
    a) fix bug preventing use of optical flow for terrain height estimation
    b) fix bug affecting accuracy of optical flow estimated height during climb/descent
    c) fix range beacon fusion bugs
4) return accel filter (INS_ACCEL_FILT) to 20hz (some users were seeing oscillations at 10hz)
------------------------------------------------------------------
Copter 3.5.0-rc8 15-Jun-2017
Changes from 3.5-rc7
1) Solo on Master:
    a) add/fix tones and LED support
    b) resolve dataflash health reporting issue
2) Allow flight controller to boot even if critical sensors fail (previously stopped at startup and displayed message people could rarely see)
3) Minor enhancements:
    a) Landing Gear retracts/deploys only when pilot first moves switch (previously would always deploy on startup)
    b) BeagleBoneBlue supports external compass, OLED display 
4) Bug fixes:
    a) increase pilot stick dead zones for roll, pitch and yaw (still slightly more narrow than AC3.4)
    b) increase default roll, pitch and yaw rate IMAX values to 0.5, reduce default yaw rate filter to 2.5hz
    c) reduce accel filter from 20hz to 10hz (smoothes throttle in AltHold)
    d) fix EKF declination fusion
    e) object avoidance fix to ignore old distance measurements and out-of-range distances from range finder
    f) fix poxyz west-east processing (it was backwards)
------------------------------------------------------------------
Copter 3.5-rc7 25-May-2017
Changes from 3.5-rc6
1) Bug fixes and minor enhancements:
    a) fix OneShot ESC calibration and compass-mot calibration
    b) fix Navio2 and ErleBrain crashes caused by insufficient memory allocated to scheduler
    c) AutoTune with position hold only resets target position when roll and/or pitch sticks moved
    d) fix Acro, Sport trainer mode to stop attitude targets going beyond angle limits
    e) sanity check WPNAV_RADIUS is non-zero
    f) MarvelMind driver fixes (still a work-in-progress)
    g) Sanity check command when resuming mission in Auto
------------------------------------------------------------------
Copter 3.5-rc6 15-May-2017
Changes from 3.5-rc5
1) Bug fixes and minor enhancements:
    a) ESC calibration fix when not using safety switch (was waiting for safety switch to be pushed)
    b) Parameter loading improvements (responds sooner after startup, counts parameters in separate thread)
    c) Pilot radio passthrough fix for tradheli, single copter, coax copter
------------------------------------------------------------------
Copter 3.5-rc5 03-May-2017
Changes from 3.5-rc4
1) Intel Aero RTF support
2) Smart battery fetches serial number, temperature and individual cell voltages
3) Vehicle heads towards target point instead of next waypoint during straight line waypoint mission commands in AUTO
4) Visual Odometry supported using VISION_POSITION_DELTA mavlink message
5) Do-Set-Home sets EKF origin allowing user to specify vehicle starting position without GPS
6) Object avoidance parameter (AVOID_MARGIN) allows configuring distance to maintain from objects
7) Motor output can be duplicated to multiple channels
8) IRLock can be connected to any I2C bus
9) Piksi "Multi" GPS support
10) Performance Improvements:
    a) inter-EKF scheduling cooperation (improves load balancing when using multiple EKFs)
    b) parameter requests from GCS processed asynchronously (resolves bad flight behaviour when fetching params in-flight) 
    c) display performance improvements
1) Bug fixes including:
    a) LSM303D (Pixhawk's backup IMU) timing fixes
    b) auxiliary PWM outputs update even when RC input stops
    c) dataflash log over mavlink fixes
    d) EKF recovery from severe errors
    e) reduce twitch when transitioning from spline to straight line waypoints in AUTO
    f) enable motors after initialisation completes (resolves some BLHeli ESC initialisation and calibration issues)
------------------------------------------------------------------
Copter 3.5-rc4 08-Apr-2017
Changes from 3.5-rc3
1) Bug fixes including:
    a) Baro based altitude estimate fixes including limiting ground temperature to reasonable value
    b) Fix 0.6ms lag reduction
    c) Fix Invensense IMU driver temperature reading
    d) Fix semaphore take during interrupts that may have caused very occasional main loop rate issues
    e) Return Home if GCS asks for first (i.e. zero-ith) mission command (required for Solo)
------------------------------------------------------------------
Copter 3.5-rc3 24-Mar-2017
Changes from 3.5-rc2
1) OLED Display fixes:
    a) can be connected to either I2C bus
    b) last character on line correctly cleared
2) bug fix to detect SBUS failsafe on Pixracer
3) bug fix to Gimbal servo reversing
4) ensure WPNAV_LOIT_SPEED is non-zero to avoid divide-by-zero
5) Fence pre-arm check failure messages clarified 
------------------------------------------------------------------
Copter 3.5-rc2 13-Mar-2017
Changes from 3.5-rc1
1) Improved dual GPS support including blending (set GPS_TYPE2=1, GPS_AUTO_SWITCH=2)
2) AutoTune with position hold (enter AutoTune from Loiter or PosHold modes to enable light position hold)
3) New boards/drivers:
    a) PixhawkMini support
    b) AUAV2.1 board support (for AUAV2.1 set BRD_TYPE to 20 to distinguish from PixhawkMini)
    c) Garmin LidarLiteV3 support
    d) Maxell Smart Battery support
    e) Invensense ICM-20602 IMU and ST L3GD20H gyro drivers added
4) IMU to Motor lag reduced by 0.6ms
5) Object Avoidance:
    a) support TeraRanger Tower sensor
    b) support using normal (1-dimensional lidar/sonar)
6) Minor changes/enhancements:
    a) add MOT_SPOOL_TIME parameter to allow users to control spool-up time of motors
    b) remove Solo gimbal from -v2 because of 1MB Flash limit on many Pixhawk1s
    c) altitude check for Guided mode takeoff (must be at least 1m)
    d) auxiliary switch to arm/disarm vehicle (set CH7_OPT to 41)
    e) change battery alarm tone to reduce impact on IMU
    f) add battery monitor health reporting
    g) support DO-FENCE-ENABLE mission command to enable/disable fence during missions
7) Bug Fixes:
    a) OLED Display clears messages after 10seconds
    b) Servo Gimbal output range fix (could attempt to push servos beyond their limits)
    c) fix do-set-servo
    d) fix dataflash log time (was always appearing as 1970)
------------------------------------------------------------------
Copter 3.5-rc1 28-Jan-2017
Changes from 3.4.4
1) Multicopter firmware consolidated into single binary, users must set FRAME_CLASS parameter (1:Quad, 2:Hexa, 3:Octa, 4:OctaQuad, 5:Y6, 7:Tri)
2) PixRacer specific items:
    a) board LEDs supported
    b) ESC calibration start problem resolved (was sometimes not detecting high throttle from pilot)
3) Relaxed compass calibration and compass consistency checks
4) Sensor and Optional Hardware Improvements:
    a) IMU sampling rate for MPU9250, ICM20608 IMUs to 8kHz for improved vibration resistance (Pixracer, Pixhawk2)
    b) Onboard Display (http://ardupilot.org/copter/docs/common-display-onboard.html)
    c) Here+ RTK GPS (UBlox M8P RTK) support
    d) LightWare I2C drivers usable on Copter (thanks to in-tree drivers below)
    e) MaxBotix sonar with serial interface now supported
    f) Bebop optical flow (but sonar not yet operational making this feature currently impossible to use)
5) Precision Loiter using IRLock sensor (set CH7_OPT to 39)
6) Delivery improvements:
   a) Servo gripper (http://ardupilot.org/copter/docs/common-gripper-servo.html)
   b) Package_Place mission command (drops package without vehicle landing) 
7) Pozyx support for Non-GPS flight (http://ardupilot.org/copter/docs/common-pozyx.html)
8) EKF improvements:
    a) improved blending of optical flow and GPS
    b) EKF3 added in ride-along.  Enable by setting EK3_ENABLE=1
    c) improvements when using range finder as primary height reference
9) Object Avoidance improvements (http://ardupilot.org/dev/docs/code-overview-object-avoidance.html):
    a) uses mini-fence
    b) accepts MAVLink distance-sensor messages 
    c) simple avoidance in AltHold mode 
10) Other improvements:
    a) pre-arm check that logging is working
    b) better reporting of battery failsafe to ground stations
    c) reduced minimum waypoint speed to 20cm/s
    d) Flight time recorder (http://ardupilot.org/copter/docs/common-flight-time-recorder.html)
    e) UBlox GPS baud rate fix (was occasionally selecting wrong rate) 
11) Technical improvements (users may not see a functional change):
    a) in-tree drivers lead to improved sharing of drivers with Linux boards
    b) copter arming checks consolidated with other vehicles
------------------------------------------------------------------
Copter 3.4.6 15-Mar-2017
Changes from 3.4.5
1) ensure WPNAV_LOIT_SPEED is non-zero to avoid divide-by-zero
------------------------------------------------------------------
Copter 3.4.5 11-Feb-2017
Changes from 3.4.4
1) Bug fix to Guided mode that could lead to crash after take-off completes if MOT_THST_HOVER too high 
------------------------------------------------------------------
Copter 3.4.4 06-Jan-2017 / Copter 3.4.4-rc1 21-Dec-2016
Changes from 3.4.3
1) Bug Fixes / Minor Enhancements:
    a) prevent unwanted EKF core switching at startup
    b) helicopter prevented from taking off in position hold mode before rotors at full speed
    c) battery resistance calculations fixed
    d) avoid unnecessary parameter change notifications to ground station
    e) default power module definitions for Navio boards
    f) on-board compass calibration capability bit added (tells ground stations that onboard calibration is supported)
------------------------------------------------------------------
Copter 3.4.3 08-Dec-2016 / Copter 3.4.3-rc1 01-Dec-2016
Changes from 3.4.2
1) Bug Fixes:
    a) reduce unnecessary EKF core switching and fix position reset
    b) attitude control does not limit rate if ATC_ACCEL_MAX is zero
    c) ignore arm command from GCS if already armed
    d) set land-complete to false during auto takeoff 
    e) onboard compass calibration memory init fix that could lead to failure to complete
2) Minor Improvements:
    a) LeddarOne driver robustness improvements
    b) allow MOT_THR_MIX_MAX to be as high as 2.0
    c) uavcan bus settle time increased to 2sec
    d) helicopters do not hold position during auto takeoff until rotor is at full speed
------------------------------------------------------------------
Copter 3.4.2 16-Nov-2016 / Copter 3.4.2-rc1/rc2 07-Nov-2016
Changes from 3.4.1
1) Minor Improvements:
    a) ACRO_Y_EXPO applies to all flight modes (but is defaulted to zero)
    b) Autotune timeout increased (helps with large copters)
    c) AltHold filter applied to P (was previously only for D)
    d) arming check for compass health (was previously only a pre-arm check)
2) Bug Fixes:
    a) compass calibration (onboard) fix to return completion status
    b) LeddarOne driver busy-wait fix 
    c) SBF GPS altitude and accuracy reporting fix 
    d) MAV GPS uses existing configured baud rate instead of auto detecting 
------------------------------------------------------------------
Copter 3.4.1 01-Nov-2016
Changes from 3.4.0
1) Pixracer PPM RC input fix
------------------------------------------------------------------
Copter 3.4.0 31-Oct-2016
Changes from 3.4.0-rc7
1) Disabled LeddarOne driver (will be fixed in AC3.4.1 within a couple of weeks)
------------------------------------------------------------------
Copter 3.4.0-rc7 25-Oct-2016
Changes from 3.4.0-rc6
1) Bug fixes, minor improvements:
    a) more DSM binding fixes for pixracer
    b) allow non-motor channels to move before safety switch is pressed
    c) fix Bebop motor order
    d) fix MAVLink system id for first few messages
    e) fix throw mode throttle while rotating to level
------------------------------------------------------------------
Copter 3.4.0-rc6 15-Oct-2016
Changes from 3.4.0-rc5
1) Object Avoidance with SF40c 360 lidar
2) ACRO_Y_EXPO parameter allows exponential yaw in ACRO mode (slow yaw at mid stick but fast response at full stick)
3) ACRO_THR_MID parameter allows custom mid throttle for ACRO mode
4) Precision Landing reliability and accuracy improvements using mini EKF and adding IRLock lens distortion correction 
5) Bug fixes, minor improvements:
    a) fix DSM binding on Pixracer
    b) resolved position jump caused by EKF core change (also added logging and reporting to GCS)
    c) EKF failsafe while in LAND triggers non-GPS LAND
    d) small throttle jump removed when switching between AltHold, Loiter
6) Safety:
    a) EKF falls back to optical flow if GPS fails
    b) pre-arm check that GPS configuration has completed
------------------------------------------------------------------
Copter 3.4.0-rc5 14-Sep-2016
Changes from 3.4.0-rc4
1) Sprayer enabled by default
2) Bug fixes, minor improvements:
    a) EKF fix for yaw angle reset fix when switching between cores (AC3.4 runs multiple EKFs simultaneously)
    b) EKF fixes when fusing range finder into height estimate
    c) pixracer clone board IMU hardware issue work around
    d) dataflash time-going-backwards fix
    e) fix rate PID conversion (AC3.3->AC3.4) for single, coax and tricopters
------------------------------------------------------------------
Copter 3.4.0-rc4 06-Sep-2016
Changes from 3.4.0-rc3
1) Intel Aero flight controller support (not Intel Aero RTF, that was done in a later release)
2) Bug fixes, minor improvements:
    a) resolve unlikely endless-climb in AltHold, Loiter by ensuring Z-axis accelerometer IMAX can always overpower hover throttle
    b) fix auto build issue that could lead to parameter corruption
    c) fix EKF2 gyro estimation error when switching between IMUs
    d) sanity check MOT_THST_MIX_MIN, MAX parameters
    e) increase likelihood that parachute will release by reducing counter instead of resetting counter if vehicle becomes temporarily upright
------------------------------------------------------------------
Copter 3.4.0-rc3 31-Aug-2016
Changes from 3.4.0-rc2
1) Landing detector improvements:
    a) checks that descent rate is less than 1m/s (avoids disarms when vehicle is obviously still descending)
    b) tradheli fix for landing detector
2) WP_NAVALT_MIN parameter limits maximum lean angle when vehicle is below the specified alt while taking off or landing.  Only applies in LAND mode, final stage of RTL and during take-off in AUTO mode
3) GND_EFFECT_COMP parameter allows turning on EKF compensation for ground effect during take-off and landing
4) Advanced Failsafe for use in OutBack challenge (see http://ardupilot.org/plane/docs/advanced-failsafe-configuration.html)
5) AutoTune minor improvement to reduce step size on vehicles with very low hover throttle (i.e. high power-to-weight ratio)
6) Bug fixes, minor improvement:
    a) OneShot ESC calibration fix
    b) LAND_SPEED_HIGH allows specifying vertical descent speed separate from WPNAV_SPEED_DN during the initial descent in LAND mode (and final stage of RTL)
    c) improved USB driver reliability for windows users (NuttX change)
    d) report dataflash logging health to ground station
    e) Guided mode accepts yaw rate from SET_ATTITUDE_TARGET message 
------------------------------------------------------------------
Copter 3.4.0-rc2 08-Aug-2016
Changes from 3.4.0-rc1
1) ADSB based avoidance of manned aircraft
2) Polygon fence (works seamlessly with circular and altitude fences)
3) Throttle related changes:
    a) motors speed feedback to pilot while landed - motors only spin up to minimum throttle as throttle stick is brought to mid (previously spun up to 25%)
    b) MOT_HOVER_LEARN allows MOT_THST_HOVER to be learned in flight
    c) THR_MIN becomes MOT_SPIN_MIN
    d) THR_MID becomes MOT_THST_HOVER
4) Precision landing:
    a) control improved by reducing lag from sensor and correcting math errors
    b) descent rate slows to allow time to correct horizontal position error
5) Guided mode:
    a) velocity controller accelerates/decelerates smoothly to target
    b) Guided_NoGPS flight mode added to allow companion computer control in non-GPS environments
    c) stop at fence if using velocity controller, reject points outside fence if using position controller
6) Throw Mode:
    a) THROW_NEXTMODE parameter allows copter to switch to Auto, Guided, RTL, Land or Brake after throw is complete  
    b) Throw mode fixes so motors reliably start
7) Attitude controller:
    a) use Quaternions instead of Euler angles
    b) ATC_ANG_LIM_TC allows controlling rate at which lean angles are limited to preserve altitude in AltHold, Loiter
8) EKF can use range finder as primary height source if EKF2_RNG_USE_HGT parameter is set to 70
9) Bug fixes
    a) AutoTune fix so that gains don't fall too low
    b) Rally points with altitudes no longer cause vehicle to climb above fence during RTL
    c) various EKF fixes including bug in initialisation of declination co-variances
    d) SingleCopter, CoaxCopter flag gains 
10) Small enhancements:
    a) Rally points outside fence are ignored
    b) AP_Button support added to allow ground station to see if user has pushed a button on the flight controller
    c) support for PLAY_TUNE and LED_CONTROL mavlink messages to cause vehicle to play tune or change colour of LED
    d) support for GPS_INPUT mavlink message to allow supplying vehicle position data from other sources
    e) NAV_DELAY mission command to delay start of mission (or individual command) until a specified absolute time
    e) TKOFF_NAV_ALT param allows take-off with no horizontal position hold until vehicle reaches specified altitude (intended mostly for helicopters)
    f) SCHED_LOOP_RATE param allows slowing main loop rate from default of 400 (intended for developers only) 
11) Safety (in addition to Fence improvements above):
    a) Motors begin spinning 2 seconds after arming (previously motors spun immediately)
------------------------------------------------------------------
Copter 3.4.0-rc1 28-May-2016
Changes from 3.3.3
1) EKF2 allows "boat mode" (attitude initialisation without gyro calibration)
2) Throw mode
3) Terrain following:
    a) support terrain altitudes during missions using GCS's map data or rangefinder (i.e. Lidar)
    b) LightWare range finder driver fixes (I2C works)
    c) Bebop sonar support
4) Precision Landing using IRLock sensor
5) Attitude controller re-organisation (all parameter changes automatically moved and scales adjusted)
    a) RATE_ parameters become ATC_RAT_ (i.e. RATE_RLL_P becomes ATC_RAT_RLL_P) 
    b) Rate Roll, Pitch, Yaw P, D are reduced by 10% for X, V, H frames, increased by 27% for all other frames
    c) STB_ parameters becomes ATC_ANG_ (i.e. STB_RLL_P becomes ATC_ANG_RLL_P)
6) Motors library improvements:
    a) support OneShot ESCs (set MOT_PWM_TYPE to 0 for Normal, 1 for OneShot or 2 for OneShot125)
    b) TriCopter compensates for tail servo angle
    c) SingleCopter, CoaxCopter adjust control surfaces based on throttle output
    d) MOT_PWM_MIN, MAX allow specifying output ranges to ESCs which are difference from throttle input channel (i.e. RC3)
    e) TradHeli servo objects moved into heli class (HSV_ parameters become H_SV_)
7) uAvionix Ping sensor support (ADS-B vehicles appear on GCS, avoidance will come in future release) 
8) Safety:
    a) warning if GPS update rate is slow (under 5hz, does not stop arming) 
------------------------------------------------------------------
Copter 3.3.3 24-Feb-2016 / 3.3.3-rc2 27-Jan-2016
Changes from 3.3.3-rc1
1) bug fix to Guided mode's velocity controller to run at 400hz
2) bug fix to MAVLink routing to allow camera messages to reach MAVLink enabled cameras and gimbals (including SToRM32)
------------------------------------------------------------------
Copter 3.3.3-rc1 4-Jan-2016
Changes from 3.3.2
1) Restrict mode changes in helicopter when rotor is not at speed
2) add ATC_ANGLE_BOOST param to allow disabling angle boost for all flight modes
3) add LightWare range finder support
------------------------------------------------------------------
Copter 3.3.2 01-Dec-2015 / 3.3.2-rc2 18-Nov-2015
Changes from 3.3.2-rc1
1) Bug fix for desired climb rate initialisation that could lead to drop when entering AltHold, Loiter, PosHold
2) Fix to hard landings when WPNAV_SPEED_DN set high in RTL, Auto (resolved by using non-feedforward alt hold)
3) Reduce Bad AHRS by filtering innovations
4) Allow arming without GPS if using Optical Flow
5) Smoother throttle output in Guided mode's velocity control (z-axis now 400hz) 
------------------------------------------------------------------
Copter 3.3.2-rc1 4-Nov-2015
Changes from 3.3.1
1) Helicopter Improvements:
    a) Fix Arming race condition
    b) Fix servos to move after arming in Stabilize and Acro
    c) Implement Pirouette Compensation
    d) Add Rate I-Leak-Min functionality
    e) Add new Stab Collective and Acro Expo Col functions
    f) Add circular swashplate limits (Cyclic Ring)
    g) Add new H_SV_Man functions
    h) Add Hover Roll Trim function
    i) Add Engine Run Enable Aux Channel function
    j) Add servo boot test function
    h) Add Disarm Delay parameter
------------------------------------------------------------------
Copter 3.3.1 26-Oct-2015 / 3.3.1-rc1 20-Oct-2015
Changes from 3.3
1) Bug fix to prevent potential crash if Follow-Me is used after an aborted takeoff
2) compiler upgraded to 4.9.3 (runs slightly faster than 4.7.2 which was used previously)
------------------------------------------------------------------
Copter 3.3 29-Sep-2015 / 3.3-rc12 22-Sep-2015
Changes from 3.3-rc11
1) EKF recovers from pre-arm "Compass variance" failure if compasses are consistent
------------------------------------------------------------------
Copter 3.3-rc11 10-Sep-2015
Changes from 3.3-rc10
1) PreArm "Need 3D Fix" message replaced with detailed reason from EKF
------------------------------------------------------------------
Copter 3.3-rc10 28-Aug-2015
Changes from 3.3-rc9
1) EKF improvements:
    a) simpler optical flow takeoff check
2) Bug Fixes/Minor enhancements:
    a) fix INS3_USE parameter eeprom location
    b) fix SToRM32 serial protocol driver to work with recent versions
    c) increase motor pwm->thrust conversion (aka MOT_THST_EXPO) to 0.65 (was 0.50)
    d) Firmware version sent to GCS in AUTOPILOT_VERSION message
3) Safety:
    a) pre-arm check of compass variance if arming in Loiter, PosHold, Guided
    b) always check GPS before arming in Loiter (previously could be disabled if ARMING_CHECK=0)
    c) sanity check locations received from GCS for follow-me, do-set-home, do-set-ROI
    d) fix optical flow failsafe (was not always triggering LAND when optical flow failed)
    e) failsafe RTL vs LAND decision based on hardcoded 5m from home check (previously used WPNAV_RADIUS parameter)
------------------------------------------------------------------
Copter 3.3-rc9 19-Aug-2015
Changes from 3.3-rc8
1) EKF improvements:
    a) IMU weighting based on vibration levels (previously used accel clipping)
    b) fix blended acceleration (used for altitude control) in cases where first IMU fails
    c) ensure unhealthy barometer values are never consumed
2) TradHeli: remove acceleration feed forward
3) Safety:
    a) check accel, gyro and baro are healthy when arming (previously was only checked pre-arm)
    b) Guided mode velocity controller timeout (vehicle stops) after 3 seconds with no update from GCS
4) Minor enhancements:
    a) fix for AUAV board's usb-connected detection
    b) add Lidar-Lite-V2 support
    c) MOT_THR_MIN_MAX param added to control prioritisation of throttle vs attitude during dynamic flight
    d) RALLY_INCL_HOME param allows always including home when using rally points
    e) DO_FLIGHT_TERMINATION message from GCS acts as kill switch
5) Bug Fixes:
    a) fix to ensure motors start slowly on 2nd spin-up
    b) fix RTL_CLIMB_MIN feature (vehicle climbed too high above home)
------------------------------------------------------------------
Copter 3.3-rc8 25-Jul-2015
Changes from 3.3-rc7
1) EKF improvements:
    a) de-weight accelerometers that are clipping to improve resistance to high vibration
    b) fix EKF to use primary compass instead of first compass (normally the same)
2) UBlox "HDOP" corrected to actually be hdop (was pdop) which leads to 40% lower value reported
3) TradHeli:
    a) Motors library split into "Multicopter" and "TradHeli" so TradHeli does not see multicopter parameters
    b) Heading target reset during landing to reduce vehicle fighting to rotate while on the ground
4) Minor enhancements:
    a) SToRM32 gimbal can be connected to any serial port
    b) log when baro, compass become unhealthy
    c) ESC_CALIBRATION parameter can be set to "9" to disable esc calibration startup check
    d) Circle rate adjustment with ch6 takes effect immediately
    e) log home and origin
    f) pre-arm check of battery voltage and fence
    g) RTL_CLIMB_MIN parameter forces vehicle to climb at least this many cm when RTL is engaged (default is zero)
5) Bug fixes:
    a) fix THR_MIN being incorrectly scaled as pwm value during low-throttle check
    b) fence distance calculated from home (was incorrectly calculated from ekf-origin)
    c) When flying with joystick and joystick is disconnected, control returns immediately to regular TX
    d) dataflash's ATT yaw fixed to report heading as 0 ~ 360
    e) fix to mission's first command being run multiple times during mission if it was a do-command
    f) ekf-check is enabled only after ekf-origin is set (stops red-yellow flashing led when flying without GPS lock)
    g) fix initialisation of mount's mode
    h) start-up logging so parameters only logged once, mission always written
6) Linux:
    a) bebop support
------------------------------------------------------------------
Copter 3.3-rc7 28-Jun-2015
Changes from 3.3-rc6
1) reduce EKF gyro bias uncertainty that caused attitude estimate errors
2) force 400hz IMU logging on (temporary for release candidate testing)
------------------------------------------------------------------
Copter 3.3-rc6 25-Jun-2015
Changes from 3.3-rc5
1) EKF related changes:
    a) reset altitude even when arming without GPS lock
    b) fix yaw twitch caused by EKF heading estimate reset
    c) fix IMU time scaling bug that caused height estimate to deviate from the baro
2) AutoTune improvements:
    a) improved yaw tuning by increasing yaw step magnitude
    b) added logging of accelerations
    c) improvements to step tests
3) Improved crash check:
    a) allow triggering even if pilot doesn't move throttle to zero
    b) easier triggering by removing baro check and using angle error instead of absolute tilt angle 
4) TradHeli:
    a) swash moves while landed in AltHold mode
    b) improvements to land detector
    c) fixed RSC Runup Time calculation
    d) Rate FF Low-pass Filter changed from 5Hz to 10Hz, more responsive
5) support Linux builds for NAVIO+ and Erle-Brain (http://firmware.diydrones.com/Copter/beta/)
6) Other improvements / Bug Fixes:
    a) sonar pre-arm checks only enforced if using optical flow
    b) fix EKF failsafe bug that would not allow recovery
    c) full rate IMU logging for improved vibration analysis (set LOG_BITMASK to All+FullIMU)
    d) new VIBE dataflash message records vibration levels
    e) default MNT_TYPE to "1" if servo gimbal rc outputs were defined
    f) RC_FEEL defaults to medium
    g) addition of SToRM32 serial support (supports mount angle feedback to GCS)
    h) new tricopter's tail servo parameters (MOT_YAW_SV_MIN, MAX, TRIM, REV)
------------------------------------------------------------------
Copter 3.3-rc5 23-May-2015
Changes from 3.3-rc4
1) Fix AHRS bad gyro health message caused by timing jitter and log IMU health
2) TradHeli:
    a) better default rate PIDs
    b) Collective pitch output now operates even when disarmed 
3) Small changes/fixes:
    a) GCS can use MAV_CMD_MISSION_START to start mission in AUTO even without pilot raising throttle 
    b) GCS can force disarming even in flight by setting param2 to "21196"
    c) rc-override timeout reduced from 2 seconds to 1 (applies when using GCS joysticks to control vehicle)
    d) do-set-speed fix so it takes effect immediately during missions
    e) GCS failsafe disarms vehicle if already landed (previously it could RTL) 
------------------------------------------------------------------
Copter 3.3-rc4 17-May-2015
Changes from 3.3-rc3
1) AutoTune:
    a) save roll, pitch, yaw rate acceleration limits along with gains
    b) more conservative gains 
2) Roll, pitch rate control feed-forward now on by default (set ATC_RATE_FF_ENAB to "0" to disable)
3) Serial ports increased to maximum of 4 (set SERIALX_PROTOCOL to 1)
4) MOT_THR_MIX_MIN param to control minimum throttle vs attitude during landing (higher = more attitude control but bumpier landing) 
5) EKF fixes/improvements
    a) prevent yaw errors during fast spins
    b) bug fix preventing external selection of optical flow mode
6) Parachute:
    a) servo/relay held open for 2sec when deploying (was 1sec)
    b) fix altitude check to be alt-above-home (was alt-above ekf origin which could be slightly different)
7) TradHeli:
    a) parameters moved to stop possibility of corruption if board is switched between tradheli and multicopter firmware.  Heli users may need to re-setup some heli-specific params.
    b) H_COLYAW param can be float 
8) Small Improvements / Bug Fixes:
    a) reduced spline overshoot after very long track followed by very short track
    b) log entire mission to dataflash whenever it's uploaded
    c) altitude reported if vehicle takes off before GPS lock
    d) high speed logging of IMU
    e) STOP flight mode renamed to BRAKE and aux switch option added
------------------------------------------------------------------
Copter 3.3-rc2/rc3 02-May-2015
Changes from 3.3-rc1
1) AutoTune reliability fixes (improved filtering to reduce noise interference)
2) Optical flow improvements:
    a) Range Finder pre-arm check - lift vehicle between 50cm ~ 2m before arming.  Can be disabled by setting ARMING_CHECK to "Skip Params/Sonar"
    b) Vehicle altitude limited to range finder altitude when optical flow is enabled 
3) AltHold & Take-off changes:
    a) feed-forward controller and jerk limiting should result in slightly snappier performance and smoother take-offs
    b) vehicle climbs automatically to PILOT_TKOFF_ALT alt when taking off in Loiter, AltHold, PosHold, Sport (disabled by default, pilot's throttle input overrides takeoff)
    c) PILOT_THR_FILT allows enforcing smoother throttle response in manual flight modes (defaults to 0 = off)
    d) TX with sprung throttle can set PILOT_THR_BHV to "1" so motor feedback when landed starts from mid-stick instead of bottom of stick
    e) GCS can initiate takeoff even in Loiter, AltHold, PosHold and sport by issuing NAV_TAKEOFF mavlink command
4) Stop flight mode - causes vehicle to stop quickly, and does not respond to user input or waypoint movement via MAVLink.  Requires GPS, will be renamed to Brake mode.
5) Aux channel features:
    a) Emergency Stop - stops all motors immediately and disarms in 5 seconds
    b) Motor Interlock - opposite of Emergency Stop, must be on to allow motors to spin motors, must be off to arm
6) Air pressure gain scaling (of roll, pitch, yaw) should mostly remove need to re-tune when flying at very different altitudes
7) landing detector simplified to only check vehicle is not accelerating & motors have hit their lower limit
8) Loiter tuning params to remove "freight train" stops:
       raising WPNAV_LOIT_MAXA makes vehicle start and stop faster
       raising WPNAV_LOIT_MINA makes vehicle stop more quickly when sticks centered 
9) Other items:
    a) faster EKF startup
    b) Camera control messages sent via MAVLink to smart cameras.  Allow control of camera zoom for upcoming IntelEdison/Sony QX1 camera control board
    c) Lost Copter Alarm can be triggered by holding throttle down, roll right, pitch back
10) Bug fixes:
    a) Home position set to latest arm position (it was being set to previous disarm location or first GPS lock position)
    b) bug fix to mission Jump to command zero
------------------------------------------------------------------
Copter 3.3-rc1 11-Apr-2015
Changes from 3.2.1
1) Only support fast CPUs boards (Pixhawk, VRBrain, etc) and drop support for APM1, APM2 (sorry!)
2) AutoTune for yaw
3) Smooth throttle curve which should reduce wobbles during fast climbs and descents
4) ch7/ch8 aux switches expanded to ch9 ~ ch12 (see CH9_OPT ~ CH12_OPT params)
5) PX4Flow support in Loiter mode (still somewhat experimental)
6) Safety features:
    a) EKF on by default replacing DCM/InertialNav which should improve robustness
    b) increased accelerometer range from 8G to 16G to reduce chance of climb due to high vibrations (requires accel calibration)
7) Landing features:
    a) improved landing on slopes
    b) retractable landing gear (see LGR_ parameters)
8) Camera Gimbal features:
    a) SToRM32 gimbal support (using MAVLink)
    b) AlexMos gimbal support (using AlexMos serial interface)
    c) do-mount-control commands supported in missions (allows controlling gimbal angle in missions)
9) Battery related features:
    a) PID scaling for battery voltage (disabled by default, see MOT_THST_BAT_ parameters)
    b) smart battery support
10) Other:
    a) support do-set-home command (allows return-to-me and locked home position once GCS enhancements are completed)
    b) performance improvements for Pixhawk reduce CPU load from 80% to 15%
    c) firmware string name changed from ArduCopter to APM:Copter
------------------------------------------------------------------
ArduCopter 3.2.1 11-Feb-2015 / 3.2.1-rc2 30-Jan-2015
Changes from 3.2.1-rc1
1) Bug Fixes:
    a) prevent infinite loop with linked jump commands
    b) Pixhawk memory corruption fix when connecting via USB
    c) vehicle stops at fence altitude limit in Loiter, AltHold, PosHold
    d) protect against multiple arming messages from GCS causing silent gyro calibration failure    
------------------------------------------------------------------
ArduCopter 3.2.1-rc1 08-Jan-2015
Changes from 3.2
1) Enhancements:
    a) reduced twitch when passing Spline waypoints
    b) Faster disarm after landing in Auto, Land, RTL
    c) Pixhawk LED turns green before arming only after GPS HDOP falls below 2.3 (only in flight modes requiring GPS)
2) Safety Features:
    a) Add desired descent rate check to reduce chance of false-positive on landing check
    b) improved MPU6k health monitoring and re-configuration in case of in-flight failure
    c) Rally point distance check reduced to 300m (reduces chance of RTL to far away forgotten Rally point)
    d) auto-disarm if vehicle is landed for 15seconds even in Auto, Guided, RTL, Circle
    e) fence breach while vehicle is landed causes vehicle to disarm (previously did RTL)
3) Bug Fixes:
    a) Check flight mode even when arming from GCS (previously it was possible to arm in RTL mode if arming was initiated from GCS)
    b) Send vehicle target destination in RTL, Guided (allows GCS to show where vehicle is flying to in these modes)
    c) PosHold wind compensation fix
------------------------------------------------------------------
ArduCopter 3.2 07-Nov2014 / 3.2-rc14 31-Oct-2014
Changes from 3.2-rc13
1) Safety Features:
    a) fail to arm if second gyro calibration fails (can be disabled with ARMING_CHECK)
2) Bug fixes:
    a) DCM-check to require one continuous second of bad heading before triggering LAND
    b) I2C bug that could lead to Pixhawk freezing up if I2C bus is noisy
    c) reset DCM and EKF gyro bias estimates after gyro calibration (DCM heading could drift after takeoff due to sudden change in gyro values)
    d) use primary GPS for LED status (instead of always using first GPS)
------------------------------------------------------------------
ArduCopter 3.2-rc13 23-Oct-2014
Changes from 3.2-rc12
1) DCM check triggers LAND if yaw disagrees with GPS by > 60deg (configure with DCM_CHECK_THRESH param) and in Loiter, PosHold, Auto, etc 
2) Safety features:
    a) landing detector checks baro climbrate between -1.5 ~ +1.5 m/s
    b) sanity check AHRS_RP_P and AHRS_YAW_P are never less than 0.05
    c) check set-mode requests from GCS are for this vehicle
3) Bug fixes:
    a) fix ch6 tuning of wp-speed (was getting stuck at zero)
    b) parachute servo set to off position on startup
    c) Auto Takeoff timing bug fix that could cause severe lean on takeoff
    d) timer fix for "slow start" of motors on Pixhawk (timer was incorrectly based on 100hz APM2 main loop speed)
4) reduced number of relays from 4 to 2 (saves memory and flash required on APM boards)
5) reduced number of range finders from 2 to 1 (saves memory and flash on APM boards)
6) allow logging from startup when LOG_BITMASK set to "All+DisarmedLogging" 
------------------------------------------------------------------
ArduCopter 3.2-rc12 10-Oct-2014
Changes from 3.2-rc11
1) disable sonar on APM1 and TradHeli (APM1 & APM2) to allow code to fit 
2) Add pre-arm and health check that gyro calibration succeeded 
3) Bug fix to EKF reporting invalid position and velocity when switched on in flight with Ch7/Ch8 switch 
------------------------------------------------------------------
ArduCopter 3.2-rc11 06-Oct-2014
Changes from 3.2-rc10
1) reduce lean on take-off in Auto by resetting horizontal position targets 
2) TradHeli landing check ignores overall throttle output
3) reduce AHRS bad messages by delaying 20sec after init to allow EKF to settle (Pixhawk only)
4) Bug fixes:
    a) fix THR_MIN scaling issue that could cause landing-detector to fail to detect landing when ch3 min~max > 1000 pwm
    b) fix Mediatek GPS configuration so update rate is set correctly to 5hz
    c) fix to Condition-Yaw mission command to support relative angles
    d) EKF bug fixes when recovering from GPS glitches (affects only Pixhawks using EKF)
------------------------------------------------------------------
ArduCopter 3.2-rc10 24-Sep-2014
Changes from 3.2-rc9
1) two-stage land-detector to reduce motor run-up when landing in Loiter, PosHold, RTL, Auto
2) Allow passthrough from input to output of channels 9 ~ 14 (thanks Emile!)
3) Add 4hz filter to vertical velocity error during AltHold
4) Safety Feature:
    a) increase Alt Disparity pre-arm check threshold to 2m (was 1m)
    b) reset battery failsafe after disarming/arming (thanks AndKe!)
    c) EKF only apply centrifugal corrections when GPS has at least 6 satellites (Pixhawk with EKF enabled only)
5) Bug fixes:
    a) to default compass devid to zero when no compass connected
    b) reduce motor run-up while landing in RTL
------------------------------------------------------------------
ArduCopter 3.2-rc9 11-Sep-2014
Changes from 3.2-rc8
1) FRAM bug fix that could stop Mission or Parameter changes from being saved (Pixhawk, VRBrain only)
------------------------------------------------------------------
ArduCopter 3.2-rc8 11-Sep-2014
Changes from 3.2-rc7
1) EKF reduced ripple to resolve copter motor pulsing
2) Default Param changes:
    a) AltHold Rate P reduced from 6 to 5
    b) AltHold Accel P reduced from 0.75 to 0.5, I from 1.5 to 1.0
    c) EKF check threshold increased from 0.6 to 0.8 to reduce false positives
3) sensor health flags sent to GCS only after initialisation to remove false alerts 
4) suppress bad terrain data alerts
5) Bug Fix:
    a)PX4 dataflash RAM usage reduced to 8k so it works again  
------------------------------------------------------------------
ArduCopter 3.2-rc7 04-Sep-2014
Changes from 3.2-rc6
1) Safety Items:
    a) Landing check made more strict (climb rate requirement reduced to 30cm/s, overall throttle < 25%, rotation < 20deg/sec)
    b) pre-arm check that accels are consistent (Pixhawk only, must be within 1m/sec/sec of each other)
    c) pre-arm check that gyros are consistent (Pixhawk only, must be within 20deg/sec of each other)
    d) report health of all accels and gyros (not just primary) to ground station 
------------------------------------------------------------------
ArduCopter 3.2-rc6 31-Aug-2014
Changes from 3.2-rc5
1) Spline twitch when passing through a waypoint largely resolved
2) THR_DZ param added to allow user configuration of throttle deadzone during AltHold, Loiter, PosHold
3) Landing check made more strict (climb rate must be -40~40cm/s for 1 full second)
4) LAND_REPOSITION param default set to 1
5) TradHeli with flybar passes through pilot inputs directly to swash when in ACRO mode
6) Safety Items:
    a) EKF check disabled when using inertial nav (caused too many false positives)
    b) pre-arm check of internal vs external compass direction (must be within 45deg of each other)
7) Bug Fixes:
    a) resolve NaN in angle targets when vehicle hits gimbal lock in ACRO mode
    b) resolve GPS driver buffer overflow that could lead to missed GPS messages on Pixhawk/PX4 boards
    c) resolve false "compass not calibrated" warnings on Pixhawk/PX4 caused by missing device id initialisation 
------------------------------------------------------------------
ArduCopter 3.2-rc5 15-Aug-2014
Changes from 3.2-rc4
1) Pixhawk's max num waypoints increased to 718
2) Smoother take-off in AltHold, Loiter, PosHold (including removing initial 20cm jump when taking off)
3) ACRO mode roll, pitch, yaw EXPO added for faster rotation when sticks are at extremes (see ACRO_EXPO parameter)
4) ch7/ch8 relay option replaces ch6 option (ch6 is reserved for tuning not switching things on/off)
5) Safety Items:
    a) Baro glitch check relaxed to 5m distance, 15m/s/s max acceleration
    b) EKF/INav check relaxed to 80cm/s/s acceleration correct (default remains as 0.6 but this now means 80cm/s/s)
    c) When GPS or Baro glitch clears, the inertial nav velocities are *not* reset reducing chance of sudden vehicle lean
    d) Baro altitude calculation checked for divide-by-zero and infinity
6) Bug Fixes:
    a) AltHold jump bug fixed (altitude target reset when landed)
    b) Rally point bug fix so it does not climb excessively before flying to rally point
    c) body-frame rate controller z-axis bug fix (fast rotation in z-axis would cause wobble in roll, pitch) 
------------------------------------------------------------------
ArduCopter 3.2-rc4 01-Aug-2014
Changes from 3.2-rc3
1) Pre-takeoff throttle feedback in AltHold, Loiter, PosHold
2) Terrain altitude retrieval from ground station (informational purposes only so far, Pixhawk only) 
3) Safety Items:
    a) "EKF check" will switch to LAND mode if EKF's compass or velocity variance over 0.6 (configurable with EKFCHECK_THRESH param)
       When EKF is not used inertial nav's accelerometer corrections are used as a substitute
    b) Barometer glitch protection added.  BAROGLTCH_DIST and BAROGLTCH_ACCEL parameters control sensitivity similar to GPSGLITCH protection
       When glitching occurs barometer values are temporarily ignored
    c) Throttle/radio and battery failsafes now disarm vehicle when landed regardless of pilot's throttle position
    d) auto-disarm extended to Drift, Sport and OF_Loiter flight modes
    e) APM2 buzzer notification added for arming failure
    f) APM2 arming buzz made longer (now matches Pixhawk)
    g) do-set-servo commands cannot interfere with motor output
4) Bug Fixes:
    a) Drift slow yaw response fixed
    b) AC3.2-rc3 failsafe bug resolved.  In -rc3 the throttle failsafe could be triggered even when disabled or motors armed (although vehicle would not takeoff) 
------------------------------------------------------------------
ArduCopter 3.2-rc3 16-Jul-2014
Changes from 3.2-rc2
1) Hybrid renamed to PosHold
2) Sonar (analog, i2c) and PulsedLight Range Finders enabled on Pixhawk (Allyson, Tridge)
3) Landing changes:
    a) disable pilot repositioning while landing in RTL, Auto (set LAND_REPOSITION to 1 to re-enable) (JonathanC)
    b) delay 4 seconds before landing due to failsafe (JonathanC)
4) Secondary compass calibration enabled, pre-arm check that offsets match current devices (Randy, Tridge, MichaelO)
5) Control improvements:
    a) use bias adjusted gyro rates - helps in cases of severe gyro drift (Jonathan)
    b) bug-fixes when feed-forward turned off (Leonard)
6) TradHeli improvements (RobL):
    a) bug fix to use full collective range in stabilize and acro flight modes
    b) filter added to main rotor input (ch8) to ensure momentary blip doesn't affect main rotor speed
7) Safety items:
    a) increased default circular Fence radius to 300m to reduce chance of breach when GPS lock first acquired
    b) radio failsafe timeout for late frames reduced to 0.5sec for normal receivers or 2.0sec when flying with joystick (Craig)
    c) accelerometer pre-arm check for all active accelerometers (previously only checked the primary accelerometer)
8) Other features:
    a) ch7/ch8 option to retract mount (svefro)
    b) Do-Set-ROI supported in Guided, RTL mode
    c) Condition-Yaw accepted in Guided, RTL modes (MoussSS)
    d) CAMERA dataflash message includes relative and absolute altitude (Craig)
9) Red Balloon Popper support (Randy, Leonard):
    a) Velocity controller added to Guided mode
    b) NAV_GUIDED mission command added
10) Bug fixes:
    a) bug fix to flip on take-off in stabilize mode when landing flag cleared slowly (JonathanC)
    b) allow disarming in AutoTune (JonathanC)
    c) bug fix to unpredictable behaviour when two spline points placed directly ontop of each other
------------------------------------------------------------------
ArduCopter 3.2-rc2 27-May-2014
Changes from 3.2-rc1
1) Hybrid mode initialisation bug fix
2) Throttle pulsing bug fix on Pixhawk
3) Parachute enabled on Pixhawk
4) Rally Points enabled on Pixhawk
------------------------------------------------------------------
ArduCopter 3.2-rc1 9-May-2014
Changes from 3.1.4
1) Hybrid mode - position hold mode but with direct response to pilot input during repositioning (JulienD, SandroT)
2) Spline waypoints (created by David Dewey, modified and integrated by Leonard, Randy)
3) Drift mode uses "throttle assist" for altitude control (Jason)
4) Extended Kalman Filter for potentially more reliable attitude and position control (Pixhawk only) (Paul Riseborough).  Set AHRS_EKF_USE to 1 to enable or use Ch7/8 switch to enable/disable in flight. 
5) Manual flight smoothness:
    a) Smoother roll, pitch response using RC_FEEL_RP parameter (100 = crisp, 0 = extremely soft)
    b) Adjustable max rotation rate (ATC_RATE_RP_MAX, ATC_RATE_Y_MAX) and acceleration (ATC_ACCEL_RP_MAX, ATC_ACCEL_Y_MAX)
6) Autopilot smoothness:
    a) Vertical acceleration in AltHold, Loiter, Hybrid modes can be configured with PILOT_ACCEL_Z parameter (higher = faster acceleration)
    b) Maximum roll and pitch angle acceleration in Loiter mode can be configured with WPNAV_LOIT_JERK (higher = more responsive but potentially jerky)
    c) Yaw speed can be adjusted with ATC_SLEW_YAW parameter (higher = faster)
    d) smoother takeoff with configurable acceleration using WPNAV_ACCEL_Z parameter
    e) Twitches removed during guided mode or when entering Loiter or RTL from high speeds  
7) Mission improvements:
    a) mission will be resumed from last active command when pilot switches out and then back into Auto mode (prev behaviour can be restored by setting MIS_RESTART param to 1)
    b) DO_SET_ROI persistent across waypoints.  All-zero DO_SET_ROI command restores default yaw behaviour
    c) do-jump fixed
    d) conditional_distance fixed
    e) conditional_delay fixed
    f) do-change-speed command takes effect immediately during mission
    g) vehicle faces directly at next waypoint (previously it could be about 10deg off)
    h) loiter turns fix to ensure it will circle around lat/lon point specified in mission command (previously it could be off by CIRCLE_RADIUS)
8) Safety improvements:
    a) After a fence breach, if the pilot re-takes control he/she will be given a minimum of 10 seconds and 20m to recover before the autopilot will invoke RTL or LAND
    b) Parachute support including automatic deployment during mechanical failures
9) Other enhancements:
    a) V-tail quad support
    b) Dual GPS support (secondary GPS output is simply logged, not actually used yet)
    c) Electro Permanent Magnet (aka Gripper) support
    d) Servo pass through for channels 6 ~ 8 (set RC6_FUNCTION to 1)
    e) Remove 80m limit on RTL's return altitude but never let it be above fence's max altitude
10) Other bug fixes:
    a) Bug fix for LAND sometimes getting stuck at 10m
    b) During missions, vehicle will maintain altitude even if WPNAV_SPEED is set above the vehicle's capabilities
    c) when autopilot controls throttle (i.e. Loiter, Auto, etc) vehicle will reach speeds specified in PILOT_VELZ_MAX and WPNAV_SPEED_UP, WPNAV_SPEED_DN parameters
11) CLI removed from APM1/2 to save flash space, critical functions moved to MAVLink:
    a) Individual motor tests (see MP's Initial Setup > Optional Hardware > Motor Test)
    b) compassmot (see MP's Initial Setup > Optional Hardware > Compass/Motor Calib)
    c) parameter reset to factory defautls (see MP's Config/Tuning > Full Parameter List > Reset to Default)
------------------------------------------------------------------
ArduCopter 3.1.5 27-May-2014 / 3.1.5-rc2 20-May-2014
Changes from 3.1.5-rc1
1) Bug Fix to broken loiter (pixhawk only)
2) Workaround to read from FRAM in 128byte chunks to resolve a few users boot issues (Pixhawk only)
------------------------------------------------------------------
ArduCopter 3.1.5-rc1 14-May-2014
Changes from 3.1.4
1) Bug Fix to ignore roll and pitch inputs to loiter controller when in radio failsafe
2) Bug Fix to allow compassmot to work on Pixhawk
------------------------------------------------------------------
ArduCopter 3.1.4 8-May-2014 / 3.1.4-rc1 2-May-2014
Changes from 3.1.3
1) Bug Fix for Pixhawk/PX4 NuttX I2C memory corruption when errors are found on I2C bus
------------------------------------------------------------------
ArduCopter 3.1.3 7-Apr-2014
Changes from 3.1.2
1) Stability patch fix which could cause motors to go to min at full throttle and with large roll/pitch inputs
------------------------------------------------------------------
ArduCopter 3.1.2 13-Feb-2014 / ArduCopter 3.1.2-rc2 12-Feb-2014
Changes from 3.1.2-rc1
1) GPS Glitch detection disabled when connected via USB
2) RC_FEEL_RP param added for adjusting responsiveness to pilot roll/pitch input in Stabilize, Drift, AltHold modes
------------------------------------------------------------------
ArduCopter 3.1.2-rc1 30-Jan-2014
Changes from 3.1.1
1) Pixhawk baro bug fix to SPI communication which could cause large altitude estimate jumps at high temperatures
------------------------------------------------------------------
ArduCopter 3.1.1 26-Jan-2014 / ArduCopter 3.1.1-rc2 21-Jan-2014
Changes from 3.1.1-rc1
1) Pixhawk improvements (available for APM2 when AC3.2 is released):
    a) Faster arming
    b) AHRS_TRIM fix - reduces movement in loiter when yawing
    c) AUX Out 5 & 6 turned into general purpose I/O pins
    d) Three more relays added (relays are pins that can be set to 0V or 5V)
    e) do-set-servo fix to allow servos to be controlled from ground station
    f) Motorsync CLI test
    g) PX4 parameters moved from SD card to eeprom
    h) additional pre-arm checks for baro & inertial nav altitude and lean angle
------------------------------------------------------------------
ArduCopter 3.1.1-rc1 14-Jan-2014
Changes from 3.1
1) Pixhawk improvements:
    a) Telemetry port 2 enabled (for MinimOSD)
    b) SD card reliability improvements
    c) parameters moved to FRAM
    d) faster parameter loading via USB
    e) Futaba SBUS receiver support
2) Bug fixes:
    a) Loiter initialisation fix (Loiter would act like AltHold until flight mode switch changed position)
    b) ROI commands were not returning Lat, Lon, Alt to mission planner when mission was loaded from APM
3) TradHeli only fixes:
    a) Drift now uses same (reduced) collective range as stabilize mode
    b) AutoTune disabled (for tradheli only)
    c) Landing collective (smaller than normal collective) used whenever copter is not moving
------------------------------------------------------------------
ArduCopter 3.1 14-Dec-2013
Changes from 3.1-rc8
1) Pixhawk improvements:
    a) switch to use MPU6k as main accel/gyro
    b) auto loading of IO-board firmware on startup
2) RTL fixes:
    a) initialise waypoint leash length (first RTL stop would be more aggressive than 2nd)
    b) reduce projected stopping distance for higher speed stops
------------------------------------------------------------------
ArduCopter 3.1-rc8 9-Dec-2013
Changes from 3.1-rc7
1) add Y6 motor mapping with all top props CW, bottom pros CCW (set FRAME = 10)
2) Safety Changes:
    a) ignore yaw input during radio failsafe (previously the copter could return home spinning if yaw was full over at time of failsafe)
    b) Reduce GPSGLITCH_RADIUS to 2m (was 5m) to catch glitches faster
3) Bug fixes:
    a) Optical flow SPI bus rates
    b) TradHeli main rotor ramp up speed fix
------------------------------------------------------------------
ArduCopter 3.1-rc7 22-Nov-2013
Changes from 3.1-rc6
1) MOT_SPIN_ARMED default to 70
2) Smoother inertial nav response to missed GPS messages
3) Safety related changes
    a) radio and battery failsafe disarm copter if landed in Loiter or AltHold (previously they would RTL)
    b) Pre-Arm check failure warning output to ground station every 30 seconds until they pass
    c) INS and inertial nav errors logged to dataflash's PM message
    d) pre-arm check for ACRO_BAL_ROLL, ACRO_BAL_PITCH
------------------------------------------------------------------
ArduCopter 3.1-rc6 16-Nov-2013
Improvements over 3.1-rc5
1) Heli improvements:
    a) support for direct drive tails (uses TAIL_TYPE and TAIL_SPEED parameters)
    b) smooth main rotor ramp-up for those without external govenor (RSC_RAMP_TIME)
    c) internal estimate of rotor speed configurable with RSC_RUNUP_TIME parameter to ensure rotor at top speed before starting missions
    d) LAND_COL_MIN collective position used when landed (reduces chance copter will push too hard into the ground when landing or before starting missions)
    e) reduced collective while in stabilize mode (STAB_COL_MIN, STAB_COL_MAX) for more precise throttle control
    f) external gyro parameter range changed from 1000~2000 to 0~1000 (more consistent with other parameters)
    g) dynamic flight detector switches on/off leaky-i term depending on copter speed
2) SingleCopter airframe support (contribution from Bill King)
3) Drift mode replaces TOY
4) MPU6k SPI bus speed decreased to 500khz after 4 errors
5) Safety related changes:
    a) crash detector cuts motors if copter upside down for more than 2 seconds
    b) INS (accel and gyro) health check in pre-arm checks
    c) ARMING_CHECK allows turning on/off individual checks for baro, GPS, compass, parameters, board voltage, radio
    d) detect Ublox GPS running at less than 5hz and resend configuration
    e) GPSGlitch acceptable radius reduced to 5m (stricter detection of glitches)
    f) range check roll, pitch input to ensure crazy radio values don't get through to stabilize controller
    g) GPS failsafe options to trigger AltHold instead of LAND or to trigger LAND even if in flight mode that does not require GPS
    h) Battery failsafe option to trigger RTL instead of LAND
    i) MOT_SPIN_ARMED set to zero by default
6) Bug fixes:
    a) missing throttle controller initialisation would mean Stabilize mode's throttle could be non-tilt-compensated
    b) inertial nav baro and gps delay compensation fix (contribution from Neurocopter)
    c) GPS failsafe was invoking LAND mode which still used GPS for horizontal control
------------------------------------------------------------------
ArduCopter 3.1-rc5 22-Oct-2013
Improvements over 3.1-rc4
1) Pixhawk USB reliability improvements
2) AutoTune changes:
    a) enabled by default
    b) status output to GCS
    c) use 2 pos switch only
3) ch7/ch8 LAND
4) Tricopter stability patch improvements [thanks to texlan]
5) safety improvements:
    a) slower speed up of motors after arming
    b) pre-arm check that copter is moving less than 50cm/s if arming in Loiter or fence enabled
------------------------------------------------------------------
ArduCopter 3.1-rc4 13-Oct-2013
Improvements over 3.1-rc3
1) Performance improvements to resolve APM alt hold issues for Octacopters:
     a) SPI bus speed increased from 500khz to 8Mhz
     b) Telemetry buffer increased to 512bytes
     c) broke up medium and slow loops into individual scheduled tasks and increased priority of alt hold tasks
2) Bug fix for Pixhawk USB connection
3) GPS Glitch improvements:
       a) added GPS glitch check to arming check
       b) parameters for vehicle max acceleration (GPSGLITCH_ACCEL) and always-ok radius (GPSGLICH_RADIUS)
------------------------------------------------------------------
ArduCopter 3.1-rc3 09-Oct-2013
Improvements over 3.1-rc2
1) GPS Glitch protection - gps positions are compared with the previous gps position.  Position accepted if within 10m or copter could have reached the position with max accel of 10m/s/s.
2) Bug fix for pixhawk SPI bus contention that could lead to corrupted accelerometer values on pixhawk resolved
3) Auto Tuning (compile time option only add "#define AUTOTUNE ENABLED" to APM_Config.h and set CH7_Opt or CH8_Opt parameter to 17)
4) CPU Performance improvement when reading from MPU6k for APM
5) SUPER_SIMPLE parameter changed to a bit map to allow some flight modes to use super simpler while others use regular simple (MP release to allow easier selection will go out with AC3.1 official release)
6) Safety changes:
    a) safety button must be pushed before arming on pixhawk
    b) RGB LED (aka toshiba led) changed so that disarmed flashes, armed is either blue (if no gps lock) or green (if gps lock)
    c) sensor health bitmask sent to groundstations
------------------------------------------------------------------
ArduCopter 3.1-rc2 18-Sep-2013
Improvements over 3.1-rc1
1) bug fix for MOT_SPIN_ARMED to allow it to be higher than 127
2) PX4/pixhawk auto-detect internal/external compass so COMPASS_ORIENT should be set to ORIENTATION_NONE if using GPS+compass module
------------------------------------------------------------------
ArduCopter 3.1-rc1 9-Sep-2013
Improvements over 3.0.1
1) Support for Pixhawks board
2) Arm, Disarm, Land and Takeoff in Loiter and AltHold
3) Improved Acro
    a) ACRO_RP_P, ACRO_YAW_P parameters added to control speed of rotation
    b) ACRO_BAL_ROLL, ACRO_BAL_PITCH controls speed at which copter returns to level
    c) ACRO_TRAINER can be set to 0:disable trainer, 1:auto leveling when sticks released, 2:auto leveling and lean angle limited to ANGLE_MAX
    d) Ch7 & Ch8 switch to set ACRO_TRAINER options in-flight
4) SPORT mode - equivalent of earth frame Acro with support for simple mode
5) Sonar ground tracking improvements and bug fixes that reduce reaction to bad sonar data
6) Safety improvements
    a) motors always spin when armed (speed can be controlled with MOT_SPIN_ARMED, set to 0 to disable)
    b) vehicle's maximum lean angle can be reduced (or increased) with the ANGLE_MAX parameter
    c) arming check that GPS hdop is > 2.0 (disable by setting GPS_HDOP parameter to 900)
    d) slow take-off in AUTO, LOITER, ALTHOLD to reduce chance of motor/esc burn-out on large copters
7) Bug fixes:
    a) Optical flow sensor initialisation fix
    b) altitude control fix for Loiter_turns mission command (i.e. mission version of CIRCLE mode)
    c) DO_SET_ROI fix (do not use "ROI")
8) Distribute Loiter & Navigation calcs over 4 cycles to reduce impact on a single 100hz loop
9) RCMAP_ parameters allow remapping input channels 1 ~ 4
------------------------------------------------------------------
ArduCopter 3.0.1-rc2 / 3.0.1 11-Jul-2013
Improvements over 3.0.1-rc1
1) Rate Roll, Pitch and Yaw I fix when we hit motor limits
2) pre-arm check changes:
    a) double flash arming light when pre-arm checks fail
    b) relax mag field checks to 35% min, 165% max of expected field
3) loiter and auto changes:
      a) reduced Loiter speed to 5 m/s
      b) reduced WP_ACCEL to 1 m/s/s (was 2.5 m/s/s)
      c) rounding error fix in loiter controller
      d) bug fix to stopping point calculation for RTL and loiter during missions
4) Stability Patch fix which was freezing Rate Taw I term and allowing uncommanded Yaw
------------------------------------------------------------------
ArduCopter 3.0.1-rc1 26-Jun-2013
Improvements over 3.0.0
1) bug fix to Fence checking position after GPS lock was lost
2) bug fix to LAND so that it does not attempt to maintain horizontal position without GPS lock
------------------------------------------------------------------
ArduCopter 3.0.0 / 3.0.0-rc6 16-Jun-2013
Improvements over 3.0.0-rc5
1) bug fix to Circle mode's start position (was moving to last loiter target)
2) WP_ACCEL parameter added to allow user to adjust acceleration during missions
3) loiter acceleration set to half of LOIT_SPEED parameter value (was hard-coded)
4) reduce AltHold P to 1.0 (was 2.0)
------------------------------------------------------------------
ArduCopter 3.0.0-rc5 04-Jun-2013
Improvements over 3.0.0-rc4
1) bug fix to LAND flight mode in which it could try to fly to mission's next waypoint location
2) bug fix to Circle mode to allow counter-clockwise rotation
3) bug fix to heading change in Loiter, RTL, Missions when pilot's throttle is zero
4) bug fix for mission sticking at take-off command when pilot's throttle is zero
5) bug fix for parameters not saving when new value is same as default value
6) reduce pre-arm board min voltage check to 4.3V (was 4.5V)
7) remove throttle controller's ability to limit lean angle in loiter, rtl, auto
------------------------------------------------------------------
ArduCopter 3.0.0-rc4 02-Jun-2013
Improvements over 3.0.0-rc3
1) loiter improvements:
     i) repositioning enhanced with feed forward
     ii) use tan to convert desired accel to lean angle
2) stability patch improvements for high powered copters or those with roll-pitch rate gains set too high
3) auto mode vertical speed fix (it was not reaching the desired speeds)
4) alt hold smoothed by filtering feed forward input
5) circle mode fix to initial position and smoother initialisation
6) RTL returns to initial yaw heading before descending
7) safe features:
    i) check for gps lock when entering failsafe
    ii) pre-arm check for mag field length
    iii) pre-arm check for board voltage between 4.5v ~ 5.8V
    iv) beep twice during arming
    v) GPS failsafe enabled by default (will LAND if loses GPS in Loiter, AUTO, Guided modes)
    vi) bug fix for alt-hold mode spinning motors before pilot has raised throttle
8) bug fixes:
    i) fixed position mode so it responding to pilot input
    ii) baro cli test
    iii) moved cli motor test to test sub menu and minor change to throttle output
    iv) guided mode yaw control fix
------------------------------------------------------------------
ArduCopter 3.0.0-rc3 22-May-2013
Improvements over 3.0.0-rc2
1) bug fix for dataflash erasing unnecessarily
2) smoother transition to waypoints, loiter:
    intermediate point's speed initialised from copter's current speed
3) Ch8 auxiliary function switch (same features as Ch7)
4) safety checks:
    Warning to GCS of reason for pre-arm check failure
    ARMING_CHECK parameter added to allow disabling pre-arm checks
    Added compass health and offset check to pre-arm check
    compassmot procedure displays interference as percentage of total mag field
5) WPNAV dataflash message combined into NTUN message
6) allow TriCopters to use ESC calibration
------------------------------------------------------------------
ArduCopter 3.0.0-rc2 13-May-2013
Improvements over 3.0.0-rc1:
1) smoother transition to waypoints, loiter:
    reduced loiter max acceleration to smooth waypoints
    bug fix to uninitialised roll/pitch when entering RTL, AUTO, LOITER
2) fast waypoints - copter does not stop at waypoints unless delay is specified
3) WPNAV_LOIT_SPEED added to allow faster/slower loiter repositioning
4) removed speed limits on auto missions
5) enhance LAND mission command takes lat/lon coordinates
6) bug fix for RTL not pointing home sometimes
7) centrifugal correction disabled when copter is disarmed to stop HUD moving
8) centrifugal correction disabled when sat count less than 6 (AHRS_GPS_MINSATS)
9) compass calibration reliability improvements when run from mission planner
10) bug fix to allow compassmot to be run from mission planner terminal screen
11) add support for H-quad frame
12) add COMPASS_ORIENT parameter to support external compass in any orientation
------------------------------------------------------------------
ArduCopter 3.0.0-rc1 01-May-2013
Improvements over 2.9.1b:
1) Inertial navigation for X & Y axis (Randy/Leonard/Jonathan)
2) 3D waypoint navigation library (Leonard/Randy)
    WPNAV_SPEED, WPNAV_SPEED_UP, WPNAV_SPEED_DN control target speeds during missions and RTL
    WP_YAW_BEHAVIOR to allow disabling yaw during missions and RTL
3) PX4 support (some features still not available) (Tridge/Pat/PX4Dev Team)
4) Safety improvements:
    Tin-can shaped fence (set FENCE_ENABLED to 1 and copter will RTL if alt > 150m or horizontal distance from home > 300m) (Randy/Tridge/Leonard)
    GCS failsafe (set FS_GCS_ENABLED to 1 and if you are using a tablet to fly your copter it will RTL and return control to the radio 3 seconds after losing telemetry) (Randy)
    pre-arm checks to ensure accelerometer and radio calibration has been performed before arming (Randy)
5) motor interference compensation for compass (Jonathan/Randy)
6) Circle mode improvements:
    set CIRCLE_RADIUS to zero to do panorama shots in circle mode (copter does not move in a circle but instead slowly rotates)
    CIRCLE_RATE parameter allows controlling direction and speed of rotation in CIRCLE mode and LOITER_TURNS (can also be adjusted in flight from CH6 knob)
7) SONAR_GAIN parameter add to allow reducing the response to objects sensed by sonar (Randy)
8) support for trapezoidal quads (aka V shaped or FPV quads) (Leonard/Craig)
9) performance improvements to dataflash logging (Tridge)
10) bug-fix to analog read which could cause bad sonar reads when using voltage or current monitor (Tridge)
11) bug-fix to motors going to minimum when throttle is low while switching into Loiter, AUTO, RTL, ALT_HOLD (Jason/Randy)
12) bug-fix for auto disarm sometimes disarming shortly after arming (Jason/SirAlex)
------------------------------------------------------------------
ArduCopter 2.9.1b 30-Feb-2013
Improvements over 2.9.1:
1) reduce INS_MPU6K_FILTER to 20hz
2) reduce InertialNav Z-axis time constant to 5 (was 7)
3) increased max InertialNav accel correction to 3 m/s (was 1m/s)
4) bug fix for alt_hold being passed as int16_t to get_throttle_althold_with_slew
5) bug fix for throttle after acro flip (was being kept at min throttle if pilot switched out of ACRO mode while inverted)
6) reduce yaw_rate P default to 0.20 (was 0.25)
------------------------------------------------------------------
ArduCopter 2.9.1 & 2.9.1-rc2 01-Feb-2013
Improvements over 2.9.1-rc1:
1) small corretion to use of THR_MID to scale lower end of manual throttle between THR_MIN and 500 instead of 0 and 500
2) bug fix for longitude scaling being incorrectly calculated using Next Waypoint instead of home which could lead to scaling being 1
3) ESC calibration change to set update rate to ESCs to 50hz to allow simonk ESC to be calibrated
------------------------------------------------------------------
ArduCopter 2.9.1-rc1 31-Jan-2013
Improvements over 2.9:
1) THR_MID parameter added to allow users to adjust the manual throttle so that vehicle hovers at about mid stick
2) bug fix for autotrim - roll axis was backwards
3) bug fix to set sonar_alt_health to zero when sonar is disabled
4) capture level roll and pitch trims as part of accel calibration
5) bug fix to ppm encoder false positives
------------------------------------------------------------------
ArduCopter 2.9 & 2.9-rc5 14-Jan-2013
Improvements over 2.9-rc4:
1) add constraint to loiter commanded roll and pitch angles
2) relax altitude requirement for take-off command to complete
------------------------------------------------------------------
ArduCopter 2.9-rc4 12-Jan-2013
Improvements over 2.9-rc3:
1) Smoother transition between manual and auto flight modes (Leonard)
2) bug fix for LAND not actually landing when initiated from failsafe (Randy/Craig)
------------------------------------------------------------------
ArduCopter 2.9-rc3 11-Jan-2013
Improvements over 2.9-rc2:
1) alt hold with sonar improvements - now on by default (Leonard/Randy)
2) performance and memory usage improvements (Tridge/Randy)
3) increase APM1 baro pressure read from 5hz to 8.3hz to improve alt hold (Randy)
4) bug fix: altitude error reported to GCS (Randy)
5) limit inertial nav's max accel offset correction to 100cm/s/s to speed up recovery after hard impacts (Randy)_
6) moved rate controllers to run after ins read (Tridge/Randy)
------------------------------------------------------------------
ArduCopter 2.9-rc2 31-Dec-2012
Improvements over 2.9-rc1:
1) increased throttle rate gains from 1.0 to 6.0
2) APM1 fix so it works with inertial nav (5hz update rate of baro was beyond the tolerance set in the inav library)
------------------------------------------------------------------
ArduCopter 2.9-rc1 23-Dec-2012
Improvements over 2.8.1:
1) altitude hold improvements:
    a)inertial navigation for vertical axis [Randy/Jonathan/Leonard/Jason]
    b)accel based throttle controller [Leonard/Randy]
    c)accelerometer calibration routine updated to use gauss-newton method [Randy/Tridge/Rolfe Schmidt]
    d)parameters to control climb rate:
        AUTO_VELZ_MIN, AUTO_VELZ_MAX - allows you to control the maximum climb and descent rates of the autopilot (in cm/s)
        PILOT_VELZ_MAX - allows you to control the maximum climb/descent rate when in alt hold or loiter (in cm/s)
2) landing improvements [Leonard/Randy]
    LAND_SPEED - allows you to set the landing speed in cm/s
3) camera related improvements:
    a) AP_Relay enabled for APM2 and integrated with AP_Camera [Sandro Benigno]
    b) camera trigger with channel 7 switch or DO_DIGICAM_CONTROL mission command [Randy]
    c) allow yaw override by pilot or with CONDITIONAL_YAW command during missions [Randy]
        YAW_OVR_BEHAVE - Controls when autopilot takes back normal control of yaw after pilot overrides (0=after next wp, 1=never)
4) trad heli improvements [Rob]
    a) code tested and brought back into the fold (2.8.1 was never released for trad helis)
    b) enabled rate controller (previously only used angle controllers)
    c) fix to rotor speed controllers - now operates by switching off channel 8
    d) allow wider collective pitch range in acro and alt hold modes vs stabilize mode
    e) removed angle boost function because it created more problems than it solved
    f) bug fix to allow collective pitch to use the entire range of servos
5) mediatek gps driver improvements [Craig]
    a) added support for 1.9 firmware
    b) bug fix to start-up routine so sbas can be enabled
6) failsafe improvements (both throttle and battery) [Randy/Craig/John Arne Birkeland]
    a) RTL will not trigger if your throttle is zero - reduces risk of accidentally invoking RTL if you switch off your receiver before disarming
    b) failsafe triggered in unlikely case of a PPM encoder failure
    c) bug fix to resolve motors momentarily reducing to zero after failsafe is triggered
7) mpu6k filtering made configurable and default changed to 42hz for copters [Leonard/Tridge]
8) support ppm sum for transmitters with as few as 5 channels [Randy/John Arne Birkeland]
9) acro trainer - copter will return to be generally upright if you release the sticks in acro mode [Leonard]
    ACRO_BAL_ROLL, ACRO_BAL_PITCH - controls rate at which roll returns to level
    ACRO_TRAINER - 1 to enable the auto-bring-upright feature
10) other changes and bug fixes:
    a) allow >45 degrees when in stabilize mode by adding lines like below to APM_Config (compile time option only) [Jason]
        #define MAX_INPUT_ROLL_ANGLE 6000     // 60 degrees
        #define MAX_INPUT_PITCH_ANGLE 6000    // 60 degrees
    b) bug fix to stop RTL from ever climbing to an unreasonable height (i.e. >80m) [Jason]
    c) event and state logging [Jason]
    d) allow cli to be used over telemetry link [Tridge]
    e) bug fix to allow compass accumulate to run when we have spare cpu cycles [Randy]
    f) bug fix so do_set_servo command works [Randy]
    g) bug fix to PID controller's so they don't calculate crazy D term on the first call [Tridge]
    h) bug fix to initialise navigation parameter to resolve twitch when entering some navigation modes [Jason]
    i) performance improvement to copter leds - use DigitalFastWrite and DigitalFastRead instead of native arduino functions  [Randy]
    j) removed unused stab_d from roll and pitch controller [Jason]
    k) bug fix for guided mode not reaching target altitude if it reaches horizontal target first [Randy]
    l) code clean-up, parameter documentation improvements [Randy/Jason/Rob/others]

------------------------------------------------------------------
ArduCopter 2.8.1 22-Oct-2012
Improvements over 2.8:
- 430 bytes of RAM freed up to resolve APM1 level issue and reduce chance of memory corruption on both APM1 and APM2

Improvements over 2.7.3:
- Improved ACRO mode (Leonard Hall)
- Improved stability patch to reduce "climb-on-yaw" problem (Leonard, Rob Lefebvre, Randy)
- Rate controller targets moved to body frames (yaw control now works properly when copter is inverted) (Leonard/Randy)
- Less bouncy Stabilize yaw control (Leonard)
- OpticalFlow sensor support for APM2.5 (Randy)
- DMP works again by adding "#define DMP_ENABLED ENABLED" to APM_Config.h You can also log DMP vs DCM to the dataflash by adding "#define SECONDARY_DMP_ENABLED ENABLED" (Randy)
- Watch dog added to shutdown motors if main loop feezes for 2 seconds (Randy)
- Thrust curve added to linearize pwm->thrust. Removes deadzone found above 90% throttle in most ESC/motors (Randy)
- More timing improvements (main loop is now tied to MPU6000s interrupt) (Randy)
- GPS NMEA bug fix (Alexey Kozin)
- Logging improvements (log I terms, dump all settings at head of dataflash log) (Jason)

Bug Fixes / Parameter changes:
- fixed skipping of last waypoint (Jason)
- resolved twitching when no GPS attached (Tridge)
- fixed loss of altitude if alt hold is engaged before first GPS lock (Randy/Jason)
- moved Roll-Pitch I terms from Stabilize controllers to Rate controllers
- TILT_COMPENSATION param tuned for TradHeli (Rob)

Code Cleanup:
- HAL changes for platform portability (Pat Hickey)
- Removed INSTANT_PWM (Randy)
------------------------------------------------------------------
