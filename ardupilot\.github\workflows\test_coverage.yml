name: test coverage

on:
  workflow_dispatch:
  schedule:
    - cron: '0 0 * * 0'  # every sunday at midnight
# paths:
# - "*"
# - "!README.md" <-- don't rebuild on doc change
concurrency:
  group: ci-${{github.workflow}}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  build:
    runs-on: ubuntu-22.04
    container:
      image: ardupilot/ardupilot-dev-${{ matrix.type }}:v0.1.3
      options: --privileged
    strategy:
      fail-fast: false  # don't cancel if a job from the matrix fails
      matrix:
        toolchain: [
            base,  # GCC
        ]
        config: [
            coverage,
            sitltest-can,
        ]
        type: [
          coverage,
        ]
        include:
          - config: sitltest-can
            type: periph
        exclude:
          - config: sitltest-can
            type: coverage
    steps:
      # git checkout the PR
      - uses: actions/checkout@v4
        with:
          submodules: 'recursive'
      # Put ccache into github cache for faster build
      - name: Prepare ccache timestamp
        id: ccache_cache_timestamp
        run: |
          NOW=$(date -u +"%F-%T")
          echo "timestamp=${NOW}" >> $GITHUB_OUTPUT
      - name: ccache cache files
        uses: actions/cache@v4
        with:
          path: ~/.ccache
          key: ${{github.workflow}}-ccache-${{ matrix.toolchain }}-${{steps.ccache_cache_timestamp.outputs.timestamp}}
          restore-keys: ${{github.workflow}}-ccache-${{ matrix.toolchain }}-  # restore ccache from either previous build on this branch or on master
      - name: setup ccache
        run: |
          . .github/workflows/ccache.env
      - name: test ${{matrix.config}} ${{ matrix.toolchain }}
        env:
          CI_BUILD_TARGET: ${{matrix.config}}
          TERM: xterm
        shell: 'script -q -e -c "bash {0}"'
        run: |
          git config --global --add safe.directory ${GITHUB_WORKSPACE}
          PATH="/github/home/<USER>/bin:$PATH"
          python3 -m pip install --progress-bar off --user mavproxy
          python3 -m pip uninstall -y pymavlink
          git submodule update --init --recursive --depth=1
          (cd modules/mavlink/pymavlink && DISABLE_MAVNATIVE=True MDEF="$PWD/../message_definitions" python3 -m pip install --progress-bar off --user .)
          if [[ ${{ matrix.config }} == "coverage" ]]; then
            Tools/scripts/run_coverage.py -f
          else
            Tools/scripts/run_coverage.py -i
            ./waf configure --board sitl_periph_universal --debug --coverage
            ./waf build --target bin/AP_Periph
            Tools/scripts/run_coverage.py -i
            Tools/autotest/autotest.py test.CAN --debug --coverage
            Tools/scripts/run_coverage.py -u
          fi

      - name: Coveralls
        uses: coverallsapp/github-action@master
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          path-to-lcov: ./reports/lcov-report/lcov.info
          flag-name: run-${{matrix.config}}
          parallel: true


  finish:
    if: always()
    needs: build
    runs-on: ubuntu-22.04
    steps:
      - name: Coveralls Finished
        uses: coverallsapp/github-action@master
        with:
          github-token: ${{ secrets.github_token }}
          parallel-finished: true
